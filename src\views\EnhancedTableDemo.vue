<template>
  <div class="enhanced-table-demo">
    <h1>增强表格组件演示</h1>
    
    <div class="demo-section">
      <h2>功能特性</h2>
      <div class="features-grid">
        <div class="feature-card">
          <h3>🔍 增强筛选</h3>
          <ul>
            <li>多种筛选模式（包含、等于、开头匹配、结尾匹配、正则表达式）</li>
            <li>数值范围筛选</li>
            <li>日期范围筛选</li>
            <li>筛选历史记录</li>
          </ul>
        </div>
        <div class="feature-card">
          <h3>📊 优化滚动</h3>
          <ul>
            <li>表头固定滚动</li>
            <li>虚拟滚动支持</li>
            <li>滚动同步</li>
            <li>滚动指示器</li>
          </ul>
        </div>
        <div class="feature-card">
          <h3>🎨 用户体验</h3>
          <ul>
            <li>列宽拖拽调整</li>
            <li>智能排序</li>
            <li>行悬停效果</li>
            <li>快捷键支持</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>基础演示</h2>
      <div class="demo-controls">
        <button @click="generateData" class="demo-btn">生成测试数据</button>
        <button @click="addRandomRow" class="demo-btn">添加随机行</button>
        <button @click="clearData" class="demo-btn">清空数据</button>
        <span class="data-info">当前数据: {{ tableData.length - 1 }} 行</span>
      </div>
      
      <BasicTableComponent
        ref="tableRef"
        :data="tableData"
        :width="1000"
        :height="500"
        :show-filter="true"
        :enable-virtual-scroll="true"
        :row-height="32"
        :frozen-columns="1"
      />
    </div>

    <div class="demo-section">
      <h2>大数据演示</h2>
      <div class="demo-controls">
        <button @click="generateLargeData" class="demo-btn">生成大量数据 (1000行)</button>
        <span class="data-info">测试虚拟滚动性能</span>
      </div>
      
      <BasicTableComponent
        ref="largeTableRef"
        :data="largeTableData"
        :width="1000"
        :height="400"
        :show-filter="true"
        :enable-virtual-scroll="true"
        :row-height="32"
        :page-size="50"
      />
    </div>

    <div class="demo-section">
      <h2>API 演示</h2>
      <div class="api-controls">
        <button @click="getFilterState" class="demo-btn">获取筛选状态</button>
        <button @click="setFilterState" class="demo-btn">设置筛选状态</button>
        <button @click="resetAllFilters" class="demo-btn">重置所有筛选</button>
      </div>
      <pre v-if="filterState" class="filter-state-display">{{ JSON.stringify(filterState, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import BasicTableComponent from '@/components/BasicTableComponent.vue'

// 响应式数据
const tableRef = ref(null)
const largeTableRef = ref(null)
const tableData = ref([])
const largeTableData = ref([])
const filterState = ref(null)

// 生成测试数据
const generateData = () => {
  const headers = ['ID', '姓名', '年龄', '城市', '薪资', '入职日期', '部门', '邮箱']
  const cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉']
  const departments = ['技术部', '产品部', '运营部', '市场部', '人事部', '财务部']
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  
  const data = [headers]
  
  for (let i = 1; i <= 50; i++) {
    const name = names[Math.floor(Math.random() * names.length)]
    const age = Math.floor(Math.random() * 40) + 22
    const city = cities[Math.floor(Math.random() * cities.length)]
    const salary = Math.floor(Math.random() * 20000) + 5000
    const department = departments[Math.floor(Math.random() * departments.length)]
    const joinDate = new Date(2020 + Math.floor(Math.random() * 4), 
                             Math.floor(Math.random() * 12), 
                             Math.floor(Math.random() * 28) + 1)
                             .toISOString().split('T')[0]
    
    data.push([
      i,
      name + i,
      age,
      city,
      `¥${salary.toLocaleString()}`,
      joinDate,
      department,
      `${name.toLowerCase()}${i}@company.com`
    ])
  }
  
  tableData.value = data
}

// 生成大量数据
const generateLargeData = () => {
  const headers = ['ID', '产品名称', '价格', '库存', '分类', '创建日期', '状态']
  const categories = ['电子产品', '服装', '食品', '图书', '家居', '运动', '美妆', '汽车']
  const statuses = ['在售', '缺货', '下架', '预售']
  const productNames = ['产品A', '产品B', '产品C', '产品D', '产品E']
  
  const data = [headers]
  
  for (let i = 1; i <= 1000; i++) {
    const productName = productNames[Math.floor(Math.random() * productNames.length)]
    const price = (Math.random() * 1000 + 10).toFixed(2)
    const stock = Math.floor(Math.random() * 1000)
    const category = categories[Math.floor(Math.random() * categories.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const createDate = new Date(2023, 
                               Math.floor(Math.random() * 12), 
                               Math.floor(Math.random() * 28) + 1)
                               .toISOString().split('T')[0]
    
    data.push([
      i,
      `${productName}-${i}`,
      `¥${price}`,
      stock,
      category,
      createDate,
      status
    ])
  }
  
  largeTableData.value = data
}

// 添加随机行
const addRandomRow = () => {
  if (tableData.value.length === 0) {
    generateData()
    return
  }
  
  const cities = ['北京', '上海', '广州', '深圳', '杭州']
  const departments = ['技术部', '产品部', '运营部', '市场部']
  const names = ['新员工A', '新员工B', '新员工C']
  
  const newId = tableData.value.length
  const name = names[Math.floor(Math.random() * names.length)]
  const age = Math.floor(Math.random() * 40) + 22
  const city = cities[Math.floor(Math.random() * cities.length)]
  const salary = Math.floor(Math.random() * 20000) + 5000
  const department = departments[Math.floor(Math.random() * departments.length)]
  const joinDate = new Date().toISOString().split('T')[0]
  
  tableData.value.push([
    newId,
    name + newId,
    age,
    city,
    `¥${salary.toLocaleString()}`,
    joinDate,
    department,
    `${name.toLowerCase()}${newId}@company.com`
  ])
}

// 清空数据
const clearData = () => {
  tableData.value = []
  largeTableData.value = []
}

// API 演示方法
const getFilterState = () => {
  if (tableRef.value) {
    filterState.value = tableRef.value.getFilterState()
  }
}

const setFilterState = () => {
  if (tableRef.value) {
    tableRef.value.setFilterState({
      column: '1',
      text: '张',
      mode: 'contains'
    })
  }
}

const resetAllFilters = () => {
  if (tableRef.value) {
    tableRef.value.resetFilter()
  }
  if (largeTableRef.value) {
    largeTableRef.value.resetFilter()
  }
}

// 组件挂载时生成初始数据
onMounted(() => {
  generateData()
})
</script>

<style scoped>
.enhanced-table-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.feature-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.feature-card h3 {
  margin: 0 0 12px 0;
  color: #495057;
}

.feature-card ul {
  margin: 0;
  padding-left: 20px;
}

.feature-card li {
  margin-bottom: 4px;
  font-size: 14px;
  color: #6c757d;
}

.demo-controls,
.api-controls {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.demo-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.demo-btn:hover {
  background: #0056b3;
}

.data-info {
  color: #6c757d;
  font-size: 14px;
}

.filter-state-display {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 12px;
  overflow-x: auto;
}

h1 {
  color: #212529;
  margin-bottom: 30px;
}

h2 {
  color: #495057;
  margin-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 8px;
}
</style>
