<template>
  <div class="bank-code-query">
    <!-- 标题 -->
    <h1 class="title">联行号查询系统</h1>
    
    <!-- 认证信息 -->
    <div class="auth-section">
      <el-form :inline="true" :model="authForm" class="auth-form">
        <el-form-item label="AppCode">
          <el-input
            v-model="authForm.appcode"
            placeholder="请输入阿里云API的AppCode"
            clearable
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item>
          <el-button 
            type="primary" 
            :loading="loading"
            :disabled="!isFormValid"
            @click="handleQuery"
          >
            执行查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <div class="table-actions" v-if="tableData.some(row => row.bankCode)">
        <el-button 
          type="success" 
          @click="exportResults"
          :icon="Download"
          plain
        >
          导出结果
        </el-button>
        <el-button 
          type="primary" 
          @click="copyAllResults"
          :icon="CopyDocument"
          plain
        >
          复制全部结果
        </el-button>
      </div>
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        @paste="handlePaste"
        @cell-click="handleCellClick"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column 
          label="银行卡号" 
          prop="cardNumber"
          min-width="300"
        >
          <template #default="{ row, $index }">
            <el-input
              v-model="row.cardNumber"
              placeholder="请粘贴或输入银行卡号"
              @change="clearBankCode($index)"
              clearable
            />
          </template>
        </el-table-column>
        
        <el-table-column 
          label="联行号" 
          prop="bankCode"
          min-width="200"
        >
          <template #default="{ row }">
            <div class="bank-code-cell">
              <span :class="{ 'no-data': !row.bankCode, 'error': row.error }">
                {{ row.bankCode || '未查询' }}
              </span>
              <el-button
                v-if="row.bankCode"
                type="primary"
                link
                @click.stop="copyText(row.bankCode)"
                class="copy-btn"
              >
                复制
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column 
          label="银行名称" 
          prop="bankName"
          min-width="200"
        >
          <template #default="{ row }">
            <div class="bank-name-cell">
              <span :class="{ 'no-data': !row.bankName, 'error': row.error }">
                {{ row.bankName || '未查询' }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column 
          label="操作" 
          width="120"
          align="center"
        >
          <template #default="{ $index }">
            <el-button 
              type="danger" 
              link 
              @click="removeRow($index)"
              :disabled="tableData.length <= 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="table-footer">
        <el-button 
          type="primary" 
          plain 
          @click="addRow"
          :icon="Plus"
        >
          添加行
        </el-button>
        <div class="hint">提示：可直接在表格中粘贴多行银行卡号</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Download, CopyDocument } from '@element-plus/icons-vue';

// 认证表单
const authForm = ref({
  appcode: ''
});

// 表格数据
const tableData = ref([{ cardNumber: '', bankCode: '', bankName: '', error: false }]);
const loading = ref(false);
const selectedCell = ref(null);

// 表单验证
const isFormValid = computed(() => {
  return authForm.value.appcode && 
         tableData.value.some(row => row.cardNumber.trim());
});

// 添加新行
const addRow = () => {
  tableData.value.push({ cardNumber: '', bankCode: '', bankName: '', error: false });
};

// 删除行
const removeRow = (index) => {
  if (tableData.value.length > 1) {
    tableData.value.splice(index, 1);
  } else {
    ElMessage.warning('至少保留一行数据');
  }
};

// 清空联行号
const clearBankCode = (index) => {
  tableData.value[index].bankCode = '';
  tableData.value[index].bankName = '';
  tableData.value[index].error = false;
};

// 处理粘贴事件
const handlePaste = (event) => {
  if (!selectedCell.value) return;
  
  event.preventDefault();
  const clipboardData = event.clipboardData || window.clipboardData;
  const pastedText = clipboardData.getData('text');
  
  if (!pastedText) return;
  
  const rows = pastedText.split(/\r\n|\n|\r/).filter(row => row.trim());
  if (rows.length === 0) return;
  
  const { rowIndex } = selectedCell.value;
  
  rows.forEach((row, idx) => {
    const currentIndex = rowIndex + idx;
    if (currentIndex < tableData.value.length) {
      tableData.value[currentIndex].cardNumber = row.trim();
      tableData.value[currentIndex].bankCode = '';
    } else {
      tableData.value.push({ 
        cardNumber: row.trim(), 
        bankCode: '' 
      });
    }
  });};

// 处理单元格点击
const handleCellClick = (row, column, cell, event) => {
  const rowIndex = tableData.value.findIndex(r => r === row);
  selectedCell.value = { rowIndex, column };
};

// 复制文本
const copyText = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success('已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
    ElMessage.error('复制失败，请重试');
  }
};

// 执行查询
const handleQuery = async () => {
  if (!isFormValid.value || loading.value) return;
  
  loading.value = true;
  
  try {
    // 获取所有有效的银行卡号
    const cardNumbers = tableData.value
      .map(row => row.cardNumber.trim())
      .filter(num => num);
      
    if (cardNumbers.length === 0) {
      ElMessage.warning('请至少输入一个银行卡号');
      loading.value = false;
      return;
    }
    
    // 设置API请求参数
    const host = 'https://hjcnaps.market.alicloudapi.com';
    const path = '/bank_union_card/query';
    const url = host + path;
    const appcode = authForm.value.appcode;
    
    if (!appcode) {
      ElMessage.error('请输入有效的AppCode');
      loading.value = false;
      return;
    }
    
    console.log('开始查询银行卡信息，卡号数量:', cardNumbers.length);
    
    // 为每个卡号发起请求
    const promises = cardNumbers.map(async (cardNumber) => {
      try {
        console.log(`开始查询卡号: ${cardNumber}`);
        
        // 发送请求
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Authorization': 'APPCODE ' + appcode,
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
          },
          body: new URLSearchParams({ 'bankcard': cardNumber })
        });
        
        // 记录原始响应
        const responseText = await response.text();
        console.log(`卡号 ${cardNumber} 响应:`, responseText);
        
        if (!response.ok) {
          throw new Error(`API错误: ${response.status}`);
        }
        
        // 解析JSON
        let data;
        try {
          data = JSON.parse(responseText);
        } catch (e) {
          console.error('JSON解析错误:', e);
          throw new Error('API返回的数据格式不正确');
        }
        
        console.log(`卡号 ${cardNumber} 解析后数据:`, data);
        
        // 检查API返回的数据结构 - 适应新的响应格式
        if (!data || data.code !== 200) {
          const errorMsg = data?.msg || '未知错误';
          console.error(`API返回错误: ${errorMsg}`);
          throw new Error(errorMsg);
        }
        
        // 返回处理后的结果 - 适应新的响应格式
        return {
          cardNumber: cardNumber,
          bankCode: data.data?.cnaps || '',
          bankName: data.data?.name || data.data?.bank || '',
          success: true,
          rawData: data
        };
      } catch (err) {
        console.error(`查询卡号 ${cardNumber} 失败:`, err);
        return {
          cardNumber: cardNumber,
          bankCode: '',
          bankName: '',
          errorMsg: '查询失败: ' + (err.message || '未知错误'),
          success: false
        };
      }
    });
    
    // 等待所有请求完成
    const results = await Promise.all(promises);
    console.log('所有查询结果:', results);
    
    // 更新表格数据
    let successCount = 0;
    results.forEach(result => {
      const row = tableData.value.find(row => row.cardNumber.trim() === result.cardNumber);
      if (row) {
        if (result.success) {
          row.bankCode = result.bankCode;
          row.bankName = result.bankName;
          row.error = false;
          successCount++;
        } else {
          row.bankCode = '';
          row.bankName = '';
          row.error = true;
          // 在控制台显示错误信息
          console.error(`卡号 ${result.cardNumber} 查询失败: ${result.errorMsg}`);
        }
      }
    });
    
    if (successCount > 0) {
      ElMessage.success(`查询成功: ${successCount}/${cardNumbers.length} 条记录`);
    } else {
      ElMessage.warning('所有查询均失败，请检查AppCode和网络连接');
    }
  } catch (error) {
    console.error('查询失败:', error);
    ElMessage.error('查询失败：' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};
// 导出结果
const exportResults = () => {
  const validResults = tableData.value.filter(row => row.bankCode);
  
  if (validResults.length === 0) {
    ElMessage.warning('没有可导出的结果');
    return;
  }
  
  // 创建CSV内容
  const headers = ['银行卡号', '联行号', '银行名称'];
  const csvContent = [
    headers.join(','),
    ...validResults.map(row => [
      row.cardNumber,
      row.bankCode,
      row.bankName
    ].join(','))
  ].join('\n');
  
  // 添加BOM头，解决中文乱码问题
  const BOM = new Uint8Array([0xEF, 0xBB, 0xBF]);
  const blob = new Blob([BOM, csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `联行号查询结果_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  ElMessage.success('导出成功');
};

// 复制全部结果
const copyAllResults = () => {
  const validResults = tableData.value.filter(row => row.bankCode);
  
  if (validResults.length === 0) {
    ElMessage.warning('没有可复制的结果');
    return;
  }
  
  // 创建文本内容
  const textContent = validResults.map(row => 
    `${row.cardNumber}\t${row.bankCode}\t${row.bankName || ''}`
  ).join('\n');
  
  // 复制到剪贴板
  navigator.clipboard.writeText(textContent)
    .then(() => {
      ElMessage.success(`已复制 ${validResults.length} 条结果到剪贴板`);
    })
    .catch(err => {
      console.error('复制失败:', err);
      ElMessage.error('复制失败，请重试');
    });
};
</script>

<style scoped lang="scss">
.bank-code-query {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 24px 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .auth-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 24px;
    
    .auth-form {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: flex-start;
    }
    
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }
  
  .table-container {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    
    .bank-code-cell {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 8px;
      
      .no-data {
        color: #909399;
        font-style: italic;
      }
      
      .error {
        color: #F56C6C;
      }
      
      .copy-btn {
        padding: 0;
        margin-left: 8px;
      }
    }
    
    .table-actions {
      margin-bottom: 16px;
      display: flex;
      gap: 12px;
    }
    
    .table-footer {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .hint {
        color: #909399;
        font-size: 13px;
      }
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .bank-code-query {
    padding: 12px;
    
    .auth-section {
      padding: 16px;
      
      .auth-form {
        flex-direction: column;
        gap: 12px;
        
        :deep(.el-form-item) {
          width: 100%;
          margin-right: 0;
          
          .el-input {
            width: 100% !important;
          }
        }
      }
    }
    
    .table-container {
      padding: 12px;
      
      .table-footer {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
      }
    }
  }
}
</style>