<template>
  <BaseQueryComponent
    title="保证金台账"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/guarantee-ledger'

// 查询字段配置
const queryFields = [
  {
    key: 'serialNumber',
    label: '序号',
    type: 'number',
    placeholder: '请输入序号',
    width: '120px'
  },
  {
    key: 'organization',
    label: '组织机构',
    type: 'text',
    placeholder: '请输入组织机构编码',
    width: '150px'
  },
  {
    key: 'organizationName',
    label: '组织机构名称',
    type: 'text',
    placeholder: '请输入组织机构名称',
    width: '200px'
  },
  {
    key: 'projectCode',
    label: '项目编号',
    type: 'text',
    placeholder: '请输入项目编号',
    width: '150px'
  },
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    placeholder: '请输入项目名称',
    width: '200px'
  },
  {
    key: 'currency',
    label: '币种',
    type: 'select',
    placeholder: '请选择币种',
    width: '120px',
    options: [
      { label: 'CNY', value: 'CNY' },
      { label: 'USD', value: 'USD' },
      { label: 'EUR', value: 'EUR' },
      { label: 'JPY', value: 'JPY' }
    ]
  },
  {
    key: 'deadline',
    label: '截止日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'guaranteeType',
    label: '保证金（押金）类型',
    type: 'select',
    placeholder: '请选择保证金类型',
    width: '180px',
    options: [
      { label: '履约保证金', value: '履约保证金' },
      { label: '投标保证金', value: '投标保证金' },
      { label: '质量保证金', value: '质量保证金' },
      { label: '农民工工资保证金', value: '农民工工资保证金' }
    ]
  },
  {
    key: 'receivingParty',
    label: '收取方单位',
    type: 'text',
    placeholder: '请输入收取方单位',
    width: '200px'
  },
  {
    key: 'receivingPartyGroup',
    label: '收取方所在集团',
    type: 'text',
    placeholder: '请输入收取方所在集团',
    width: '200px'
  },
  {
    key: 'actualPaidAmount',
    label: '实缴金额',
    type: 'amount-range'
  },
  {
    key: 'actualRecoveredAmount',
    label: '实际回收金额',
    type: 'amount-range'
  },
  {
    key: 'remainingAmount',
    label: '剩余金额',
    type: 'amount-range'
  },
  {
    key: 'responsiblePerson',
    label: '负责人',
    type: 'text',
    placeholder: '请输入负责人姓名',
    width: '150px'
  }
]
</script>
