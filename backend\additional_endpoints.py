from fastapi import HTTPException
from typing import Dict, Any, List
import duckdb
from datetime import datetime
from main import QueryRequest, QueryResponse, build_where_clause, DB_PATH

# 内行查询API
async def query_internal_bank(request: QueryRequest):
    """查询内行查询"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        # 创建内行查询表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS internal_bank_query (
                fiscal_year VARCHAR,
                posting_date DATE,
                input_date DATE,
                voucher_number VARCHAR,
                profit_center VARCHAR,
                profit_center_desc VARCHAR,
                general_ledger_account_long_text VARCHAR,
                internal_bank_amount DOUBLE,
                internal_bank_customer VARCHAR,
                internal_bank_customer_desc VARCHAR,
                internal_bank_customer_quantity VARCHAR,
                supplier VARCHAR,
                supplier_desc VARCHAR,
                platform_document_number VARCHAR,
                reason VARCHAR
            )
        """)
        
        where_clause = build_where_clause(request.filters, "internal_bank_query")
        query = f"""
            SELECT * FROM internal_bank_query 
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 内部对账API
async def query_internal_reconciliation(request: QueryRequest):
    """查询内部对账"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        # 创建内部对账表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS internal_reconciliation (
                customer_code VARCHAR,
                customer_name VARCHAR,
                posting_date DATE,
                voucher_number VARCHAR,
                reason VARCHAR,
                settlement_amount DOUBLE,
                payment_amount DOUBLE,
                estimated_amount DOUBLE,
                profit_center VARCHAR
            )
        """)
        
        where_clause = build_where_clause(request.filters, "internal_reconciliation")
        query = f"""
            SELECT * FROM internal_reconciliation 
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 分供结算台账API
async def query_subcontractor_settlement(request: QueryRequest):
    """查询分供结算台账"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        # 创建分供结算台账表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS subcontractor_settlement (
                fiscal_year VARCHAR,
                posting_date DATE,
                input_date DATE,
                supplier_type VARCHAR,
                voucher_number VARCHAR,
                profit_center VARCHAR,
                profit_center_desc VARCHAR,
                supplier VARCHAR,
                supplier_desc VARCHAR,
                contract VARCHAR,
                contract_desc VARCHAR,
                text VARCHAR,
                platform_document_number VARCHAR,
                tax_inclusive_settlement_amount DOUBLE,
                input_tax DOUBLE
            )
        """)
        
        where_clause = build_where_clause(request.filters, "subcontractor_settlement")
        query = f"""
            SELECT * FROM subcontractor_settlement 
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 外部确权台账API
async def query_external_confirmation(request: QueryRequest):
    """查询外部确权台账"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        # 创建外部确权台账表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS external_confirmation (
                fiscal_year VARCHAR,
                posting_date DATE,
                input_date DATE,
                voucher_number VARCHAR,
                profit_center VARCHAR,
                profit_center_desc VARCHAR,
                customer VARCHAR,
                customer_desc VARCHAR,
                contract VARCHAR,
                contract_desc VARCHAR,
                text VARCHAR,
                platform_document_number VARCHAR,
                tax_inclusive_settlement_amount DOUBLE,
                output_tax DOUBLE
            )
        """)
        
        where_clause = build_where_clause(request.filters, "external_confirmation")
        query = f"""
            SELECT * FROM external_confirmation 
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 应付汇总按供应商API
async def query_payable_by_supplier(request: QueryRequest):
    """查询应付汇总按供应商"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        # 创建应付汇总按供应商表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS payable_by_supplier (
                profit_center_name VARCHAR,
                profit_center VARCHAR,
                supplier VARCHAR,
                supplier_desc VARCHAR,
                business_type VARCHAR,
                cumulative_settlement DOUBLE,
                cumulative_payment DOUBLE,
                guarantee_balance DOUBLE,
                input_tax_balance DOUBLE,
                tax_rate VARCHAR,
                settlement_amount DOUBLE,
                paid_amount DOUBLE,
                invoice_amount DOUBLE
            )
        """)
        
        where_clause = build_where_clause(request.filters, "payable_by_supplier")
        query = f"""
            SELECT * FROM payable_by_supplier 
            WHERE {where_clause}
            ORDER BY cumulative_settlement DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 成本表API
async def query_cost_ledger(request: QueryRequest):
    """查询成本表"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建成本表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS cost_ledger (
                fiscal_year VARCHAR,
                posting_date DATE,
                input_date DATE,
                voucher_number VARCHAR,
                platform_document_number VARCHAR,
                profit_center VARCHAR,
                profit_center_desc VARCHAR,
                cost_account VARCHAR,
                supplier VARCHAR,
                supplier_desc VARCHAR,
                text VARCHAR,
                accounting_cost DOUBLE,
                account_classification VARCHAR,
                cost_category VARCHAR
            )
        """)

        where_clause = build_where_clause(request.filters, "cost_ledger")
        query = f"""
            SELECT * FROM cost_ledger
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 收款台账API
async def query_receipt_ledger(request: QueryRequest):
    """查询收款台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建收款台账表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS receipt_ledger (
                fiscal_year VARCHAR,
                posting_date DATE,
                input_date DATE,
                voucher_number VARCHAR,
                profit_center VARCHAR,
                profit_center_desc VARCHAR,
                customer VARCHAR,
                customer_desc VARCHAR,
                contract VARCHAR,
                contract_desc VARCHAR,
                text VARCHAR,
                platform_document_number VARCHAR,
                total_receipt_amount DOUBLE,
                this_profit_center DOUBLE,
                internal_bank_or_deposit DOUBLE,
                internal_transaction_other_receipts DOUBLE,
                internal_bank_customer VARCHAR
            )
        """)

        where_clause = build_where_clause(request.filters, "receipt_ledger")
        query = f"""
            SELECT * FROM receipt_ledger
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 资金整理API
async def query_fund_management(request: QueryRequest):
    """查询资金整理"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建资金整理表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS fund_management (
                fiscal_year VARCHAR,
                posting_date DATE,
                profit_center VARCHAR,
                profit_center_desc VARCHAR,
                voucher_number VARCHAR,
                platform_document_number2 VARCHAR,
                general_ledger_account VARCHAR,
                supplier_desc VARCHAR,
                reason VARCHAR,
                internal_bank_amount DOUBLE,
                internal_external_flow VARCHAR,
                document_classification VARCHAR,
                internal_bank_customer_mark VARCHAR,
                internal_bank_customer_mark2 VARCHAR
            )
        """)

        where_clause = build_where_clause(request.filters, "fund_management")
        query = f"""
            SELECT * FROM fund_management
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
