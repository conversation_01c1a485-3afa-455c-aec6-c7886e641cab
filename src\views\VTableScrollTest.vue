<template>
  <div class="scroll-test-view">
    <h1>VTable 滚动条测试</h1>
    
    <div class="test-info">
      <p>这个页面专门测试VTable的水平滚动条问题</p>
      <p>表格宽度: 600px，列总宽度应该超过600px，需要显示水平滚动条</p>
    </div>

    <div class="test-container">
      <h2>测试1: 基础配置</h2>
      <div 
        ref="table1Ref" 
        style="width: 600px; height: 300px; border: 1px solid #ccc;"
      ></div>
    </div>

    <div class="test-container">
      <h2>测试2: 强制显示滚动条</h2>
      <div 
        ref="table2Ref" 
        style="width: 600px; height: 300px; border: 1px solid #ccc; overflow: auto;"
      ></div>
    </div>

    <div class="debug-info">
      <h3>调试信息:</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as VTable from '@visactor/vtable'

const table1Ref = ref()
const table2Ref = ref()
const debugInfo = ref({})

// 测试数据 - 很多列，确保需要水平滚动
const testData = [
  ['列1', '列2', '列3', '列4', '列5', '列6', '列7', '列8', '列9', '列10', '列11', '列12'],
  ['数据1-1', '数据1-2', '数据1-3', '数据1-4', '数据1-5', '数据1-6', '数据1-7', '数据1-8', '数据1-9', '数据1-10', '数据1-11', '数据1-12'],
  ['数据2-1', '数据2-2', '数据2-3', '数据2-4', '数据2-5', '数据2-6', '数据2-7', '数据2-8', '数据2-9', '数据2-10', '数据2-11', '数据2-12'],
  ['很长的数据内容测试', '短数据', '中等长度的数据内容', '数据3-4', '数据3-5', '数据3-6', '数据3-7', '数据3-8', '数据3-9', '数据3-10', '数据3-11', '数据3-12']
]

function createTable1() {
  const headers = testData[0]
  const dataRows = testData.slice(1)
  
  const columns = headers.map((title, index) => ({
    field: index.toString(),
    title: title,
    width: 120, // 固定宽度，确保总宽度超过容器
    cellType: 'text'
  }))

  const records = dataRows.map(row => {
    const record = {}
    row.forEach((cell, index) => {
      record[index.toString()] = cell
    })
    return record
  })

  const options = {
    columns,
    records,
    widthMode: 'standard',
    heightMode: 'standard',
    autoFillWidth: false,
    theme: VTable.themes.BRIGHT,
    scrollStyle: {
      scrollRailColor: '#f1f1f1',
      scrollSliderColor: '#409EFF',
      scrollSliderHoverColor: '#66b1ff',
      width: 8,
      height: 8
    }
  }

  console.log('表格1配置:', options)
  console.log('列总宽度:', columns.length * 120)
  
  const table = new VTable.ListTable(table1Ref.value, options)
  
  table.on('ready', () => {
    console.log('表格1渲染完成')
    debugInfo.value.table1 = {
      containerWidth: 600,
      totalColumnWidth: columns.length * 120,
      needsScroll: columns.length * 120 > 600,
      tableWidth: table.tableNoFrameWidth,
      tableHeight: table.tableNoFrameHeight
    }
  })
}

function createTable2() {
  const headers = testData[0]
  const dataRows = testData.slice(1)
  
  const columns = headers.map((title, index) => ({
    field: index.toString(),
    title: title,
    width: 150, // 更宽的列
    cellType: 'text'
  }))

  const records = dataRows.map(row => {
    const record = {}
    row.forEach((cell, index) => {
      record[index.toString()] = cell
    })
    return record
  })

  const options = {
    columns,
    records,
    widthMode: 'standard',
    heightMode: 'standard',
    autoFillWidth: false,
    theme: VTable.themes.BRIGHT
  }

  console.log('表格2配置:', options)
  console.log('列总宽度:', columns.length * 150)
  
  const table = new VTable.ListTable(table2Ref.value, options)
  
  table.on('ready', () => {
    console.log('表格2渲染完成')
    debugInfo.value.table2 = {
      containerWidth: 600,
      totalColumnWidth: columns.length * 150,
      needsScroll: columns.length * 150 > 600,
      tableWidth: table.tableNoFrameWidth,
      tableHeight: table.tableNoFrameHeight
    }
  })
}

onMounted(() => {
  setTimeout(() => {
    createTable1()
    createTable2()
  }, 100)
})
</script>

<style scoped>
.scroll-test-view {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-info {
  background: #f0f9ff;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #409EFF;
}

.test-container {
  margin-bottom: 40px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.test-container h2 {
  margin-bottom: 16px;
  color: #2c3e50;
}

.debug-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-top: 20px;
}

.debug-info pre {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e1e8f0;
  overflow-x: auto;
}
</style>
