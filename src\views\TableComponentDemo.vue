<template>
  <div class="demo-view">
    <h1>VTable组件使用示例</h1>
    
    <div class="demo-section">
      <h2>基础用法</h2>
      <p>传入二维数组，第一行为标题，自动生成表格：</p>
      <VTableComponent
        :data="basicData"
        :width="500"
        :height="250"
        :show-filter="true"
      />
    </div>

    <div class="demo-section">
      <h2>动态数据</h2>
      <button @click="addRow" class="demo-btn">添加行</button>
      <button @click="removeRow" class="demo-btn">删除行</button>
      <VTableComponent
        :data="dynamicData"
        :width="600"
        :height="300"
        :show-filter="true"
        ref="dynamicTableRef"
      />
    </div>

    <div class="demo-section">
      <h2>无筛选功能</h2>
      <VTableComponent
        :data="simpleData"
        :width="400"
        :height="200"
        :show-filter="false"
      />
    </div>

    <div class="demo-section">
      <h2>使用说明</h2>
      <div class="usage-info">
        <h3>Props参数：</h3>
        <ul>
          <li><code>data</code>: 二维数组，第一行为标题</li>
          <li><code>width</code>: 表格宽度（默认600px）</li>
          <li><code>height</code>: 表格高度（默认300px）</li>
          <li><code>showFilter</code>: 是否显示筛选面板（默认true）</li>
          <li><code>tableOptions</code>: 额外的表格配置</li>
        </ul>
        
        <h3>功能特性：</h3>
        <ul>
          <li>✅ 自动从二维数组生成表格</li>
          <li>✅ 内置筛选功能（按列或全局筛选）</li>
          <li>✅ 支持复制粘贴（Ctrl+C / Ctrl+V）</li>
          <li>✅ 可编辑单元格（双击编辑）</li>
          <li>✅ 响应式数据更新</li>
        </ul>

        <h3>使用示例：</h3>
        <pre><code>&lt;VTableComponent
  :data="[
    ['姓名', '年龄', '城市'],
    ['张三', 28, '北京'],
    ['李四', 32, '上海']
  ]"
  :width="600"
  :height="300"
  :show-filter="true"
/&gt;</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VTableComponent from '@/components/VTableComponent.vue'

// 基础数据示例
const basicData = [
  ['姓名', '年龄', '城市'],
  ['张三', 28, '北京'],
  ['李四', 32, '上海'],
  ['王五', 25, '广州'],
  ['赵六', 30, '深圳']
]

// 动态数据示例
const dynamicData = ref([
  ['产品', '价格', '库存', '分类'],
  ['iPhone 15', 5999, 100, '手机'],
  ['MacBook Pro', 12999, 50, '电脑'],
  ['AirPods', 1299, 200, '耳机']
])

// 简单数据示例
const simpleData = [
  ['部门', '人数'],
  ['研发', 20],
  ['销售', 15],
  ['行政', 5]
]

const dynamicTableRef = ref()

// 添加行
function addRow() {
  const newRow = [
    `产品${dynamicData.value.length}`,
    Math.floor(Math.random() * 10000),
    Math.floor(Math.random() * 100),
    '新分类'
  ]
  dynamicData.value.push(newRow)
}

// 删除行
function removeRow() {
  if (dynamicData.value.length > 1) {
    dynamicData.value.pop()
  }
}
</script>

<style scoped>
.demo-view {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 40px;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.demo-section h2 {
  color: #409eff;
  margin-bottom: 10px;
}

.demo-section p {
  color: #666;
  margin-bottom: 20px;
}

.demo-btn {
  margin-right: 10px;
  margin-bottom: 20px;
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.demo-btn:hover {
  background: #66b1ff;
}

.usage-info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.usage-info h3 {
  color: #333;
  margin-top: 20px;
  margin-bottom: 10px;
}

.usage-info ul {
  margin-left: 20px;
}

.usage-info li {
  margin-bottom: 5px;
  color: #666;
}

.usage-info code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.usage-info pre {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  margin-top: 10px;
}

.usage-info pre code {
  background: none;
  padding: 0;
}
</style>
