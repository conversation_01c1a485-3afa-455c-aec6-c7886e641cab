<template>
  <div class="basic-table-component">
    <!-- 增强筛选面板 -->
    <div v-if="showFilter" class="filter-panel">
      <!-- 基础筛选 -->
      <div class="filter-row">
        <div class="filter-item">
          <label class="filter-label">筛选列:</label>
          <select v-model="filterColumn" class="filter-select">
            <option value="">所有列</option>
            <option
              v-for="(header, index) in headers"
              :key="index"
              :value="index.toString()"
            >
              {{ header }}
            </option>
          </select>
        </div>
        <div class="filter-item">
          <label class="filter-label">关键词:</label>
          <input
            v-model="filterText"
            placeholder="输入关键词筛选"
            class="filter-input"
            @keyup.enter="applyFilter"
          />
        </div>
        <div class="filter-item">
          <label class="filter-label">筛选模式:</label>
          <select v-model="filterMode" class="filter-select">
            <option value="contains">包含</option>
            <option value="equals">等于</option>
            <option value="startsWith">开头匹配</option>
            <option value="endsWith">结尾匹配</option>
            <option value="regex">正则表达式</option>
          </select>
        </div>
      </div>

      <!-- 高级筛选 -->
      <div v-if="showAdvancedFilter" class="filter-row advanced-filter">
        <div class="filter-item" v-if="isNumericColumn(parseInt(filterColumn))">
          <label class="filter-label">数值范围:</label>
          <input
            v-model="numericFilter.min"
            type="number"
            placeholder="最小值"
            class="filter-input-small"
          />
          <span class="filter-separator">-</span>
          <input
            v-model="numericFilter.max"
            type="number"
            placeholder="最大值"
            class="filter-input-small"
          />
        </div>
        <div class="filter-item" v-if="isDateColumn(parseInt(filterColumn))">
          <label class="filter-label">日期范围:</label>
          <input
            v-model="dateFilter.start"
            type="date"
            class="filter-input-small"
          />
          <span class="filter-separator">-</span>
          <input
            v-model="dateFilter.end"
            type="date"
            class="filter-input-small"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="filter-actions">
        <button class="filter-button primary" @click="applyFilter">
          <span class="button-icon">🔍</span>筛选
        </button>
        <button class="filter-button" @click="resetFilter">
          <span class="button-icon">🔄</span>重置
        </button>
        <button
          class="filter-button secondary"
          @click="toggleAdvancedFilter"
        >
          <span class="button-icon">⚙️</span>
          {{ showAdvancedFilter ? '简单筛选' : '高级筛选' }}
        </button>
        <button
          v-if="filterHistory.length > 0"
          class="filter-button secondary"
          @click="showFilterHistory = !showFilterHistory"
        >
          <span class="button-icon">📋</span>历史记录
        </button>
      </div>

      <!-- 筛选历史 -->
      <div v-if="showFilterHistory && filterHistory.length > 0" class="filter-history">
        <div class="filter-history-header">
          <span>筛选历史</span>
          <button @click="clearFilterHistory" class="clear-history-btn">清空</button>
        </div>
        <div class="filter-history-list">
          <div
            v-for="(history, index) in filterHistory"
            :key="index"
            class="filter-history-item"
            @click="applyFilterHistory(history)"
          >
            <span class="history-text">{{ formatFilterHistory(history) }}</span>
            <span class="history-time">{{ formatTime(history.timestamp) }}</span>
          </div>
        </div>
      </div>

      <!-- 当前筛选状态 -->
      <div v-if="hasActiveFilter" class="filter-status">
        <span class="status-text">
          当前筛选: {{ getFilterStatusText() }}
          (显示 {{ filteredData.length }} / {{ tableData.length }} 条记录)
        </span>
      </div>
    </div>

    <!-- 优化的表格容器 -->
    <div
      class="table-wrapper"
      :style="{
        height: height ? `${height}px` : 'auto',
        width: width ? `${width}px` : '100%'
      }"
    >
      <!-- 表格头部容器 (固定) -->
      <div class="table-header-container" ref="headerContainer">
        <table class="data-table header-table" ref="headerTable">
          <thead>
            <tr>
              <th
                v-for="(header, index) in headers"
                :key="index"
                :class="{
                  'sticky-column': index < frozenColumns,
                  'numeric-column': isNumericColumn(index),
                  'date-column': isDateColumn(index),
                  'sortable': true,
                  'sorted': sortColumn === index.toString()
                }"
                :style="{
                  minWidth: getColumnWidth(index),
                  width: getColumnWidth(index)
                }"
                @click="sortByColumn(index)"
                @mousedown="startColumnResize($event, index)"
              >
                <div class="header-content">
                  <span class="header-text">{{ header }}</span>
                  <span v-if="sortColumn === index.toString()" class="sort-indicator">
                    {{ sortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </div>
                <div class="column-resizer" @mousedown.stop="startColumnResize($event, index)"></div>
              </th>
            </tr>
          </thead>
        </table>
      </div>

      <!-- 表格主体容器 (可滚动) -->
      <div
        class="table-body-container"
        ref="bodyContainer"
        @scroll="handleScroll"
        :style="{
          maxHeight: height ? `${height - 40}px` : '400px'
        }"
      >
        <table class="data-table body-table" ref="bodyTable">
          <tbody>
            <!-- 虚拟滚动支持 -->
            <tr
              v-for="(row, rowIndex) in visibleData"
              :key="startIndex + rowIndex"
              :class="{
                'row-even': (startIndex + rowIndex) % 2 === 0,
                'row-odd': (startIndex + rowIndex) % 2 === 1
              }"
            >
              <td
                v-for="(cell, cellIndex) in row"
                :key="cellIndex"
                :class="{
                  'sticky-column': cellIndex < frozenColumns,
                  'numeric-cell': isNumericValue(cell),
                  'date-cell': isDateValue(cell)
                }"
                :style="{
                  minWidth: getColumnWidth(cellIndex),
                  width: getColumnWidth(cellIndex)
                }"
                :title="String(cell)"
              >
                {{ formatCellValue(cell) }}
              </td>
            </tr>
            <!-- 空数据提示 -->
            <tr v-if="filteredData.length === 0" class="no-data-row">
              <td :colspan="headers.length" class="no-data-cell">
                <div class="no-data-content">
                  <span class="no-data-icon">📋</span>
                  <span class="no-data-text">暂无数据</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 虚拟滚动占位 -->
        <div
          v-if="enableVirtualScroll && filteredData.length > pageSize"
          class="virtual-spacer"
          :style="{ height: `${(filteredData.length - visibleData.length) * rowHeight}px` }"
        ></div>
      </div>

      <!-- 滚动条同步指示器 -->
      <div v-if="isScrolling" class="scroll-indicator">
        滚动中... ({{ Math.round(scrollPercentage * 100) }}%)
      </div>
    </div>
    
    <!-- 分页器 (可选) -->
    <div v-if="showPagination && totalPages > 1" class="pagination">
      <button 
        class="page-button" 
        :disabled="currentPage === 1"
        @click="changePage(currentPage - 1)"
      >
        上一页
      </button>
      <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
      <button 
        class="page-button" 
        :disabled="currentPage === totalPages"
        @click="changePage(currentPage + 1)"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, onUnmounted } from 'vue';

// Props定义
const props = defineProps({
  // 表格数据 (二维数组，第一行为表头)
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  // 表格宽度
  width: {
    type: Number,
    default: 800
  },
  // 表格高度
  height: {
    type: Number,
    default: 400
  },
  // 是否显示筛选面板
  showFilter: {
    type: Boolean,
    default: true
  },
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: false
  },
  // 每页显示条数
  pageSize: {
    type: Number,
    default: 20
  },
  // 固定前几列
  frozenColumns: {
    type: Number,
    default: 1
  },
  // 是否启用虚拟滚动
  enableVirtualScroll: {
    type: Boolean,
    default: true
  },
  // 行高 (用于虚拟滚动计算)
  rowHeight: {
    type: Number,
    default: 32
  }
});

// 响应式状态
const filterColumn = ref('');
const filterText = ref('');
const filterMode = ref('contains'); // 筛选模式
const sortColumn = ref('');
const sortDirection = ref('asc');
const currentPage = ref(1);

// DOM引用
const headerContainer = ref(null);
const bodyContainer = ref(null);
const headerTable = ref(null);
const bodyTable = ref(null);

// 数据状态
const columnTypes = ref([]); // 存储每列的数据类型
const columnWidths = ref([]); // 存储每列的宽度

// 筛选相关状态
const showAdvancedFilter = ref(false);
const numericFilter = ref({ min: '', max: '' });
const dateFilter = ref({ start: '', end: '' });
const filterHistory = ref([]);
const showFilterHistory = ref(false);

// 虚拟滚动状态
const startIndex = ref(0);
const endIndex = ref(props.pageSize);
const scrollTop = ref(0);
const isScrolling = ref(false);
const scrollPercentage = ref(0);

// 列调整状态
const isResizing = ref(false);
const resizingColumn = ref(-1);
const resizeStartX = ref(0);
const resizeStartWidth = ref(0);

// 解析表格数据
const headers = computed(() => {
  if (!props.data || props.data.length === 0) return [];
  return props.data[0];
});

const tableData = computed(() => {
  if (!props.data || props.data.length <= 1) return [];
  return props.data.slice(1);
});

// 检查是否有活动筛选
const hasActiveFilter = computed(() => {
  return filterText.value.trim() !== '' ||
         numericFilter.value.min !== '' ||
         numericFilter.value.max !== '' ||
         dateFilter.value.start !== '' ||
         dateFilter.value.end !== '';
});

// 虚拟滚动的可见数据
const visibleData = computed(() => {
  if (!props.enableVirtualScroll || filteredData.value.length <= props.pageSize) {
    return filteredData.value;
  }

  const start = startIndex.value;
  const end = Math.min(start + props.pageSize, filteredData.value.length);
  return filteredData.value.slice(start, end);
});

// 检测并缓存列类型
const detectColumnTypes = () => {
  if (headers.value.length === 0 || tableData.value.length === 0) return;
  
  // 重置列类型数组
  columnTypes.value = new Array(headers.value.length).fill('text');
  
  // 分析每列的数据类型（取前20行样本）
  const sampleRows = tableData.value.slice(0, 20);
  
  for (let colIndex = 0; colIndex < headers.value.length; colIndex++) {
    let numericCount = 0;
    let dateCount = 0;
    let totalCount = 0;
    
    for (const row of sampleRows) {
      if (row[colIndex] !== undefined && row[colIndex] !== null && row[colIndex] !== '') {
        totalCount++;
        if (isNumericValue(row[colIndex])) numericCount++;
        if (isDateValue(row[colIndex])) dateCount++;
      }
    }
    
    // 根据占比确定列类型
    if (totalCount > 0) {
      if (dateCount / totalCount > 0.5) {
        columnTypes.value[colIndex] = 'date';
      } else if (numericCount / totalCount > 0.5) {
        columnTypes.value[colIndex] = 'numeric';
      }
    }
  }
};

// 判断是否为数值列
const isNumericColumn = (index) => {
  return columnTypes.value[index] === 'numeric';
};

// 判断是否为日期列
const isDateColumn = (index) => {
  return columnTypes.value[index] === 'date';
};

// 判断单元格值是否为数字
const isNumericValue = (value) => {
  if (value === null || value === undefined || value === '') return false;
  if (typeof value === 'number') return true;
  if (typeof value === 'string') {
    // 移除货币符号后判断是否为数字
    const strValue = value.replace(/^[¥$€]\s?/, '');
    return !isNaN(Number(strValue)) && !/^\d{4}[-/]\d{1,2}[-/]\d{1,2}/.test(value);
  }
  return false;
};

// 判断单元格值是否为日期
const isDateValue = (value) => {
  return typeof value === 'string' && /^\d{4}[-/]\d{1,2}[-/]\d{1,2}/.test(value);
};

// 增强的排序和筛选逻辑
const processedData = computed(() => {
  let result = [...tableData.value];

  // 排序
  if (sortColumn.value !== '') {
    const colIndex = parseInt(sortColumn.value);
    result.sort((a, b) => {
      const valueA = a[colIndex];
      const valueB = b[colIndex];

      // 处理数字排序
      if (isNumericValue(valueA) && isNumericValue(valueB)) {
        const numA = parseFloat(String(valueA).replace(/[¥$€,]/g, ''));
        const numB = parseFloat(String(valueB).replace(/[¥$€,]/g, ''));
        return sortDirection.value === 'asc' ? numA - numB : numB - numA;
      }

      // 处理日期排序
      if (isDateValue(valueA) && isDateValue(valueB)) {
        const dateA = new Date(valueA);
        const dateB = new Date(valueB);
        return sortDirection.value === 'asc' ? dateA - dateB : dateB - dateA;
      }

      // 字符串排序
      const strA = String(valueA || '').toLowerCase();
      const strB = String(valueB || '').toLowerCase();

      if (sortDirection.value === 'asc') {
        return strA.localeCompare(strB, 'zh-CN', { numeric: true });
      } else {
        return strB.localeCompare(strA, 'zh-CN', { numeric: true });
      }
    });
  }

  // 增强筛选
  result = applyAdvancedFilter(result);

  return result;
});

// 高级筛选逻辑
const applyAdvancedFilter = (data) => {
  let result = [...data];

  // 文本筛选
  if (filterText.value.trim()) {
    const keyword = filterText.value.trim();

    if (filterColumn.value) {
      // 指定列筛选
      const colIndex = parseInt(filterColumn.value);
      result = result.filter(row => {
        const cellValue = String(row[colIndex] || '');
        return matchesFilterMode(cellValue, keyword, filterMode.value);
      });
    } else {
      // 全部列筛选
      result = result.filter(row => {
        return row.some(cell =>
          matchesFilterMode(String(cell || ''), keyword, filterMode.value)
        );
      });
    }
  }

  // 数值范围筛选
  if (filterColumn.value && (numericFilter.value.min !== '' || numericFilter.value.max !== '')) {
    const colIndex = parseInt(filterColumn.value);
    if (isNumericColumn(colIndex)) {
      result = result.filter(row => {
        const cellValue = row[colIndex];
        if (!isNumericValue(cellValue)) return false;

        const numValue = parseFloat(String(cellValue).replace(/[¥$€,]/g, ''));
        const min = numericFilter.value.min !== '' ? parseFloat(numericFilter.value.min) : -Infinity;
        const max = numericFilter.value.max !== '' ? parseFloat(numericFilter.value.max) : Infinity;

        return numValue >= min && numValue <= max;
      });
    }
  }

  // 日期范围筛选
  if (filterColumn.value && (dateFilter.value.start !== '' || dateFilter.value.end !== '')) {
    const colIndex = parseInt(filterColumn.value);
    if (isDateColumn(colIndex)) {
      result = result.filter(row => {
        const cellValue = row[colIndex];
        if (!isDateValue(cellValue)) return false;

        const cellDate = new Date(cellValue);
        const startDate = dateFilter.value.start ? new Date(dateFilter.value.start) : new Date('1900-01-01');
        const endDate = dateFilter.value.end ? new Date(dateFilter.value.end) : new Date('2100-12-31');

        return cellDate >= startDate && cellDate <= endDate;
      });
    }
  }

  return result;
};

// 筛选模式匹配
const matchesFilterMode = (cellValue, keyword, mode) => {
  const cell = cellValue.toLowerCase();
  const key = keyword.toLowerCase();

  switch (mode) {
    case 'equals':
      return cell === key;
    case 'startsWith':
      return cell.startsWith(key);
    case 'endsWith':
      return cell.endsWith(key);
    case 'regex':
      try {
        const regex = new RegExp(keyword, 'i');
        return regex.test(cellValue);
      } catch (e) {
        return false;
      }
    case 'contains':
    default:
      return cell.includes(key);
  }
};

// 分页后的数据
const filteredData = computed(() => {
  if (!props.showPagination) return processedData.value;
  
  const start = (currentPage.value - 1) * props.pageSize;
  return processedData.value.slice(start, start + props.pageSize);
});

// 总页数
const totalPages = computed(() => {
  if (!props.showPagination) return 1;
  return Math.ceil(processedData.value.length / props.pageSize);
});

// 计算列宽
const getColumnWidth = (index) => {
  // 根据内容自动计算列宽
  const minWidth = 80; // 最小宽度
  const maxWidth = 300; // 最大宽度
  
  // 计算标题宽度
  const headerContent = headers.value[index] || '';
  const headerLength = String(headerContent).length;
  let columnWidth = headerLength * 12 + 24; // 基础宽度
  
  // 考虑数据内容宽度（取前20行样本）
  const dataRows = tableData.value.slice(0, 20);
  let maxContentLength = 0;
  let hasNumericValues = false;
  let hasDateValues = false;
  
  dataRows.forEach(row => {
    if (row[index] !== undefined) {
      const cellValue = row[index];
      const strValue = String(cellValue);
      maxContentLength = Math.max(maxContentLength, strValue.length);
      
      // 检测是否为数字列
      if (!isNaN(Number(cellValue)) && cellValue !== null && cellValue !== '') {
        hasNumericValues = true;
      }
      
      // 检测是否为日期列
      if (typeof cellValue === 'string' && /^\d{4}[-/]\d{1,2}[-/]\d{1,2}/.test(cellValue)) {
        hasDateValues = true;
      }
    }
  });
  
  // 根据列的内容类型调整宽度
  if (hasNumericValues && !hasDateValues) {
    // 数字列通常需要更少的宽度
    columnWidth = Math.max(columnWidth, maxContentLength * 7 + 16);
    return `${Math.min(Math.max(columnWidth, minWidth), 150)}px`;
  } else if (hasDateValues) {
    // 日期列使用固定宽度
    return '120px';
  } else {
    // 文本列
    columnWidth = Math.max(columnWidth, maxContentLength * 8 + 24);
    // 约束列宽范围
    return `${Math.min(Math.max(columnWidth, minWidth), maxWidth)}px`;
  }
};

// 格式化单元格值
const formatCellValue = (value) => {
  if (value === null || value === undefined || value === '') return '';
  
  // 检查是否为数字
  if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
    try {
      const num = Number(value);
      if (!isNaN(num)) {
        // 如果是整数，直接返回
        if (Number.isInteger(num)) {
          return String(num);
        }
        // 尝试分析原始格式中的小数位数
        const strValue = String(value);
        const decimalPart = strValue.split('.')[1];
        
        // 如果原始值有小数部分且小于等于2位，保留原样
        if (decimalPart && decimalPart.length <= 2) {
          return strValue;
        }
        // 否则保留两位小数
        return num.toFixed(2);
      }
    } catch (e) {
      // 转换失败，按原样返回
    }
  }
  
  // 已经格式化为货币的字符串
  if (typeof value === 'string' && /^[¥$€]/.test(value)) {
    return value;
  }
  
  // 检查是否为日期
  if (typeof value === 'string' && /^\d{4}[-/]\d{1,2}[-/]\d{1,2}/.test(value)) {
    return value; // 日期字符串保持原样
  }
  
  // 其他情况，转为字符串
  return String(value);
};

// 排序
const sortByColumn = (index) => {
  const indexStr = index.toString();
  if (sortColumn.value === indexStr) {
    // 切换排序方向
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // 设置新的排序列
    sortColumn.value = indexStr;
    sortDirection.value = 'asc';
  }
};

// 增强的筛选方法
const applyFilter = () => {
  currentPage.value = 1; // 重置到第一页

  // 保存筛选历史
  if (hasActiveFilter.value) {
    const filterState = {
      column: filterColumn.value,
      text: filterText.value,
      mode: filterMode.value,
      numericFilter: { ...numericFilter.value },
      dateFilter: { ...dateFilter.value },
      timestamp: Date.now()
    };

    // 避免重复的筛选历史
    const isDuplicate = filterHistory.value.some(h =>
      h.column === filterState.column &&
      h.text === filterState.text &&
      h.mode === filterState.mode
    );

    if (!isDuplicate) {
      filterHistory.value.unshift(filterState);
      // 限制历史记录数量
      if (filterHistory.value.length > 10) {
        filterHistory.value = filterHistory.value.slice(0, 10);
      }
    }
  }
};

// 重置筛选
const resetFilter = () => {
  filterColumn.value = '';
  filterText.value = '';
  filterMode.value = 'contains';
  numericFilter.value = { min: '', max: '' };
  dateFilter.value = { start: '', end: '' };
  currentPage.value = 1;
  showAdvancedFilter.value = false;
};

// 切换高级筛选
const toggleAdvancedFilter = () => {
  showAdvancedFilter.value = !showAdvancedFilter.value;
};

// 应用筛选历史
const applyFilterHistory = (history) => {
  filterColumn.value = history.column;
  filterText.value = history.text;
  filterMode.value = history.mode;
  numericFilter.value = { ...history.numericFilter };
  dateFilter.value = { ...history.dateFilter };
  showFilterHistory.value = false;
  applyFilter();
};

// 清空筛选历史
const clearFilterHistory = () => {
  filterHistory.value = [];
  showFilterHistory.value = false;
};

// 格式化筛选历史显示
const formatFilterHistory = (history) => {
  let text = '';
  if (history.column) {
    const columnName = headers.value[parseInt(history.column)] || '未知列';
    text += `${columnName}: `;
  }
  if (history.text) {
    text += `"${history.text}" (${history.mode})`;
  }
  if (history.numericFilter.min || history.numericFilter.max) {
    text += ` 数值: ${history.numericFilter.min || '∞'} - ${history.numericFilter.max || '∞'}`;
  }
  if (history.dateFilter.start || history.dateFilter.end) {
    text += ` 日期: ${history.dateFilter.start || '∞'} - ${history.dateFilter.end || '∞'}`;
  }
  return text || '空筛选';
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取筛选状态文本
const getFilterStatusText = () => {
  let parts = [];
  if (filterText.value) {
    const columnName = filterColumn.value ?
      headers.value[parseInt(filterColumn.value)] : '所有列';
    parts.push(`${columnName}包含"${filterText.value}"`);
  }
  if (numericFilter.value.min || numericFilter.value.max) {
    parts.push(`数值范围: ${numericFilter.value.min || '∞'} - ${numericFilter.value.max || '∞'}`);
  }
  if (dateFilter.value.start || dateFilter.value.end) {
    parts.push(`日期范围: ${dateFilter.value.start || '∞'} - ${dateFilter.value.end || '∞'}`);
  }
  return parts.join(', ');
};

// 翻页
const changePage = (page) => {
  currentPage.value = Math.max(1, Math.min(page, totalPages.value));
};

// 处理滚动事件
const handleScroll = (event) => {
  const container = event.target;
  scrollTop.value = container.scrollTop;

  // 同步表头滚动
  if (headerContainer.value) {
    headerContainer.value.scrollLeft = container.scrollLeft;
  }

  // 计算滚动百分比
  const maxScroll = container.scrollHeight - container.clientHeight;
  scrollPercentage.value = maxScroll > 0 ? container.scrollTop / maxScroll : 0;

  // 虚拟滚动计算
  if (props.enableVirtualScroll && filteredData.value.length > props.pageSize) {
    const newStartIndex = Math.floor(container.scrollTop / props.rowHeight);
    startIndex.value = Math.max(0, newStartIndex);
  }

  // 滚动指示器
  isScrolling.value = true;
  clearTimeout(handleScroll.scrollTimer);
  handleScroll.scrollTimer = setTimeout(() => {
    isScrolling.value = false;
  }, 150);
};

// 开始列宽调整
const startColumnResize = (event, columnIndex) => {
  event.preventDefault();
  isResizing.value = true;
  resizingColumn.value = columnIndex;
  resizeStartX.value = event.clientX;

  // 获取当前列宽
  const th = event.target.closest('th');
  resizeStartWidth.value = th ? th.offsetWidth : 100;

  // 添加全局鼠标事件
  document.addEventListener('mousemove', handleColumnResize);
  document.addEventListener('mouseup', endColumnResize);

  // 防止文本选择
  document.body.style.userSelect = 'none';
};

// 处理列宽调整
const handleColumnResize = (event) => {
  if (!isResizing.value) return;

  const deltaX = event.clientX - resizeStartX.value;
  const newWidth = Math.max(50, resizeStartWidth.value + deltaX);

  // 更新列宽
  columnWidths.value[resizingColumn.value] = newWidth;

  // 应用新宽度到表格
  updateColumnWidth(resizingColumn.value, newWidth);
};

// 结束列宽调整
const endColumnResize = () => {
  isResizing.value = false;
  resizingColumn.value = -1;

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleColumnResize);
  document.removeEventListener('mouseup', endColumnResize);

  // 恢复文本选择
  document.body.style.userSelect = '';
};

// 更新列宽
const updateColumnWidth = (columnIndex, width) => {
  const headerCells = headerTable.value?.querySelectorAll('th');
  const bodyCells = bodyTable.value?.querySelectorAll(`td:nth-child(${columnIndex + 1})`);

  if (headerCells && headerCells[columnIndex]) {
    headerCells[columnIndex].style.width = `${width}px`;
    headerCells[columnIndex].style.minWidth = `${width}px`;
  }

  if (bodyCells) {
    bodyCells.forEach(cell => {
      cell.style.width = `${width}px`;
      cell.style.minWidth = `${width}px`;
    });
  }
};

// 监听数据变化
watch(() => props.data, () => {
  resetFilter();
  sortColumn.value = '';
  currentPage.value = 1;
  
  // 分析列类型
  nextTick(() => {
    detectColumnTypes();
    updateTableLayout();
  });
}, { deep: true });

// 监听筛选条件变化
watch(filterText, () => {
  if (filterText.value === '') {
    currentPage.value = 1;
  }
});

// 挂载后初始化
onMounted(() => {
  // 分析列类型
  detectColumnTypes();
  
  // 初始化表格布局
  updateTableLayout();
  
  // 添加窗口大小变化的监听
  window.addEventListener('resize', () => {
    updateTableLayout();
  });
});

// 确保在组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateTableLayout);
});

// 更新表格布局以确保表格能完整显示
const updateTableLayout = () => {
  nextTick(() => {
    if (tableContainer.value && dataTable.value) {
      // 计算表格实际宽度
      setTimeout(() => {
        // 获取容器宽度
        const containerWidth = tableContainer.value.clientWidth;
        
        // 延迟执行以确保DOM已完全渲染
        const allCells = dataTable.value.querySelectorAll('th');
        if (allCells.length > 0) {
          // 计算所有列宽的总和
          let totalWidth = 0;
          allCells.forEach((cell) => {
            const width = cell.offsetWidth;
            totalWidth += width;
          });
          
          // 加上一些额外的宽度，确保所有列都能完整显示
          totalWidth += 50; // 增加额外边距，确保所有列都能显示
          
          // 如果计算的总宽度小于容器宽度，使用容器宽度
          if (totalWidth < containerWidth) {
            totalWidth = containerWidth;
          }
          
          // 设置表格的最小宽度
          dataTable.value.style.minWidth = `${totalWidth}px`;
          
          // 确保容器可以水平滚动
          tableContainer.value.style.overflowX = 'auto';
          
          // 检查容器是否能容纳表格
          const isScrollable = totalWidth > containerWidth;
          
          // 打印一些调试信息
          console.log('表格布局计算:', {
            总列宽: totalWidth,
            容器宽度: containerWidth,
            需要滚动: isScrollable,
            设置最小宽度: `${totalWidth}px`
          });
        }
      }, 200); // 增加延迟时间，确保DOM完全渲染
    }
  });
};

// 暴露方法给父组件
defineExpose({
  resetFilter,
  applyFilter,
  sortByColumn,
  changePage,
  toggleAdvancedFilter,
  clearFilterHistory,
  // 获取当前筛选状态
  getFilterState: () => ({
    column: filterColumn.value,
    text: filterText.value,
    mode: filterMode.value,
    numericFilter: numericFilter.value,
    dateFilter: dateFilter.value,
    hasActiveFilter: hasActiveFilter.value
  }),
  // 设置筛选状态
  setFilterState: (state) => {
    filterColumn.value = state.column || '';
    filterText.value = state.text || '';
    filterMode.value = state.mode || 'contains';
    numericFilter.value = state.numericFilter || { min: '', max: '' };
    dateFilter.value = state.dateFilter || { start: '', end: '' };
  }
});
</script>

<style scoped>
.basic-table-component {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: 100%;
  position: relative; /* 添加相对定位 */
  flex: 1;
  height: auto;
}

/* 增强筛选面板 */
.filter-panel {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.advanced-filter {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border: 1px dashed #d0d7de;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
  min-width: 60px;
}

.filter-select,
.filter-input {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  color: #606266;
  transition: all 0.2s;
  width: 160px;
}

.filter-input-small {
  padding: 6px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  width: 100px;
}

.filter-separator {
  color: #909399;
  margin: 0 4px;
}

.filter-select:focus,
.filter-input:focus,
.filter-input-small:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.filter-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-button {
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background: #f4f4f5;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-button:hover {
  background: #e9e9eb;
  border-color: #c0c4cc;
}

.filter-button.primary {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.filter-button.primary:hover {
  background: #66b1ff;
}

.filter-button.secondary {
  background: #909399;
  color: white;
  border-color: #909399;
}

.filter-button.secondary:hover {
  background: #a6a9ad;
}

.button-icon {
  font-size: 12px;
}

/* 筛选历史 */
.filter-history {
  margin-top: 12px;
  border-top: 1px solid #e4e7ed;
  padding-top: 12px;
}

.filter-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

.clear-history-btn {
  background: none;
  border: none;
  color: #f56c6c;
  cursor: pointer;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  transition: all 0.2s;
}

.clear-history-btn:hover {
  background: #fef0f0;
}

.filter-history-list {
  max-height: 120px;
  overflow-y: auto;
}

.filter-history-item {
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-history-item:hover {
  background: #f0f9ff;
  border-color: #409eff;
}

.history-text {
  font-size: 11px;
  color: #606266;
  flex: 1;
}

.history-time {
  font-size: 10px;
  color: #909399;
}

/* 筛选状态 */
.filter-status {
  margin-top: 8px;
  padding: 8px 12px;
  background: #e1f3d8;
  border: 1px solid #b3d8a4;
  border-radius: 4px;
}

.status-text {
  font-size: 11px;
  color: #529b2e;
}

/* 优化的表格容器 */
.table-wrapper {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  background: #fff;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.table-header-container {
  overflow: hidden;
  border-bottom: 2px solid #e4e7ed;
  background: #fafbfc;
  position: relative;
  z-index: 10;
}

.table-body-container {
  overflow: auto;
  position: relative;
  background: #fff;
}

.header-table,
.body-table {
  width: 100%;
  table-layout: fixed;
}

/* 滚动条样式 */
.table-body-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-body-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-body-container::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.table-body-container::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

.table-body-container::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* 增强表格样式 */
.data-table {
  border-collapse: collapse;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.data-table th,
.data-table td {
  padding: 10px 12px;
  text-align: left;
  border-right: 1px solid #f0f0f0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
}

.data-table th:last-child,
.data-table td:last-child {
  border-right: none;
}

.data-table th {
  background: #fafbfc;
  color: #606266;
  font-weight: 600;
  font-size: 12px;
  border-bottom: 1px solid #e4e7ed;
  user-select: none;
  position: relative;
}

.data-table th.sortable {
  cursor: pointer;
  transition: all 0.2s;
}

.data-table th.sortable:hover {
  background: #f0f2f5;
}

.data-table th.sorted {
  background: #e6f7ff;
  color: #1890ff;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.column-resizer {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  background: transparent;
  transition: background 0.2s;
}

.column-resizer:hover {
  background: #409eff;
}

.data-table td {
  color: #606266;
  font-size: 12px;
  border-bottom: 1px solid #f6f6f6;
  transition: background-color 0.2s;
}

.data-table tbody tr:hover td {
  background-color: #f8f9fa;
}

.row-even {
  background-color: #fafafa;
}

.row-odd {
  background-color: #ffffff;
}

.row-even:hover,
.row-odd:hover {
  background-color: #f0f9ff !important;
}

/* 冻结列 */
.sticky-column {
  position: sticky;
  left: 0;
  z-index: 5;
  background-color: inherit !important;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
}

td.sticky-column {
  background-color: white !important;
}

th.sticky-column {
  z-index: 15;
  background-color: #f5f7fa !important;
}

tr:hover td.sticky-column {
  background-color: #f5f7fa !important;
}

.sort-indicator {
  margin-left: 4px;
  font-size: 12px;
  color: #409eff;
}

/* 无数据行 */
.no-data-row {
  background: #fafafa !important;
}

.no-data-cell {
  text-align: center;
  padding: 40px 20px !important;
  border: none !important;
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.no-data-icon {
  font-size: 24px;
  opacity: 0.5;
}

.no-data-text {
  color: #909399;
  font-size: 14px;
}

/* 虚拟滚动 */
.virtual-spacer {
  width: 100%;
  pointer-events: none;
}

/* 滚动指示器 */
.scroll-indicator {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  pointer-events: none;
  z-index: 100;
}

/* 分页器 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 10px;
}

.page-button {
  padding: 4px 10px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background: #f4f4f5;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.page-button:hover:not(:disabled) {
  background: #e9e9eb;
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 12px;
  color: #606266;
}

/* 为不同类型的单元格设置不同的样式 */
.numeric-column,
.numeric-cell {
  text-align: right;
}

.date-column, 
.date-cell {
  text-align: center;
}

.numeric-cell {
  font-family: 'Courier New', monospace;
  padding-right: 16px !important;
}
</style> 