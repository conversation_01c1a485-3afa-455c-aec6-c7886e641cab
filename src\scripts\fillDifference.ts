import { createUniver, defaultTheme, FUniver, LocaleType, merge, Univer } from '@univerjs/presets';

export default async function fetchDatadifference(univerAPIInstance:FUniver){
    try {
      const response = await fetch('http://localhost:8000/api/budget-data-difference', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json() as { [key: string]: { 收入: number, 成本: number } };
      const sheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
      if (sheet) {
        const arr=sheet.getRange(0,0,sheet.getLastRow()+1,sheet.getLastColumn()+1).getValues();
        var localIndex=0;
        var differIncomeIndex=0;
        var differCostIndex=0;
        var accmulatedIncomeIndex=0;
        var accmulatedCostIndex=0;
        for(let i=0;i<arr[0].length;i++){
            if(arr[0][i]=='定位符'){localIndex=i}
            if(arr[0][i]=='缺失收入'){differIncomeIndex=i}
            if(arr[0][i]=='缺失成本'){differCostIndex=i}
            if(arr[0][i]=='已过帐主营业务收入'){accmulatedIncomeIndex=i}
            if(arr[0][i]=='已过帐主营业务成本'){accmulatedCostIndex=i}
        }
        for(let i=1;i<arr.length;i++){
            var localValue=arr[i][localIndex] as string;
            if(localValue in data){
              try{
                var differIncome=data[localValue]['收入']-arr[i][accmulatedIncomeIndex];
                var differCost=data[localValue]['成本']-arr[i][accmulatedCostIndex];
                sheet.getRange(i,differIncomeIndex).setValue(differIncome);
                sheet.getRange(i,differCostIndex).setValue(differCost)
              }catch(error){
                console.log(error);
              }
            }
                
      }}
    } catch (error) {
      alert('获取数据失败: ' + error.message);
    }
  }



