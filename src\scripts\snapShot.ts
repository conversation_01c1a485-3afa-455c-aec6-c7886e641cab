import { createUniver, defaultTheme, FUniver, LocaleType, merge, Univer } from '@univerjs/presets';

export default async function fetchSnapShot(univerAPIInstance:FUniver){
    try{
    const response = await fetch('http://localhost:8000/api/get-snapsheet', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ periodId: 'max' })
  });
  
  if (!response.ok) {
    throw new Error(`获取快照失败: ${response.status} ${response.statusText}`);
  }

  const snapsheet = await response.json();
  const unitId = univerAPIInstance?.getActiveWorkbook()?.getId();

  if(unitId) {
    univerAPIInstance?.disposeUnit(unitId);
  }
  univerAPIInstance?.createWorkbook(snapsheet);
    }catch(error){
       alert(error);
        const wb=univerAPIInstance?.createWorkbook({id:"Sheet1",name:"财务报表"});
        wb?.create("收入成本测算",100,10);
        wb?.create("快速取数",100,10);
        wb?.create("独立结账模板安全费",100,10);
        wb?.create("批量暂估",100,10);
        wb.deleteSheet(wb.getSheetByName("Sheet1"))
    }
}