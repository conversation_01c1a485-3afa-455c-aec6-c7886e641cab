<template>
  <BaseQueryComponent
    title="应付汇总按合同"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/payable-by-contract'

// 查询字段配置
const queryFields = [
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    placeholder: '请输入项目名称',
    width: '200px'
  },
  {
    key: 'profitCenter',
    label: '利润中心',
    type: 'text',
    placeholder: '请输入利润中心编号',
    width: '150px'
  },
  {
    key: 'supplier',
    label: '供应商',
    type: 'text',
    placeholder: '请输入供应商编号',
    width: '150px'
  },
  {
    key: 'supplierDesc',
    label: '供应商描述',
    type: 'text',
    placeholder: '请输入供应商名称',
    width: '200px'
  },
  {
    key: 'businessType',
    label: '业务类型',
    type: 'select',
    placeholder: '请选择业务类型',
    width: '150px',
    options: [
      { label: '分包', value: '分包' },
      { label: '材料', value: '材料' },
      { label: '设备', value: '设备' },
      { label: '服务', value: '服务' },
      { label: '租赁', value: '租赁' }
    ]
  },
  {
    key: 'contractNumber',
    label: '合同编号',
    type: 'text',
    placeholder: '请输入合同编号',
    width: '180px'
  },
  {
    key: 'contractDesc',
    label: '合同文本描述',
    type: 'text',
    placeholder: '请输入合同描述',
    width: '200px'
  },
  {
    key: 'cumulativeSettlement',
    label: '累计结算',
    type: 'amount-range'
  },
  {
    key: 'cumulativePayment',
    label: '累计付款',
    type: 'amount-range'
  }
]
</script>
