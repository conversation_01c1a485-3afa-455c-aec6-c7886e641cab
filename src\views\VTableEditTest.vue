<template>
  <div class="vtable-edit-test">
    <h1>VTable编辑功能测试</h1>
    
    <div class="test-info">
      <h2>测试说明</h2>
      <ul>
        <li>双击任意单元格应该进入编辑模式</li>
        <li>编辑后按Enter确认，按Esc取消</li>
        <li>查看控制台输出以了解事件触发情况</li>
      </ul>
    </div>
    
    <div class="test-controls">
      <button @click="testEdit" class="test-btn">测试编辑第一个单元格</button>
      <button @click="logTableInfo" class="test-btn">输出表格信息</button>
      <button @click="toggleEditable" class="test-btn">
        {{ editable ? '禁用' : '启用' }}编辑
      </button>
    </div>
    
    <div class="table-container">
      <VTableComponent
        ref="tableRef"
        :data="testData"
        :width="800"
        :height="300"
        :show-filter="false"
        :editable="editable"
        :enable-copy-paste="true"
        :auto-width="false"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
      />
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <div class="debug-panel">
        <p><strong>编辑状态:</strong> {{ editable ? '启用' : '禁用' }}</p>
        <p><strong>最后编辑:</strong> {{ lastEdit || '无' }}</p>
        <p><strong>数据变化次数:</strong> {{ changeCount }}</p>
      </div>
      
      <h3>当前数据</h3>
      <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VTableComponent from '@/components/VTableComponent.vue'

// 表格引用
const tableRef = ref()

// 测试数据
const testData = ref([
  ['姓名', '年龄', '城市', '状态'],
  ['张三', 28, '北京', '在职'],
  ['李四', 32, '上海', '在职'],
  ['王五', 25, '广州', '试用期'],
  ['赵六', 30, '深圳', '在职']
])

// 状态
const editable = ref(true)
const lastEdit = ref('')
const changeCount = ref(0)

// 数据变化处理
const handleDataChange = (newData) => {
  console.log('数据变化:', newData)
  testData.value = newData
  changeCount.value++
}

// 单元格编辑处理
const handleCellEdit = (editInfo) => {
  console.log('单元格编辑:', editInfo)
  lastEdit.value = `行${editInfo.row}, 列${editInfo.col}: ${editInfo.oldValue} → ${editInfo.newValue}`
}

// 测试编辑
const testEdit = () => {
  if (tableRef.value) {
    console.log('尝试编辑第一个数据单元格 (行1, 列0)')
    // 注意：第一行是标题，所以数据从第二行开始
    tableRef.value.startEdit(1, 0)
  }
}

// 输出表格信息
const logTableInfo = () => {
  if (tableRef.value) {
    const instance = tableRef.value.getTableInstance()
    console.log('表格实例:', instance)
    console.log('表格配置:', instance?.options)
    console.log('列配置:', instance?.options?.columns)
  }
}

// 切换编辑状态
const toggleEditable = () => {
  editable.value = !editable.value
  console.log('编辑状态切换为:', editable.value)
}
</script>

<style scoped>
.vtable-edit-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.test-info h2 {
  margin: 0 0 10px 0;
  color: #007bff;
}

.test-info ul {
  margin: 0;
  padding-left: 20px;
}

.test-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.test-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.test-btn:hover {
  background: #0056b3;
}

.table-container {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.debug-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.debug-info h3 {
  margin: 0 0 15px 0;
  color: #495057;
}

.debug-panel {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  margin-bottom: 20px;
}

.debug-panel p {
  margin: 5px 0;
  font-family: monospace;
}

pre {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
}
</style>
