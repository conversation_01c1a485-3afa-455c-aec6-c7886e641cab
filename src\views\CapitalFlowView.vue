<template>
  <div class="capital-flow-container">
    <h1>资金流动分析</h1>

    <div class="page-header">
      <div class="filter-section">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="dateShortcuts"
          @change="handleDateChange"
        />
        <el-select
          v-model="selectedAccount"
          placeholder="选择账户"
          @change="loadCapitalFlowData"
        >
          <el-option
            v-for="account in accounts"
            :key="account.id"
            :label="account.name"
            :value="account.id"
          ></el-option>
        </el-select>
        <el-select
          v-model="selectedFlowType"
          placeholder="流动类型"
          @change="loadCapitalFlowData"
        >
          <el-option
            v-for="type in flowTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="searchData">
          <i class="el-icon-search"></i> 查询
        </el-button>
        <el-button @click="resetFilters">
          <i class="el-icon-refresh"></i> 重置
        </el-button>
      </div>
      <div class="action-section">
        <el-button type="success" @click="exportData">
          <i class="el-icon-download"></i> 导出数据
        </el-button>
        <el-button type="primary" @click="showFlowAnalysis">
          <i class="el-icon-data-analysis"></i> 流动分析
        </el-button>
      </div>
    </div>

    <div class="summary-cards">
      <div class="summary-card">
        <div class="card-icon inflow">
          <i class="el-icon-top"></i>
        </div>
        <div class="card-content">
          <div class="card-title">资金流入</div>
          <div class="card-value">
            ¥{{ formatNumber(summaryData.totalInflow) }}
          </div>
          <div
            class="card-change"
            :class="{
              positive: summaryData.inflowChange > 0,
              negative: summaryData.inflowChange < 0,
            }"
          >
            {{ summaryData.inflowChange > 0 ? "+" : ""
            }}{{ summaryData.inflowChange }}%
          </div>
        </div>
      </div>
      <div class="summary-card">
        <div class="card-icon outflow">
          <i class="el-icon-bottom"></i>
        </div>
        <div class="card-content">
          <div class="card-title">资金流出</div>
          <div class="card-value">
            ¥{{ formatNumber(summaryData.totalOutflow) }}
          </div>
          <div
            class="card-change"
            :class="{
              positive: summaryData.outflowChange < 0,
              negative: summaryData.outflowChange > 0,
            }"
          >
            {{ summaryData.outflowChange > 0 ? "+" : ""
            }}{{ summaryData.outflowChange }}%
          </div>
        </div>
      </div>
      <div class="summary-card">
        <div class="card-icon net-flow">
          <i class="el-icon-d-arrow-right"></i>
        </div>
        <div class="card-content">
          <div class="card-title">净流动</div>
          <div class="card-value">¥{{ formatNumber(summaryData.netFlow) }}</div>
          <div
            class="card-change"
            :class="{
              positive: summaryData.netFlowChange > 0,
              negative: summaryData.netFlowChange < 0,
            }"
          >
            {{ summaryData.netFlowChange > 0 ? "+" : ""
            }}{{ summaryData.netFlowChange }}%
          </div>
        </div>
      </div>
      <div class="summary-card">
        <div class="card-icon balance">
          <i class="el-icon-wallet"></i>
        </div>
        <div class="card-content">
          <div class="card-title">当前余额</div>
          <div class="card-value">
            ¥{{ formatNumber(summaryData.currentBalance) }}
          </div>
          <div
            class="card-change"
            :class="{
              positive: summaryData.balanceChange > 0,
              negative: summaryData.balanceChange < 0,
            }"
          >
            {{ summaryData.balanceChange > 0 ? "+" : ""
            }}{{ summaryData.balanceChange }}%
          </div>
        </div>
      </div>
    </div>

    <div class="chart-section">
      <div class="chart-container">
        <div class="chart-header">
          <h3>资金流动趋势</h3>
          <el-radio-group
            v-model="chartTimeUnit"
            size="small"
            @change="updateFlowTrendChart"
          >
            <el-radio-button label="day">日</el-radio-button>
            <el-radio-button label="week">周</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
          </el-radio-group>
        </div>
        <div id="flowTrendChart" class="chart"></div>
      </div>
      <div class="chart-container">
        <div class="chart-header">
          <h3>资金流动分类</h3>
          <el-radio-group
            v-model="chartFlowType"
            size="small"
            @change="updateFlowCategoryChart"
          >
            <el-radio-button label="inflow">流入</el-radio-button>
            <el-radio-button label="outflow">流出</el-radio-button>
          </el-radio-group>
        </div>
        <div id="flowCategoryChart" class="chart"></div>
      </div>
    </div>

    <div class="table-section">
      <div class="table-header">
        <h3>资金流动明细</h3>
        <div class="table-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索交易"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          ></el-input>
        </div>
      </div>
      <VTableComponent
        :data="filteredTableData"
        :width="tableWidth"
        :height="400"
        :show-filter="true"
        :editable="false"
        :enable-copy-paste="true"
        :auto-width="true"
      />
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalRecords"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import VTableComponent from "@/components/VTableComponent.vue";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import { 
  Search, Download, Refresh, ArrowUp, ArrowDown, 
  CircleCheck, Warning, Calendar, Money, Document
} from '@element-plus/icons-vue'

// 页面状态
const dateRange = ref([
  new Date(new Date().setMonth(new Date().getMonth() - 1)),
  new Date(),
]);
const selectedAccount = ref("all");
const selectedFlowType = ref("all");
const chartTimeUnit = ref("day");
const chartFlowType = ref("inflow");
const searchKeyword = ref("");
const tableWidth = ref(0);

// 分页状态
const currentPage = ref(1);
const pageSize = ref(20);
const totalRecords = ref(0);

// 下拉选项
const accounts = ref([
  { id: "all", name: "所有账户" },
  { id: "bank1", name: "工商银行基本户" },
  { id: "bank2", name: "建设银行一般户" },
  { id: "bank3", name: "农业银行专用户" },
  { id: "cash", name: "现金账户" },
]);

const flowTypes = ref([
  { value: "all", label: "所有类型" },
  { value: "inflow", label: "资金流入" },
  { value: "outflow", label: "资金流出" },
]);

const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 1);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 3);
      return [start, end];
    },
  },
];

// 汇总数据
const summaryData = ref({
  totalInflow: 3850000,
  totalOutflow: 2950000,
  netFlow: 900000,
  currentBalance: 4750000,
  inflowChange: 12.5,
  outflowChange: 8.7,
  netFlowChange: 22.3,
  balanceChange: 15.8,
});

// 表格数据
const tableData = ref([
  [
    "交易日期",
    "交易编号",
    "交易类型",
    "交易金额",
    "账户",
    "对方账户",
    "交易分类",
    "备注",
  ],
  [
    "2024-05-15",
    "TR20240515001",
    "收入",
    120000,
    "工商银行基本户",
    "上海某贸易有限公司",
    "销售收入",
    "5月货款",
  ],
  [
    "2024-05-14",
    "TR20240514003",
    "支出",
    45000,
    "建设银行一般户",
    "北京某科技有限公司",
    "采购支出",
    "设备采购",
  ],
  [
    "2024-05-14",
    "TR20240514002",
    "收入",
    85000,
    "农业银行专用户",
    "广州某实业有限公司",
    "销售收入",
    "服务费",
  ],
  [
    "2024-05-13",
    "TR20240513005",
    "支出",
    32000,
    "工商银行基本户",
    "深圳某物流有限公司",
    "运输费用",
    "4月物流费",
  ],
  [
    "2024-05-12",
    "TR20240512001",
    "收入",
    150000,
    "建设银行一般户",
    "杭州某电子有限公司",
    "销售收入",
    "季度合同款",
  ],
  [
    "2024-05-10",
    "TR20240510004",
    "支出",
    78000,
    "工商银行基本户",
    "国家税务局",
    "税费支出",
    "4月增值税",
  ],
  [
    "2024-05-08",
    "TR20240508002",
    "收入",
    95000,
    "农业银行专用户",
    "成都某贸易有限公司",
    "销售收入",
    "产品销售",
  ],
  [
    "2024-05-07",
    "TR20240507003",
    "支出",
    42000,
    "建设银行一般户",
    "上海某人力资源公司",
    "人力成本",
    "员工社保",
  ],
  [
    "2024-05-05",
    "TR20240505001",
    "收入",
    110000,
    "工商银行基本户",
    "武汉某科技有限公司",
    "销售收入",
    "技术服务费",
  ],
  [
    "2024-05-03",
    "TR20240503002",
    "支出",
    65000,
    "农业银行专用户",
    "北京某广告有限公司",
    "市场费用",
    "广告投放",
  ],
  [
    "2024-05-02",
    "TR20240502001",
    "收入",
    130000,
    "建设银行一般户",
    "深圳某电子有限公司",
    "销售收入",
    "产品销售",
  ],
  [
    "2024-04-30",
    "TR20240430005",
    "支出",
    320000,
    "工商银行基本户",
    "员工工资账户",
    "人力成本",
    "4月工资",
  ],
  [
    "2024-04-28",
    "TR20240428003",
    "收入",
    180000,
    "农业银行专用户",
    "广州某实业有限公司",
    "销售收入",
    "项目款",
  ],
  [
    "2024-04-25",
    "TR20240425002",
    "支出",
    55000,
    "建设银行一般户",
    "上海某物业有限公司",
    "办公费用",
    "季度房租",
  ],
  [
    "2024-04-22",
    "TR20240422001",
    "收入",
    95000,
    "工商银行基本户",
    "杭州某科技有限公司",
    "销售收入",
    "服务费",
  ],
  [
    "2024-04-20",
    "TR20240420004",
    "支出",
    48000,
    "农业银行专用户",
    "北京某咨询有限公司",
    "咨询费用",
    "法律咨询",
  ],
  [
    "2024-04-18",
    "TR20240418002",
    "收入",
    135000,
    "建设银行一般户",
    "成都某贸易有限公司",
    "销售收入",
    "产品销售",
  ],
  [
    "2024-04-15",
    "TR20240415003",
    "支出",
    72000,
    "工商银行基本户",
    "深圳某科技有限公司",
    "采购支出",
    "设备维护",
  ],
  [
    "2024-04-12",
    "TR20240412001",
    "收入",
    160000,
    "农业银行专用户",
    "武汉某实业有限公司",
    "销售收入",
    "项目款",
  ],
]);

// 过滤后的表格数据
const filteredTableData = computed(() => {
  if (!searchKeyword.value) return tableData.value;

  const searchTerm = searchKeyword.value.toLowerCase();
  return tableData.value.filter((row, index) => {
    if (index === 0) return true; // 保留表头
    return row.some(
      (cell) => cell && cell.toString().toLowerCase().includes(searchTerm)
    );
  });
});

// 格式化数字
function formatNumber(num) {
  return new Intl.NumberFormat("zh-CN").format(num);
}

// 加载资金流动数据
function loadCapitalFlowData() {
  // 这里应该是从API获取数据，这里使用模拟数据
  ElMessage.success("数据加载成功");
}

// 处理日期变化
function handleDateChange() {
  loadCapitalFlowData();
}

// 搜索数据
function searchData() {
  loadCapitalFlowData();
  ElMessage.success("查询成功");
}

// 重置筛选条件
function resetFilters() {
  dateRange.value = [
    new Date(new Date().setMonth(new Date().getMonth() - 1)),
    new Date(),
  ];
  selectedAccount.value = "all";
  selectedFlowType.value = "all";
  searchKeyword.value = "";
  loadCapitalFlowData();
  ElMessage.success("已重置筛选条件");
}

// 导出数据
function exportData() {
  ElMessage.success("数据导出成功");
}

// 显示流动分析
function showFlowAnalysis() {
  ElMessage.info("正在生成资金流动分析报告...");
}

// 处理搜索
function handleSearch() {
  // 实时搜索，无需额外操作
}

// 处理分页大小变化
function handleSizeChange(size) {
  pageSize.value = size;
  loadCapitalFlowData();
}

// 处理页码变化
function handleCurrentChange(page) {
  currentPage.value = page;
  loadCapitalFlowData();
}

// 更新流动趋势图表
function updateFlowTrendChart() {
  initFlowTrendChart();
}

// 更新流动分类图表
function updateFlowCategoryChart() {
  initFlowCategoryChart();
}

// 初始化流动趋势图表
function initFlowTrendChart() {
  const chartDom = document.getElementById("flowTrendChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 模拟数据
  const dates = [
    "5/1",
    "5/2",
    "5/3",
    "5/4",
    "5/5",
    "5/6",
    "5/7",
    "5/8",
    "5/9",
    "5/10",
    "5/11",
    "5/12",
    "5/13",
    "5/14",
    "5/15",
  ];
  const inflowData = [
    120, 132, 101, 134, 90, 80, 120, 110, 125, 145, 122, 165, 122, 110, 120,
  ];
  const outflowData = [
    80, 100, 90, 120, 110, 60, 70, 90, 100, 110, 80, 120, 100, 90, 95,
  ];
  const netFlowData = dates.map(
    (_, index) => inflowData[index] - outflowData[index]
  );

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["流入", "流出", "净流动"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: dates,
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "{value}万",
      },
    },
    series: [
      {
        name: "流入",
        type: "bar",
        stack: "total",
        emphasis: {
          focus: "series",
        },
        data: inflowData,
        itemStyle: {
          color: "#67C23A",
        },
      },
      {
        name: "流出",
        type: "bar",
        stack: "total",
        emphasis: {
          focus: "series",
        },
        data: outflowData.map((val) => -val),
        itemStyle: {
          color: "#F56C6C",
        },
      },
      {
        name: "净流动",
        type: "line",
        emphasis: {
          focus: "series",
        },
        data: netFlowData,
        itemStyle: {
          color: "#409EFF",
        },
      },
    ],
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}

// 初始化流动分类图表
function initFlowCategoryChart() {
  const chartDom = document.getElementById("flowCategoryChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 模拟数据
  let data = [];
  if (chartFlowType.value === "inflow") {
    data = [
      { value: 40, name: "销售收入" },
      { value: 20, name: "投资收益" },
      { value: 15, name: "利息收入" },
      { value: 10, name: "租金收入" },
      { value: 15, name: "其他收入" },
    ];
  } else {
    data = [
      { value: 30, name: "采购支出" },
      { value: 25, name: "人力成本" },
      { value: 15, name: "税费支出" },
      { value: 10, name: "办公费用" },
      { value: 8, name: "市场费用" },
      { value: 12, name: "其他支出" },
    ];
  }

  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c}万 ({d}%)",
    },
    legend: {
      orient: "vertical",
      right: 10,
      top: "center",
      data: data.map((item) => item.name),
    },
    series: [
      {
        name: chartFlowType.value === "inflow" ? "流入分类" : "流出分类",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "18",
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: data,
        color:
          chartFlowType.value === "inflow"
            ? ["#67C23A", "#85CE61", "#95D475", "#B3E19D", "#D1EBBE"]
            : [
                "#F56C6C",
                "#F78989",
                "#F9A7A7",
                "#FACACA",
                "#FCE2E2",
                "#FDF1F1",
              ],
      },
    ],
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}

// 新增变量
const totalInflow = ref(560000)
const totalOutflow = ref(420000)
const netFlow = computed(() => totalInflow.value - totalOutflow.value)
const inflowTrend = ref(12.5)
const outflowTrend = ref(-5.2)
const turnoverRate = ref(3.2)
const turnoverTrend = ref(8.7)

// 账户余额数据
const accountBalanceData = ref([
  ['账户名称', '账户类型', '账户余额', '可用余额', '冻结金额', '更新时间'],
  ['工商银行基本户', '基本户', 1250000, 1200000, 50000, '2024-05-15 10:30'],
  ['建设银行一般户', '一般户', 850000, 850000, 0, '2024-05-15 10:30'],
  ['农业银行专用户', '专用户', 420000, 380000, 40000, '2024-05-15 10:30'],
  ['招商银行外汇户', '外汇户', 320000, 320000, 0, '2024-05-15 10:30'],
  ['浦发银行备付金', '备付金', 180000, 180000, 0, '2024-05-15 10:30'],
  ['合计', '', 3020000, 2930000, 90000, '']
])

// 资金预测数据
const forecastPeriod = ref('30')
const forecastData = ref([
  ['预计日期', '交易类型', '预计金额', '交易对象', '交易事由', '确定性'],
  ['2024-05-20', '收入', 200000, '上海某贸易有限公司', '合同款', '高'],
  ['2024-05-25', '支出', 320000, '员工工资账户', '5月工资', '高'],
  ['2024-05-28', '支出', 85000, '国家税务局', '4月增值税', '高'],
  ['2024-06-05', '收入', 150000, '广州某实业有限公司', '项目款', '中'],
  ['2024-06-10', '支出', 65000, '深圳某物流有限公司', '物流费', '中'],
  ['2024-06-15', '收入', 280000, '杭州某电子有限公司', '合同款', '低'],
  ['2024-06-20', '支出', 45000, '上海某物业有限公司', '季度房租', '高'],
  ['2024-06-25', '支出', 320000, '员工工资账户', '6月工资', '高']
])

// 资金风险预警
const enableRiskAlert = ref(true)
const riskAlertData = ref([
  {
    level: '高风险',
    type: '资金短缺',
    description: '6月25日工资支付可能导致资金短缺',
    impact: 120000,
    date: '2024-06-25'
  },
  {
    level: '中风险',
    type: '应收账款逾期',
    description: '上海某贸易有限公司应收款逾期30天',
    impact: 85000,
    date: '2024-05-20'
  },
  {
    level: '低风险',
    type: '汇率波动',
    description: '美元汇率波动可能影响外汇账户价值',
    impact: 25000,
    date: '2024-06-10'
  }
])

// 获取风险等级对应的标签类型
const getRiskLevelType = (level) => {
  switch (level) {
    case '高风险': return 'danger'
    case '中风险': return 'warning'
    case '低风险': return 'info'
    default: return 'info'
  }
}

// 刷新账户余额
const refreshAccountBalance = () => {
  ElMessage.success('账户余额已更新')
  // 模拟数据更新
  accountBalanceData.value[1][2] = 1280000
  accountBalanceData.value[1][3] = 1230000
  accountBalanceData.value[5][2] = 3050000
  accountBalanceData.value[5][3] = 2960000
}

// 生成资金预测
const generateForecast = () => {
  ElMessage.success(`已生成未来${forecastPeriod.value}天的资金预测`)
  initForecastChart()
}

// 初始化资金预测图表
function initForecastChart() {
  const chartDom = document.getElementById('forecastChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  // 生成日期数组
  const today = new Date()
  const dates = []
  for (let i = 0; i < parseInt(forecastPeriod.value); i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)
    dates.push(`${date.getMonth() + 1}/${date.getDate()}`)
  }
  
  // 生成预测数据
  const balanceData = [3020000] // 起始余额
  const inflowData = Array(dates.length).fill(0)
  const outflowData = Array(dates.length).fill(0)
  
  // 填充预测数据
  forecastData.value.slice(1).forEach(row => {
    const date = new Date(row[0])
    const dayDiff = Math.floor((date - today) / (1000 * 60 * 60 * 24))
    if (dayDiff >= 0 && dayDiff < dates.length) {
      if (row[1] === '收入') {
        inflowData[dayDiff] += parseInt(row[2])
      } else {
        outflowData[dayDiff] += parseInt(row[2])
      }
    }
  })
  
  // 计算每日余额
  for (let i = 0; i < dates.length; i++) {
    if (i > 0) {
      balanceData[i] = balanceData[i-1] + inflowData[i-1] - outflowData[i-1]
    }
  }
  
  const option = {
    title: {
      text: `未来${forecastPeriod.value}天资金预测`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params) {
        let result = params[0].name + '<br/>'
        params.forEach(param => {
          const value = param.seriesName === '预计余额' 
            ? formatNumber(param.value)
            : formatNumber(Math.abs(param.value))
          result += `${param.marker} ${param.seriesName}: ¥${value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['预计余额', '预计流入', '预计流出'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates
    },
    yAxis: [
      {
        type: 'value',
        name: '余额',
        position: 'left',
        axisLabel: {
          formatter: (value) => `${value/10000}万`
        }
      },
      {
        type: 'value',
        name: '流入/流出',
        position: 'right',
        axisLabel: {
          formatter: (value) => `${Math.abs(value)/10000}万`
        }
      }
    ],
    series: [
      {
        name: '预计余额',
        type: 'line',
        smooth: true,
        emphasis: { focus: 'series' },
        data: balanceData,
        itemStyle: { color: '#409EFF' },
        markLine: {
          data: [
            {
              name: '警戒线',
              yAxis: 1000000,
              lineStyle: { color: '#F56C6C' },
              label: { formatter: '警戒线: 100万' }
            }
          ]
        }
      },
      {
        name: '预计流入',
        type: 'bar',
        yAxisIndex: 1,
        emphasis: { focus: 'series' },
        data: inflowData,
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '预计流出',
        type: 'bar',
        yAxisIndex: 1,
        emphasis: { focus: 'series' },
        data: outflowData.map(v => -v),
        itemStyle: { color: '#F56C6C' }
      }
    ]
  }
  
  myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
}

// 页面加载时初始化
onMounted(() => {
  // 设置表格宽度
  tableWidth.value =
    document.querySelector(".capital-flow-container").offsetWidth - 40;

  // 初始化图表
  initFlowTrendChart();
  initFlowCategoryChart();
  initForecastChart();

  // 模拟数据加载
  totalRecords.value = 156;

  // 监听窗口大小变化
  window.addEventListener("resize", () => {
    tableWidth.value =
      document.querySelector(".capital-flow-container").offsetWidth - 40;
  });
});

// 监听预测周期变化
watch(forecastPeriod, () => {
  generateForecast()
})
</script>

<style scoped>
.capital-flow-container {
  width: 100%;
  height: 100%;
  padding: 24px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow-y: scroll;
  overflow-x: hidden;
}

h1 {
  margin-top: 0;
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 500;
  color: #303133;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.filter-section {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-section {
  display: flex;
  gap: 12px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-icon.inflow {
  background-color: #67c23a;
}

.card-icon.outflow {
  background-color: #f56c6c;
}

.card-icon.net-flow {
  background-color: #409eff;
}

.card-icon.balance {
  background-color: #e6a23c;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.card-value {
  font-size: 20px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.card-change {
  font-size: 12px;
  color: #909399;
}

.card-change.positive {
  color: #67c23a;
}

.card-change.negative {
  color: #f56c6c;
}

.chart-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.chart-container {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.chart {
  height: 300px;
}

.table-section {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.table-actions {
  width: 240px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-section {
    margin-bottom: 16px;
    width: 100%;
  }

  .action-section {
    width: 100%;
  }

  .chart-section {
    grid-template-columns: 1fr;
  }
}
</style>
