<script setup>
import { RouterLink, RouterView } from "vue-router";
import { computed, onMounted, watch, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { ElIcon } from "element-plus";

const router = useRouter();
const routes = computed(() => {
  const filteredRoutes = router.options.routes.filter((route) => route.meta);
  console.log("Available routes:", router.options.routes);
  console.log("Filtered routes:", filteredRoutes);
  return filteredRoutes;
});

onMounted(() => {
  console.log("Router options:", router.options);

  // 监听窗口大小变化，调整布局
  window.addEventListener("resize", handleResize);

  // 初始调整
  handleResize();
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 处理窗口大小变化
const handleResize = () => {
  // 确保内容容器高度适应窗口
  const contentWrapper = document.querySelector(".content-wrapper");
  if (contentWrapper) {
    contentWrapper.style.minHeight = `${window.innerHeight - 48}px`;
  }

  // 确保app容器填满视口
  const appContainer = document.querySelector(".app-container");
  if (appContainer) {
    appContainer.style.minHeight = `${window.innerHeight}px`;
  }
};

// 监听路由变化，确保滚动条重置
watch(
  () => router.currentRoute.value,
  () => {
    window.scrollTo(0, 0);

    // 在路由变化后，给DOM一点时间更新，然后再检查布局
    setTimeout(() => {
      handleResize();
    }, 100);
  }
);
</script>

<template>
  <div class="app-container">
    <!-- Sidebar Navigation -->
    <div class="sidebar">
      <div class="logo-container">
        <h3 class="app-logo">数据看板</h3>
        <div class="user-role">信小财</div>
        <!-- <div class="logo-subtitle">智慧财务共享中心</div> -->
      </div>

      <nav class="sidebar-nav">
        <RouterLink
          v-for="route in routes"
          :key="route.path"
          :to="route.path"
          class="nav-item"
          active-class="active"
        >
          <el-icon class="nav-icon"
            ><component :is="route.meta.icon"
          /></el-icon>
          <span class="nav-text">{{ route.meta.title }}</span>
        </RouterLink>
      </nav>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <div class="content-wrapper">
        <RouterView />
      </div>
    </div>
  </div>
</template>

<style>
/* Global CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Global font */
html {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  color: #374151;
  line-height: 1.5;
  width: 100%; /* Ensure html takes full width */
  height: 100%; /* Ensure html takes full height */
}

body {
  width: 100%; /* Ensure body takes full width */
  height: 100%; /* Ensure body takes full height */
  background-color: #f9fafb;
  overflow-x: auto; /* 改为auto，允许水平滚动 */
}

/* App Container */
.app-container {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: visible; /* 改为visible，允许内容溢出 */
}

/* Sidebar Styles */
.sidebar {
  width: 240px;
  min-width: 240px;
  background: linear-gradient(135deg, #1967d2, #4285f4);
  color: #5f6368;
  border-right: 1px solid #e0e0e0;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  z-index: 10;
  overflow-y: auto; /* 允许侧边栏滚动 */
}

.logo-container {
  padding: 16px 24px 0 24px;
  border-bottom: none;
}

.app-logo {
  color: #fff;
  font-size: 1.4rem;
}

.logo-subtitle {
  font-size: 1.1rem;
  color: #9ca3af;
}

.nav-item {
  padding: 12px 24px;
  color: #fff;
  border-radius: 0 25px 25px 0;
  margin: 0 8px;
  border-left: none;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.2rem;
}

.sidebar-nav {
  flex-grow: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  padding-top: 12px;
}

.nav-item:hover {
  background-color: #f1f3f4;
  color: #1967d2;
  transform: translateY(-5px);
}

.nav-item.active {
  background-color: #e8f0fe;
  color: #1967d2;
  font-weight: 500;
}

.nav-icon {
  font-size: 18px;
  margin-right: 8px;
  color: rgba(255, 255, 255, 0.9);
  transition: transform 0.3s ease, color 0.3s ease;
}

.nav-item:hover .nav-icon {
  transform: scale(1.4);
  color: #ffffff;
}

.nav-item.active .nav-icon {
  color: #1967d2;
}

.nav-item.active:hover .nav-icon {
  transform: scale(1.4);
  color: #1967d2;
}

.sidebar-footer {
  border-top: 1px solid #e0e0e0;
  /* background-color: #f8f9fa;
   */
  background: linear-gradient(135deg, #1967d2, #4285f4);
  margin-top: auto;
  padding: 16px 24px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 0.9rem;
  font-weight: 500;
}

.user-role {
  font-size: 1.1rem;
  color: #fff;
}

/* Main Content Styles */
.main-content {
  margin-left: 240px;
  width: calc(100% - 240px);
  min-height: 100vh;
  background-color: #f9fafb;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: visible; /* 改为visible，允许内容溢出 */
}

.content-wrapper {
  /* padding: 24px; */
  width: 100%;
  height: auto; /* 改为auto，自动适应内容高度 */
  min-height: calc(100vh - 48px); /* 确保至少有视口高度减去padding */
  flex: 1;
  display: flex;
  position: relative;
  overflow: auto;
}

/* Target the root element of the component rendered by RouterView */
.content-wrapper > :deep(*) {
  width: 100%;
  height: auto; /* 改为auto，自动适应内容高度 */
  max-width: none;
  max-height: none;
  flex: 1;
  overflow: visible;
}

/* 添加针对移动设备的响应式优化 */
@media (max-width: 1024px) {
  .main-content {
    width: 100%;
    margin-left: 0;
  }

  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }
}

/* 调整body样式，确保滚动正常 */
body {
  width: 100%; /* Ensure body takes full width */
  height: 100%; /* Ensure body takes full height */
  background-color: #f9fafb;
  overflow-x: auto; /* 改为auto，允许水平滚动 */
}
.sidebar-nav {
  .nav-icon::before {
    content: "";
    display: inline-block;
    width: 30px;
    height: 30px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    vertical-align: middle;
  }
}

/* 更新图标为Material风格 */
.fa-chart-bar::before,
.fa-money-bill::before,
.fa-database::before,
.fa-cogs::before,
.fa-search::before,
.fa-exchange-alt::before,
.fa-file-alt::before,
.fa-calculator::before,
.fa-folder::before {
  background-image: none;
}

/* 移除旧的图标样式 */
.sidebar-nav .nav-icon::before {
  content: none;
  background-image: none;
}

@media (min-width: 1024px) {
  body {
    margin: 0;
    padding: 0;
  }

  #app {
    width: 100%;
    height: auto; /* 改为auto，而不是固定的100vh */
    min-height: 100vh; /* 确保至少填满视口高度 */
  }
}
</style>
