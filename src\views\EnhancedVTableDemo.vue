<template>
  <div class="enhanced-vtable-demo">
    <h1>增强版VTable功能演示</h1>
    
    <div class="demo-section">
      <h2>🚀 功能特性</h2>
      <div class="features-grid">
        <div class="feature-card">
          <h3>✏️ 单元格编辑</h3>
          <p>双击单元格即可编辑，支持文本、数字、日期等类型</p>
        </div>
        <div class="feature-card">
          <h3>📋 复制粘贴</h3>
          <p>支持Ctrl+C/Ctrl+V快捷键，或使用工具栏按钮</p>
        </div>
        <div class="feature-card">
          <h3>📏 自动列宽</h3>
          <p>根据内容自动调整列宽，也可手动拖拽调整</p>
        </div>
        <div class="feature-card">
          <h3>🔍 智能筛选</h3>
          <p>支持按列筛选和全局搜索，回车快速应用</p>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>📊 演示表格</h2>
      <p>这个表格包含了各种数据类型，演示了所有增强功能：</p>
      
      <VTableComponent
        ref="tableRef"
        :data="demoData"
        :width="1000"
        :height="400"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
        @copy="handleCopy"
        @paste="handlePaste"
      />
    </div>

    <div class="demo-section">
      <h2>🎮 操作演示</h2>
      <div class="controls">
        <button @click="addRow" class="demo-btn">➕ 添加行</button>
        <button @click="removeLastRow" class="demo-btn">➖ 删除最后一行</button>
        <button @click="exportData" class="demo-btn">💾 导出数据</button>
        <button @click="autoFitColumns" class="demo-btn">📏 自适应列宽</button>
        <button @click="selectRandomCell" class="demo-btn">🎯 随机选择单元格</button>
      </div>
    </div>

    <div class="demo-section">
      <h2>📝 使用说明</h2>
      <div class="instructions">
        <h3>编辑操作：</h3>
        <ul>
          <li>双击任意单元格开始编辑</li>
          <li>按Enter确认编辑，按Esc取消编辑</li>
          <li>日期列会显示日期选择器</li>
          <li>状态列会显示下拉选择框</li>
        </ul>
        
        <h3>复制粘贴：</h3>
        <ul>
          <li>选中单元格后按Ctrl+C复制</li>
          <li>选中目标位置按Ctrl+V粘贴</li>
          <li>也可以使用工具栏的复制粘贴按钮</li>
          <li>支持跨应用程序复制粘贴（如Excel）</li>
        </ul>
        
        <h3>列宽调整：</h3>
        <ul>
          <li>鼠标悬停在列边界可拖拽调整宽度</li>
          <li>点击"自适应列宽"按钮自动调整所有列</li>
          <li>启用autoWidth时会根据内容自动计算列宽</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h2>📊 数据监控</h2>
      <div class="data-monitor">
        <h3>当前表格数据：</h3>
        <pre>{{ JSON.stringify(currentData, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import VTableComponent from '@/components/VTableComponent.vue'

// 表格引用
const tableRef = ref()

// 演示数据
const demoData = ref([
  ['ID', '姓名', '年龄', '薪资', '入职日期', '部门', '状态', '备注'],
  [1, '张三', 28, 8000, '2024-01-15', '研发部', '在职', '高级工程师'],
  [2, '李四', 32, 12000, '2023-06-20', '市场部', '在职', '市场经理'],
  [3, '王五', 25, 9000, '2024-03-10', '财务部', '在职', '财务专员'],
  [4, '赵六', 30, 15000, '2022-08-05', '技术部', '在职', '技术总监'],
  [5, '钱七', 26, 7500, '2024-05-12', '人事部', '试用期', '人事专员'],
  [6, '孙八', 35, 18000, '2021-12-01', '销售部', '在职', '销售总监'],
  [7, '周九', 29, 10500, '2023-09-18', '运营部', '在职', '运营经理'],
  [8, '吴十', 27, 8500, '2024-02-28', '设计部', '在职', 'UI设计师']
])

// 当前数据状态
const currentData = ref([...demoData.value])

// 数据变化处理
const handleDataChange = (newData) => {
  console.log('表格数据变化:', newData)
  currentData.value = newData
  demoData.value = newData
}

// 单元格编辑处理
const handleCellEdit = (editInfo) => {
  console.log('单元格编辑:', editInfo)
}

// 复制处理
const handleCopy = (data) => {
  console.log('复制数据:', data)
}

// 粘贴处理
const handlePaste = (data) => {
  console.log('粘贴数据:', data)
}

// 添加行
const addRow = () => {
  const newId = demoData.value.length
  const newRow = [
    newId,
    `新员工${newId}`,
    25,
    8000,
    new Date().toISOString().split('T')[0],
    '新部门',
    '试用期',
    '新员工备注'
  ]
  
  const newData = [...demoData.value]
  newData.push(newRow)
  demoData.value = newData
  
  if (tableRef.value) {
    tableRef.value.setData(newData)
  }
}

// 删除最后一行
const removeLastRow = () => {
  if (demoData.value.length > 1) {
    const newData = [...demoData.value]
    newData.pop()
    demoData.value = newData
    
    if (tableRef.value) {
      tableRef.value.setData(newData)
    }
  }
}

// 导出数据
const exportData = () => {
  if (tableRef.value) {
    const data = tableRef.value.getData()
    console.log('导出的数据:', data)
    alert('数据已导出到控制台，实际应用中会下载CSV文件')
  }
}

// 自适应列宽
const autoFitColumns = () => {
  if (tableRef.value) {
    tableRef.value.autoFitAllColumnWidth()
  }
}

// 随机选择单元格
const selectRandomCell = () => {
  if (tableRef.value && demoData.value.length > 1) {
    const row = Math.floor(Math.random() * (demoData.value.length - 1))
    const col = Math.floor(Math.random() * demoData.value[0].length)
    tableRef.value.selectCell(row, col)
  }
}
</script>

<style scoped>
.enhanced-vtable-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.feature-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.feature-card h3 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.feature-card p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin: 20px 0;
}

.demo-btn {
  padding: 10px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.demo-btn:hover {
  background: #66b1ff;
  transform: translateY(-1px);
}

.instructions {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.instructions h3 {
  color: #409eff;
  margin: 0 0 10px 0;
}

.instructions ul {
  margin: 0 0 20px 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 5px;
  line-height: 1.5;
}

.data-monitor {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.data-monitor h3 {
  margin: 0 0 15px 0;
  color: #409eff;
}

.data-monitor pre {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  color: #409eff;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}
</style>
