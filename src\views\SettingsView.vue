<template>
  <div class="settings-view-container">
    <el-tabs v-model="activeTab" class="settings-tabs">
      <el-tab-pane label="SAP配置" name="sapConfig">
        <div class="tab-content-wrapper">
          <el-card class="box-card sap-connection-card">
            <template #header>
              <div class="card-header">
                <span>SAP连接设置</span>
              </div>
            </template>
            <el-form
              :model="sapConnectionForm"
              label-position="top"
              ref="sapConnectionFormRef"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="SAP路径地址" prop="path">
                    <el-input
                      v-model="sapConnectionForm.path"
                      placeholder="例如：/H/192.168.1.100/S/3200"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="账套名" prop="accountBook">
                    <el-input
                      v-model="sapConnectionForm.accountBook"
                      placeholder="请输入账套名"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="SAP用户名" prop="username">
                    <el-input
                      v-model="sapConnectionForm.username"
                      placeholder="请输入SAP用户名"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="SAP密码" prop="password">
                    <el-input
                      v-model="sapConnectionForm.password"
                      type="password"
                      placeholder="请输入SAP密码"
                      show-password
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item>
                <el-button type="primary" @click="saveSapConnectionSettings"
                  >保存连接设置</el-button
                >
              </el-form-item>
            </el-form>
          </el-card>

          <el-card class="box-card sap-profitcenter-card">
            <template #header>
              <div class="card-header">
                <span>利润中心组配置</span>
                <div>
                  <el-button
                    type="primary"
                    @click="openPasteDialogForSapProfitCenter"
                    :icon="ElIconDocumentCopy"
                    circle
                    title="粘贴数据"
                  ></el-button>
                  <el-button
                    type="success"
                    @click="addProfitCenterRow"
                    :icon="ElIconPlus"
                    circle
                    title="添加行"
                    style="margin-left: 10px"
                  ></el-button>
                </div>
              </div>
            </template>
            <el-table
              :data="sapProfitCenterTableData"
              style="width: 100%"
              border
            >
              <el-table-column
                prop="profitCenterGroup"
                label="利润中心组"
                min-width="180"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.profitCenterGroup"
                    placeholder="利润中心组"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                prop="companyCode"
                label="公司编码"
                min-width="150"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.companyCode"
                    placeholder="公司编码"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                prop="profitCenterGroupName"
                label="利润中心组名称"
                min-width="200"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.profitCenterGroupName"
                    placeholder="利润中心组名称"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope">
                  <el-button
                    type="danger"
                    @click="removeProfitCenterRow(scope.$index)"
                    :icon="ElIconDelete"
                    circle
                    title="删除行"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right">
              <el-button type="primary" @click="saveSapProfitCenterConfig"
                >保存利润中心配置</el-button
              >
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="自动制证配置" name="autoVoucherConfig">
        <div class="tab-content-wrapper">
          <el-card class="box-card excluded-profit-centers-card">
            <template #header>
              <div class="card-header">
                <span>排除项目或单位</span>
                <div>
                  <el-button
                    type="primary"
                    @click="openPasteDialogForExcludedProfitCenters"
                    :icon="ElIconDocumentCopy"
                    circle
                    title="粘贴数据"
                  ></el-button>
                  <el-button
                    type="success"
                    @click="addExcludedProfitCenterRow"
                    :icon="ElIconPlus"
                    circle
                    title="添加行"
                    style="margin-left: 10px"
                  ></el-button>
                </div>
              </div>
            </template>
            <el-table
              :data="excludedProfitCentersTableData"
              style="width: 100%"
              border
            >
              <el-table-column
                prop="description"
                label="项目名称或单位名称"
                min-width="250"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.description"
                    placeholder="请输入项目名称或单位名称"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                prop="isEnabled"
                label="是否启用"
                width="100"
                align="center"
              >
                <template #default="scope">
                  <el-switch
                    v-model="scope.row.isEnabled"
                    active-text="是"
                    inactive-text="否"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope">
                  <el-button
                    type="danger"
                    @click="removeExcludedProfitCenterRow(scope.$index)"
                    :icon="ElIconDelete"
                    circle
                    title="删除行"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right">
              <el-button type="primary" @click="saveExcludedProfitCenters"
                >保存排除项目或单位</el-button
              >
            </div>
          </el-card>

          <el-card class="box-card document-types-card">
            <template #header>
              <div class="card-header">
                <span>单据类型配置</span>
                <div>
                  <el-button
                    type="primary"
                    @click="openPasteDialogForDocumentTypes"
                    :icon="ElIconDocumentCopy"
                    circle
                    title="粘贴数据"
                  ></el-button>
                  <el-button
                    type="success"
                    @click="addDocumentTypeRow"
                    :icon="ElIconPlus"
                    circle
                    title="添加行"
                    style="margin-left: 10px"
                  ></el-button>
                </div>
              </div>
            </template>
            <el-table :data="documentTypesTableData" style="width: 100%" border>
              <el-table-column
                prop="documentTypeDescription"
                label="单据类型描述"
                min-width="250"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.documentTypeDescription"
                    placeholder="请输入单据类型描述"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                prop="isEnabled"
                label="是否启用"
                width="100"
                align="center"
              >
                <template #default="scope">
                  <el-switch
                    v-model="scope.row.isEnabled"
                    active-text="是"
                    inactive-text="否"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope">
                  <el-button
                    type="danger"
                    @click="removeDocumentTypeRow(scope.$index)"
                    :icon="ElIconDelete"
                    circle
                    title="删除行"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right">
              <el-button type="primary" @click="saveDocumentTypes"
                >保存单据类型</el-button
              >
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="财务一体化配置" name="financialIntegrationConfig">
        <div class="tab-content-wrapper">
          <el-card class="box-card financial-integration-card">
            <template #header>
              <div class="card-header">
                <span>单位配置</span>
                <div>
                  <el-button
                    type="primary"
                    @click="openPasteDialogForUnits"
                    :icon="ElIconDocumentCopy"
                    circle
                    title="粘贴数据"
                  ></el-button>
                  <el-button
                    type="success"
                    @click="addUnitRow"
                    :icon="ElIconPlus"
                    circle
                    title="添加单位"
                    style="margin-left: 10px"
                  ></el-button>
                </div>
              </div>
            </template>
            <el-table
              :data="financialIntegrationUnitsTableData"
              style="width: 100%"
              border
            >
              <el-table-column prop="unitName" label="单位名称" min-width="300">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.unitName"
                    placeholder="请输入单位名称"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="unitCode" label="单位编码" min-width="200">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.unitCode"
                    placeholder="请输入单位编码"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope">
                  <el-button
                    type="danger"
                    @click="removeUnitRow(scope.$index)"
                    :icon="ElIconDelete"
                    circle
                    title="删除单位"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right">
              <el-button type="primary" @click="saveFinancialIntegrationUnits"
                >保存单位配置</el-button
              >
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="财商配置" name="financialQuotientConfig">
        <div class="tab-content-wrapper">
          <el-card class="box-card fq-user-config-card">
            <template #header>
              <div class="card-header">
                <span>用户凭证设置</span>
              </div>
            </template>
            <el-form
              :model="fqUserForm"
              label-position="top"
              ref="fqUserFormRef"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="用户名" prop="username">
                    <el-input
                      v-model="fqUserForm.username"
                      placeholder="请输入财商用户名"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="密码" prop="password">
                    <el-input
                      v-model="fqUserForm.password"
                      type="password"
                      placeholder="请输入财商密码"
                      show-password
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item>
                <el-button type="primary" @click="saveFqUserSettings"
                  >保存用户凭证</el-button
                >
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="计算配置" name="calculationConfig">
        <div class="tab-content-wrapper">
          <!-- 单位管理映射表 -->
          <el-card class="box-card unit-mapping-card">
            <template #header>
              <div class="card-header">
                <span>单位管理映射表</span>
                <div>
                  <el-button
                    type="primary"
                    @click="openPasteDialogForUnitMapping"
                    :icon="ElIconDocumentCopy"
                    circle
                    title="粘贴数据"
                  ></el-button>
                  <el-button
                    type="success"
                    @click="addUnitMapRow"
                    :icon="ElIconPlus"
                    circle
                    title="添加映射"
                    style="margin-left: 10px"
                  ></el-button>
                </div>
              </div>
            </template>
            <el-table :data="unitMappingTableData" style="width: 100%" border>
              <el-table-column
                prop="sourceUnitName"
                label="源单位名称"
                min-width="350"
              >
                <template #default="scope"
                  ><el-input
                    v-model="scope.row.sourceUnitName"
                    placeholder="源单位名称"
                  ></el-input
                ></template>
              </el-table-column>
              <el-table-column
                prop="sourceUnitCode"
                label="源单位编码"
                min-width="350"
              >
                <template #default="scope"
                  ><el-input
                    v-model="scope.row.sourceUnitCode"
                    placeholder="源单位编码"
                  ></el-input
                ></template>
              </el-table-column>
              <el-table-column
                prop="targetUnitName"
                label="目标单位名称"
                min-width="180"
              >
                <template #default="scope"
                  ><el-input
                    v-model="scope.row.targetUnitName"
                    placeholder="目标单位名称"
                  ></el-input
                ></template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope"
                  ><el-button
                    type="danger"
                    @click="removeUnitMapRow(scope.$index)"
                    :icon="ElIconDelete"
                    circle
                    title="删除映射"
                  ></el-button
                ></template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right">
              <el-button type="primary" @click="saveUnitMappingTable"
                >保存单位映射</el-button
              >
            </div>
          </el-card>

          <!-- SAP费用划转科目表 -->
          <el-card class="box-card expense-transfer-card">
            <template #header>
              <div class="card-header">
                <span>SAP费用划转科目表</span>
                <div>
                  <el-button
                    type="primary"
                    @click="openPasteDialogForExpenseTransfer"
                    :icon="ElIconDocumentCopy"
                    circle
                    title="粘贴数据"
                  ></el-button>
                  <el-button
                    type="success"
                    @click="addExpenseTransferRow"
                    :icon="ElIconPlus"
                    circle
                    title="添加科目"
                    style="margin-left: 10px"
                  ></el-button>
                </div>
              </div>
            </template>
            <el-table
              :data="expenseTransferTableData"
              style="width: 100%"
              border
            >
              <el-table-column
                prop="description"
                label="划转费用科目名称"
                min-width="200"
              >
                <template #default="scope"
                  ><el-input
                    v-model="scope.row.description"
                    placeholder="描述"
                  ></el-input
                ></template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope"
                  ><el-button
                    type="danger"
                    @click="removeExpenseTransferRow(scope.$index)"
                    :icon="ElIconDelete"
                    circle
                    title="删除科目"
                  ></el-button
                ></template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right">
              <el-button type="primary" @click="saveExpenseTransferTable"
                >保存费用科目</el-button
              >
            </div>
          </el-card>

          <!-- 内部往来挂账上级客商表 -->
          <el-card class="box-card internal-customer-card">
            <template #header>
              <div class="card-header">
                <span>内部往来挂账上级客商表</span>
                <div>
                  <el-button
                    type="primary"
                    @click="openPasteDialogForInternalCustomer"
                    :icon="ElIconDocumentCopy"
                    circle
                    title="粘贴数据"
                  ></el-button>
                  <el-button
                    type="success"
                    @click="addInternalCustomerRow"
                    :icon="ElIconPlus"
                    circle
                    title="添加客商"
                    style="margin-left: 10px"
                  ></el-button>
                </div>
              </div>
            </template>
            <el-table
              :data="internalCustomerTableData"
              style="width: 100%"
              border
            >
              <el-table-column
                prop="superiorCustomerCode"
                label="上级客商编码"
                min-width="180"
              >
                <template #default="scope"
                  ><el-input
                    v-model="scope.row.superiorCustomerCode"
                    placeholder="上级客商编码"
                  ></el-input
                ></template>
              </el-table-column>
              <el-table-column
                prop="superiorCustomerName"
                label="上级客商名称"
                min-width="200"
              >
                <template #default="scope"
                  ><el-input
                    v-model="scope.row.superiorCustomerName"
                    placeholder="上级客商名称"
                  ></el-input
                ></template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope"
                  ><el-button
                    type="danger"
                    @click="removeInternalCustomerRow(scope.$index)"
                    :icon="ElIconDelete"
                    circle
                    title="删除客商"
                  ></el-button
                ></template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right">
              <el-button type="primary" @click="saveInternalCustomerTable"
                >保存内部客商</el-button
              >
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import {
  Plus as ElIconPlus,
  Delete as ElIconDelete,
  DocumentCopy as ElIconDocumentCopy,
} from "@element-plus/icons-vue";

// Function to fetch all configurations
const fetchAllConfigs = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: "加载配置中...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    const response = await fetch("http://localhost:8000/api/get-all-configs", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error("获取配置失败");
    }

    const configs = await response.json();

    // Process the configs to handle numeric values that should not be converted to boolean
    const processConfigData = (data) => {
      if (!data || typeof data !== "object") return data;

      // If it's an array, process each item
      if (Array.isArray(data)) {
        return data.map((item) => processConfigData(item));
      }

      // For objects, check each property
      const result = { ...data };
      for (const key in result) {
        if (result.hasOwnProperty(key)) {
          // If the key is 'isEnabled' or ends with 'Enabled' and the value is boolean true
          // but might have been a number 1 originally, convert it back to number 1
          if (
            (key === "isEnabled" || key.endsWith("Enabled")) &&
            result[key] === 1
          ) {
            // We'll use 1 as the numeric representation of true
            result[key] = true;
          } else if (typeof result[key] === "object") {
            // Recursively process nested objects
            result[key] = processConfigData(result[key]);
          }
        }
      }
      return result;
    };

    // Process all config sections
    const processedConfigs = processConfigData(configs);

    // Update each configuration section with the processed data
    if (processedConfigs.sapConnection) {
      Object.assign(sapConnectionForm, processedConfigs.sapConnection);
    }
    if (Array.isArray(processedConfigs.sapProfitCenter)) {
      sapProfitCenterTableData.value = processedConfigs.sapProfitCenter;
    }
    if (Array.isArray(processedConfigs.excludedProfitCenters)) {
      excludedProfitCentersTableData.value =
        processedConfigs.excludedProfitCenters;
    }
    if (Array.isArray(processedConfigs.documentTypes)) {
      documentTypesTableData.value = processedConfigs.documentTypes;
    }
    if (Array.isArray(processedConfigs.financialIntegrationUnits)) {
      financialIntegrationUnitsTableData.value =
        processedConfigs.financialIntegrationUnits;
    }
    if (processedConfigs.fqUserSettings) {
      fqUserForm.username = processedConfigs.fqUserSettings.username || "";
      fqUserForm.password = processedConfigs.fqUserSettings.password || "";
    }
    if (Array.isArray(processedConfigs.unitMapping)) {
      unitMappingTableData.value = processedConfigs.unitMapping;
    }
    if (Array.isArray(processedConfigs.expenseTransfer)) {
      expenseTransferTableData.value = processedConfigs.expenseTransfer;
    }
    if (Array.isArray(processedConfigs.internalCustomers)) {
      internalCustomerTableData.value = processedConfigs.internalCustomers.map(
        (item) => ({
          superiorCustomerCode:
            item.superior_customer_code || item.superiorCustomerCode || "",
          superiorCustomerName:
            item.superior_customer_name || item.superiorCustomerName || "",
        })
      );
    }

    ElMessage.success("配置加载成功！");
  } catch (error) {
    console.error("加载配置失败:", error);
    ElMessage.error("加载配置失败，请刷新页面重试");
  } finally {
    loading.close();
  }
};

// Unified function to save configuration
const saveConfig = async (configName: string, data: any) => {
  const loading = ElLoading.service({
    lock: true,
    text: "保存中...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    const response = await fetch("http://localhost:8000/api/save-config", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        [configName]: data,
      }),
    });

    if (!response.ok) {
      throw new Error("保存失败");
    }

    return true;
  } catch (error) {
    console.error(`保存${configName}失败:`, error);
    throw error;
  } finally {
    loading.close();
  }
};

// Fetch all configurations when component is mounted
onMounted(() => {
  fetchAllConfigs();
});

// Active tab
const activeTab = ref("sapConfig");

// SAP Configuration: Connection Settings
const sapConnectionFormRef = ref();
const sapConnectionForm = reactive({
  path: "",
  username: "",
  password: "",
  accountBook: "",
});

const saveSapConnectionSettings = () => {
  sapConnectionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        await saveConfig("sapConnection", sapConnectionForm);
        ElMessage.success("SAP连接设置已保存！");
      } catch (error) {
        ElMessage.error("保存SAP连接设置失败，请稍后重试");
      }
    } else {
      ElMessage.error("请检查输入项！");
      return false;
    }
  });
};

// SAP Configuration: Profit Center Table
interface SapProfitCenterRow {
  profitCenterGroup: string;
  companyCode: string;
  profitCenterGroupName: string;
}

const sapProfitCenterTableData = ref<SapProfitCenterRow[]>([
  { profitCenterGroup: "", companyCode: "", profitCenterGroupName: "" },
]);

const addProfitCenterRow = () => {
  sapProfitCenterTableData.value.push({
    profitCenterGroup: "",
    companyCode: "",
    profitCenterGroupName: "",
  });
};

const removeProfitCenterRow = (index: number) => {
  ElMessageBox.confirm("确定删除此行吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      sapProfitCenterTableData.value.splice(index, 1);
      ElMessage.success("行已删除");
    })
    .catch(() => {
      // User cancelled
    });
};

const saveSapProfitCenterConfig = async () => {
  try {
    await saveConfig("sapProfitCenter", sapProfitCenterTableData.value);
    ElMessage.success("利润中心组配置已保存！");
  } catch (error) {
    ElMessage.error("保存利润中心组配置失败，请稍后重试");
  }
};

const openPasteDialogForSapProfitCenter = () => {
  ElMessageBox.prompt(
    "请粘贴数据（每行一条记录，列之间用Tab分隔）：<br><small>预期格式: 利润中心组 (Tab) 公司编码 (Tab) 利润中心组名称</small>",
    "粘贴利润中心数据",
    {
      confirmButtonText: "确定粘贴",
      cancelButtonText: "取消",
      inputType: "textarea",
      inputPlaceholder: "利润中心组\t公司编码\t利润中心组名称\n...", // Example placeholder
      dangerouslyUseHTMLString: true, // For <br> and <small>
      customStyle: { width: "600px" }, // Wider dialog for textarea
      inputValidator: (value) => {
        if (!value || value.trim() === "") return "粘贴内容不能为空";
        return true;
      },
    }
  )
    .then(({ value }) => {
      processPastedDataForSapProfitCenter(value);
    })
    .catch(() => {
      ElMessage.info("粘贴操作已取消");
    });
};

const processPastedDataForSapProfitCenter = (pastedText: string) => {
  const lines = pastedText.trim().split("\n");
  let rowsAdded = 0;
  lines.forEach((line) => {
    const columns = line.split("\t");
    if (columns.length >= 1) {
      // Expecting at least one column, ideally 3
      sapProfitCenterTableData.value.push({
        profitCenterGroup: columns[0]?.trim() || "",
        companyCode: columns[1]?.trim() || "",
        profitCenterGroupName: columns[2]?.trim() || "",
      });
      rowsAdded++;
    }
  });
  if (rowsAdded > 0) {
    ElMessage.success(`${rowsAdded} 行利润中心数据已成功粘贴并添加！`);
  } else {
    ElMessage.warning("未粘贴任何数据，或数据格式不正确。");
  }
};

// Auto Voucher Configuration: Excluded Profit Centers Table
interface ExcludedProfitCenterRow {
  description: string;
  isEnabled: boolean;
}

const excludedProfitCentersTableData = ref<ExcludedProfitCenterRow[]>([
  { description: "", isEnabled: true },
]);

const addExcludedProfitCenterRow = () => {
  excludedProfitCentersTableData.value.push({
    description: "",
    isEnabled: true,
  });
};

const removeExcludedProfitCenterRow = (index: number) => {
  ElMessageBox.confirm("确定删除此排除利润中心吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      excludedProfitCentersTableData.value.splice(index, 1);
      ElMessage.success("排除利润中心已删除");
    })
    .catch(() => {});
};

const saveExcludedProfitCenters = async () => {
  try {
    await saveConfig(
      "excludedProfitCenters",
      excludedProfitCentersTableData.value
    );
    ElMessage.success("排除利润中心配置已保存！");
  } catch (error) {
    ElMessage.error("保存排除利润中心配置失败，请稍后重试");
  }
};

const openPasteDialogForExcludedProfitCenters = () => {
  ElMessageBox.prompt(
    "请粘贴数据（每行一条记录，列之间用Tab分隔）：<br><small>预期格式: 利润中心代码 (Tab) 描述 (Tab) 是否启用</small>",
    "粘贴排除利润中心数据",
    {
      confirmButtonText: "确定粘贴",
      cancelButtonText: "取消",
      inputType: "textarea",
      inputPlaceholder: "利润中心代码\t描述\t是否启用\n...",
      dangerouslyUseHTMLString: true,
      customStyle: { width: "600px" },
      inputValidator: (value) => {
        if (!value || value.trim() === "") return "粘贴内容不能为空";
        return true;
      },
    }
  )
    .then(({ value }) => {
      processPastedDataForExcludedProfitCenters(value);
    })
    .catch(() => {
      ElMessage.info("粘贴操作已取消");
    });
};

const processPastedDataForExcludedProfitCenters = (pastedText: string) => {
  const lines = pastedText.trim().split("\n");
  let rowsAdded = 0;
  lines.forEach((line) => {
    const columns = line.split("\t");
    if (columns.length >= 1) {
      excludedProfitCentersTableData.value.push({
        description: columns[0]?.trim() || "",
        isEnabled: columns[1]?.trim()?.toLowerCase() === "false" ? false : true,
      });
      rowsAdded++;
    }
  });
  if (rowsAdded > 0) {
    ElMessage.success(`${rowsAdded} 行排除利润中心数据已成功粘贴并添加！`);
  } else {
    ElMessage.warning("未粘贴任何数据，或数据格式不正确。");
  }
};

// Auto Voucher Configuration: Document Types Table
interface DocumentTypeRow {
  documentTypeDescription: string;
  isEnabled: boolean;
}

const documentTypesTableData = ref<DocumentTypeRow[]>([
  { documentTypeDescription: "", isEnabled: true },
]);

const addDocumentTypeRow = () => {
  documentTypesTableData.value.push({
    documentTypeDescription: "",
    isEnabled: true,
  });
};

const removeDocumentTypeRow = (index: number) => {
  ElMessageBox.confirm("确定删除此单据类型吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      documentTypesTableData.value.splice(index, 1);
      ElMessage.success("单据类型已删除");
    })
    .catch(() => {});
};

const saveDocumentTypes = async () => {
  try {
    await saveConfig("documentTypes", documentTypesTableData.value);
    ElMessage.success("单据类型配置已保存！");
  } catch (error) {
    ElMessage.error("保存单据类型配置失败，请稍后重试");
  }
};

const openPasteDialogForDocumentTypes = () => {
  ElMessageBox.prompt(
    "请粘贴数据（每行一条记录，列之间用Tab分隔）：<br><small>预期格式: 单据类型名称 (Tab) 是否启用</small>",
    "粘贴单据类型数据",
    {
      confirmButtonText: "确定粘贴",
      cancelButtonText: "取消",
      inputType: "textarea",
      inputPlaceholder: "单据类型名称\t是否启用\n...",
      dangerouslyUseHTMLString: true,
      customStyle: { width: "600px" },
      inputValidator: (value) => {
        if (!value || value.trim() === "") return "粘贴内容不能为空";
        return true;
      },
    }
  )
    .then(({ value }) => {
      processPastedDataForDocumentTypes(value);
    })
    .catch(() => {
      ElMessage.info("粘贴操作已取消");
    });
};

const processPastedDataForDocumentTypes = (pastedText: string) => {
  const lines = pastedText.trim().split("\n");
  let rowsAdded = 0;
  lines.forEach((line) => {
    const columns = line.split("\t");
    if (columns.length >= 1) {
      documentTypesTableData.value.push({
        documentTypeDescription: columns[0]?.trim() || "",
        isEnabled: columns[1]?.trim()?.toLowerCase() === "false" ? false : true,
      });
      rowsAdded++;
    }
  });
  if (rowsAdded > 0) {
    ElMessage.success(`${rowsAdded} 行单据类型数据已成功粘贴并添加！`);
  } else {
    ElMessage.warning("未粘贴任何数据，或数据格式不正确。");
  }
};

// Financial Integration Configuration: Units Table
interface UnitRow {
  unitName: string;
  unitCode: string;
}

const financialIntegrationUnitsTableData = ref<UnitRow[]>([
  { unitName: "", unitCode: "" },
]);

const addUnitRow = () => {
  financialIntegrationUnitsTableData.value.push({
    unitName: "",
    unitCode: "",
  });
};

const removeUnitRow = (index: number) => {
  ElMessageBox.confirm("确定删除此单位吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      financialIntegrationUnitsTableData.value.splice(index, 1);
      ElMessage.success("单位已删除");
    })
    .catch(() => {});
};

const saveFinancialIntegrationUnits = async () => {
  try {
    await saveConfig(
      "financialIntegrationUnits",
      financialIntegrationUnitsTableData.value
    );
    ElMessage.success("单位配置已保存！");
  } catch (error) {
    ElMessage.error("保存单位配置失败，请稍后重试");
  }
};

const openPasteDialogForUnits = () => {
  ElMessageBox.prompt(
    "请粘贴数据（每行一条记录，列之间用Tab分隔）：<br><small>预期格式: 单位名称 (Tab) 单位编码</small>",
    "粘贴单位数据",
    {
      confirmButtonText: "确定粘贴",
      cancelButtonText: "取消",
      inputType: "textarea",
      inputPlaceholder: "单位名称\t单位编码\n...",
      dangerouslyUseHTMLString: true,
      customStyle: { width: "600px" },
      inputValidator: (value) => {
        if (!value || value.trim() === "") return "粘贴内容不能为空";
        return true;
      },
    }
  )
    .then(({ value }) => {
      processPastedDataForUnits(value);
    })
    .catch(() => {
      ElMessage.info("粘贴操作已取消");
    });
};

const processPastedDataForUnits = (pastedText: string) => {
  const lines = pastedText.trim().split("\n");
  let rowsAdded = 0;
  lines.forEach((line) => {
    const columns = line.split("\t");
    if (columns.length >= 1) {
      financialIntegrationUnitsTableData.value.push({
        unitName: columns[0]?.trim() || "",
        unitCode: columns[1]?.trim() || "",
      });
      rowsAdded++;
    }
  });
  if (rowsAdded > 0) {
    ElMessage.success(`${rowsAdded} 行单位数据已成功粘贴并添加！`);
  } else {
    ElMessage.warning("未粘贴任何数据，或数据格式不正确。");
  }
};

// Financial Quotient Configuration: User Settings
const fqUserFormRef = ref();
const fqUserForm = reactive({
  username: "",
  password: "",
});

const saveFqUserSettings = () => {
  fqUserFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        await saveConfig("fqUserSettings", fqUserForm);
        ElMessage.success("财商用户凭证已保存！");
      } catch (error) {
        ElMessage.error("保存财商用户凭证失败，请稍后重试");
      }
    } else {
      ElMessage.error("请检查输入项！");
      return false;
    }
  });
};

// Calculation Configuration: Unit Management Mapping Table
interface UnitMapRow {
  sourceUnitName: string;
  sourceUnitCode: string;
  targetUnitName: string;
}
const unitMappingTableData = ref<UnitMapRow[]>([
  { sourceUnitName: "", sourceUnitCode: "", targetUnitName: "" },
]);
const addUnitMapRow = () => {
  unitMappingTableData.value.push({
    sourceUnitName: "",
    sourceUnitCode: "",
    targetUnitName: "",
  });
};
const removeUnitMapRow = (index: number) => {
  ElMessageBox.confirm("确定删除此单位映射吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      unitMappingTableData.value.splice(index, 1);
      ElMessage.success("单位映射已删除");
    })
    .catch(() => {});
};
const saveUnitMappingTable = async () => {
  try {
    await saveConfig("unitMapping", unitMappingTableData.value);
    ElMessage.success("单位管理映射表已保存！");
  } catch (error) {
    ElMessage.error("保存单位管理映射表失败，请稍后重试");
  }
};

const openPasteDialogForUnitMapping = () => {
  ElMessageBox.prompt(
    "请粘贴数据（每行一条记录，列之间用Tab分隔）：<br><small>预期格式: 源单位名称 (Tab) 源单位编码 (Tab) 目标单位名称</small>",
    "粘贴单位映射数据",
    {
      confirmButtonText: "确定粘贴",
      cancelButtonText: "取消",
      inputType: "textarea",
      inputPlaceholder: "源单位名称\t源单位编码\t目标单位名称\n...",
      dangerouslyUseHTMLString: true,
      customStyle: { width: "700px" }, // Wider dialog
      inputValidator: (value) => {
        if (!value || value.trim() === "") return "粘贴内容不能为空";
        return true;
      },
    }
  )
    .then(({ value }) => {
      processPastedDataForUnitMapping(value);
    })
    .catch(() => {
      ElMessage.info("粘贴操作已取消");
    });
};

const processPastedDataForUnitMapping = (pastedText: string) => {
  const lines = pastedText.trim().split("\n");
  let rowsAdded = 0;
  lines.forEach((line) => {
    const columns = line.split("\t");
    if (columns.length >= 3) {
      // Require all three columns
      unitMappingTableData.value.push({
        sourceUnitName: columns[0]?.trim() || "",
        sourceUnitCode: columns[1]?.trim() || "",
        targetUnitName: columns[2]?.trim() || "",
      });
      rowsAdded++;
    }
  });
  if (rowsAdded > 0) {
    ElMessage.success(`${rowsAdded} 行单位映射数据已成功粘贴并添加！`);
  } else {
    ElMessage.warning(
      "未粘贴任何数据，或数据格式不正确。请确保每行包含源单位名称、源单位编码和目标单位名称，以制表符分隔。"
    );
  }
};

// Calculation Configuration: SAP Expense Transfer Account Table
interface ExpenseTransferRow {
  description: string;
}
const expenseTransferTableData = ref<ExpenseTransferRow[]>([
  { description: "" },
]);
const addExpenseTransferRow = () => {
  expenseTransferTableData.value.push({ description: "" });
};
const removeExpenseTransferRow = (index: number) => {
  ElMessageBox.confirm("确定删除此费用科目吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      expenseTransferTableData.value.splice(index, 1);
      ElMessage.success("费用科目已删除");
    })
    .catch(() => {});
};
const saveExpenseTransferTable = async () => {
  try {
    await saveConfig("expenseTransfer", expenseTransferTableData.value);
    ElMessage.success("SAP费用划转科目表已保存！");
  } catch (error) {
    ElMessage.error("保存SAP费用划转科目表失败，请稍后重试");
  }
};

const openPasteDialogForExpenseTransfer = () => {
  ElMessageBox.prompt(
    "请粘贴数据（每行一条记录，列之间用Tab分隔）：<br><small>预期格式: 划转费用科目名称</small>",
    "粘贴费用划转科目数据",
    {
      confirmButtonText: "确定粘贴",
      cancelButtonText: "取消",
      inputType: "textarea",
      inputPlaceholder: "费用类型\t源科目\t目标科目\t描述\n...",
      dangerouslyUseHTMLString: true,
      customStyle: { width: "700px" }, // Wider dialog
      inputValidator: (value) => {
        if (!value || value.trim() === "") return "粘贴内容不能为空";
        return true;
      },
    }
  )
    .then(({ value }) => {
      processPastedDataForExpenseTransfer(value);
    })
    .catch(() => {
      ElMessage.info("粘贴操作已取消");
    });
};

const processPastedDataForExpenseTransfer = (pastedText: string) => {
  const lines = pastedText.trim().split("\n");
  let rowsAdded = 0;
  lines.forEach((line) => {
    const columns = line.split("\t");
    if (columns.length >= 1) {
      expenseTransferTableData.value.push({
        description: columns[0]?.trim() || "",
      });
      rowsAdded++;
    }
  });
  if (rowsAdded > 0) {
    ElMessage.success(`${rowsAdded} 行费用划转科目数据已成功粘贴并添加！`);
  } else {
    ElMessage.warning("未粘贴任何数据，或数据格式不正确。");
  }
};

// Calculation Configuration: Internal Transaction Suspense Account Superior Customer Table
interface InternalCustomerRow {
  //内部客商
  superiorCustomerCode: string;
  superiorCustomerName: string;
}
const internalCustomerTableData = ref<InternalCustomerRow[]>([
  { superiorCustomerCode: "", superiorCustomerName: "" },
]);
const addInternalCustomerRow = () => {
  internalCustomerTableData.value.push({
    superiorCustomerCode: "",
    superiorCustomerName: "",
  });
};
const removeInternalCustomerRow = (index: number) => {
  ElMessageBox.confirm("确定删除此内部客商吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      internalCustomerTableData.value.splice(index, 1);
      ElMessage.success("内部客商已删除");
    })
    .catch(() => {});
};
const saveInternalCustomerTable = async () => {
  try {
    // Map the table data to match the expected API format
    const mappedData = internalCustomerTableData.value.map((item) => ({
      superior_customer_code: item.superiorCustomerCode,
      superior_customer_name: item.superiorCustomerName,
    }));

    await saveConfig("internalCustomers", mappedData);
    ElMessage.success("内部往来挂账上级客商表已保存！");
  } catch (error) {
    console.error("保存内部往来挂账上级客商表失败:", error);
    ElMessage.error("保存内部往来挂账上级客商表失败，请稍后重试");
  }
};

const openPasteDialogForInternalCustomer = () => {
  ElMessageBox.prompt(
    "请粘贴数据（每行一条记录，列之间用Tab分隔）：<br><small>预期格式: 内部单位 (Tab) 上级客商编码 (Tab) 上级客商名称</small>",
    "粘贴内部客商数据",
    {
      confirmButtonText: "确定粘贴",
      cancelButtonText: "取消",
      inputType: "textarea",
      inputPlaceholder: "内部单位\t上级客商编码\t上级客商名称\n...",
      dangerouslyUseHTMLString: true,
      customStyle: { width: "700px" }, // Wider dialog
      inputValidator: (value) => {
        if (!value || value.trim() === "") return "粘贴内容不能为空";
        return true;
      },
    }
  )
    .then(({ value }) => {
      processPastedDataForInternalCustomer(value);
    })
    .catch(() => {
      ElMessage.info("粘贴操作已取消");
    });
};

const processPastedDataForInternalCustomer = (pastedText: string) => {
  const lines = pastedText.trim().split("\n");
  let rowsAdded = 0;
  lines.forEach((line) => {
    const columns = line.split("\t");
    if (columns.length >= 2) {
      // Ensure we have at least 2 columns
      internalCustomerTableData.value.push({
        superiorCustomerCode: columns[0]?.trim() || "",
        superiorCustomerName: columns[1]?.trim() || "",
      });
      rowsAdded++;
    }
  });
  if (rowsAdded > 0) {
    ElMessage.success(`${rowsAdded} 行内部客商数据已成功粘贴并添加！`);
  } else {
    ElMessage.warning("未粘贴任何数据，或数据格式不正确。");
  }
};
</script>

<style scoped>
.settings-view-container {
  padding: 20px;
  background-color: #f4f7f6; /* Light background for the whole view */
  height: calc(100vh - 40px); /* Full viewport height minus padding */
  overflow-y: auto; /* Enable vertical scrolling */
  box-sizing: border-box; /* Include padding in height calculation */
  width: 100%;
}

.settings-tabs {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: 100%;
  box-sizing: border-box;
}

/* Style for the content area within each tab pane for consistent padding */
.tab-content-wrapper {
  padding: 20px;
  min-height: calc(100% - 40px); /* Ensure content takes at least full height */
  box-sizing: border-box;
}

.box-card {
  margin-bottom: 25px;
  max-width: 100%;
  box-sizing: border-box;
}

.box-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.el-form {
  margin-top: 10px;
}

.el-form-item {
  margin-bottom: 20px; /* Increased spacing for better readability */
}

.el-table {
  margin-top: 15px;
}

/* Ensure tabs themselves have a bit more presence */
:deep(.el-tabs__header) {
  margin-bottom: 0; /* Remove default margin if custom padding is handled by .tab-content-wrapper */
}

:deep(.el-tabs__nav-wrap::after) {
  /* Optional: remove bottom border of tab headers if desired */
  /* height: 0; */
}
:deep(.el-tabs__item) {
  padding: 0 25px; /* More padding for tab titles */
  height: 50px; /* Taller tabs */
  font-size: 15px;
  width: 25%;
}
:deep(.el-tabs__item) {
  padding: 0 25px; /* More padding for tab titles */
  height: 50px; /* Taller tabs */
  font-size: 15px;
  width: 25%;
}
:deep(.el-tabs__nav) {
  /* Optional: remove bottom border of tab headers if desired */
  /* height: 0; */
  width: 100%;
}

:deep(.el-tabs__item.is-active) {
  font-weight: 600;
}

/* Styling for buttons inside cards or at the end of forms */

/* Specific adjustments for better layout if needed */
.el-row {
  margin-bottom: 0; /* Adjust if el-form-item provides enough spacing */
}
</style>
