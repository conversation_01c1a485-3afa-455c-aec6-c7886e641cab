<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

// Mock data for charts
const incomeData = {
  categories: ['房建', '基建', '地产', '投资', '设计', '其他'],
  totalIncome: [2200, 1800, 2500, 1200, 900, 600],
  unrealizedIncome: [500, 400, 700, 300, 200, 150],
  monthlyTrend: [
    [180, 200, 220, 190, 210, 230, 250, 270, 280, 300, 320, 350],
    [150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260]
  ],
  months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
};

const profitData = {
  categories: ['房建', '基建', '地产', '投资', '设计', '其他'],
  profit: [700, 450, 700, 500, 350, 200],
  profitRate: [31.8, 25.0, 28.0, 41.7, 38.9, 33.3], // 利润率百分比
  quarterlyProfit: [
    [150, 180, 200, 170], // Q1-Q4 for 房建
    [100, 120, 130, 100], // Q1-Q4 for 基建
    [160, 180, 190, 170], // Q1-Q4 for 地产
    [110, 130, 140, 120], // Q1-Q4 for 投资
    [80, 90, 100, 80],    // Q1-Q4 for 设计
    [40, 50, 60, 50]      // Q1-Q4 for 其他
  ],
  quarters: ['Q1', 'Q2', 'Q3', 'Q4']
};

const fundsData = {
  categories: ['总资金', '短期借款', '应付票据', '应付保理', '净资金'],
  values: [12000, 3000, 2500, 1500, 5000],
  monthlyTrend: [
    [11000, 11200, 11500, 11800, 12000, 12200, 12300, 12100, 12000, 11800, 11900, 12000], // 总资金
    [2800, 2900, 3000, 3100, 3000, 2900, 2800, 2700, 2800, 2900, 3000, 3000],  // 短期借款
    [2200, 2300, 2400, 2500, 2500, 2600, 2700, 2600, 2500, 2400, 2500, 2500],  // 应付票据
    [1300, 1400, 1500, 1600, 1500, 1400, 1300, 1400, 1500, 1600, 1500, 1500],  // 应付保理
    [4700, 4600, 4600, 4600, 5000, 5300, 5500, 5400, 5200, 4900, 4900, 5000]   // 净资金
  ],
  months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
};

const assetsData = {
  contractAssets: 8500,
  accountsReceivable: 7200,
  ageingData: [
    [3000, 2500, 1500, 1000, 500], // 合同资产账龄分布
    [2500, 2200, 1300, 800, 400]   // 应收账款账龄分布
  ],
  ageingLabels: ['0-30天', '31-60天', '61-90天', '91-180天', '180天以上'],
  monthlyTrend: [
    [8000, 8100, 8200, 8300, 8400, 8500, 8600, 8700, 8600, 8500, 8400, 8500], // 合同资产
    [6800, 6900, 7000, 7100, 7200, 7300, 7200, 7100, 7000, 7100, 7200, 7200]  // 应收账款
  ],
  months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
};

const liabilitiesData = {
  categories: ['应付分包', '应付材料', '应付劳务', '应付保理', '应付票据'],
  values: [4500, 3800, 2700, 1500, 2500],
  monthlyTrend: [
    [4300, 4400, 4500, 4600, 4500, 4400, 4300, 4400, 4500, 4600, 4500, 4500], // 应付分包
    [3600, 3700, 3800, 3900, 3800, 3700, 3600, 3700, 3800, 3900, 3800, 3800], // 应付材料
    [2500, 2600, 2700, 2800, 2700, 2600, 2500, 2600, 2700, 2800, 2700, 2700], // 应付劳务
    [1300, 1400, 1500, 1600, 1500, 1400, 1300, 1400, 1500, 1600, 1500, 1500], // 应付保理
    [2300, 2400, 2500, 2600, 2500, 2400, 2300, 2400, 2500, 2600, 2500, 2500]  // 应付票据
  ],
  months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
};

const taxData = {
  uninvoicedIncome: 5800,
  uninvoicedAmount: 4200,
  unreceivedInvoiceAmount: 3500,
  monthlyTrend: [
    [5500, 5600, 5700, 5800, 5900, 6000, 5900, 5800, 5700, 5800, 5900, 5800], // 未开票收入
    [4000, 4100, 4200, 4300, 4200, 4100, 4000, 4100, 4200, 4300, 4200, 4200], // 未开票金额
    [3300, 3400, 3500, 3600, 3500, 3400, 3300, 3400, 3500, 3600, 3500, 3500]  // 未收票金额
  ],
  months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
};

// Initialize charts after component mount
onMounted(() => {
  initIncomeChart();
  initProfitChart();
  initFundsChart();
  initAssetsChart();
  initLiabilitiesChart();
  initTaxChart();
});

function initIncomeChart() {
  const chartDom = document.getElementById('incomeChart');
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['总收入', '未结转收入'],
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: incomeData.categories,
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）'
      }
    ],
    series: [
      {
        name: '总收入',
        type: 'bar',
        barWidth: '40%',
        data: incomeData.totalIncome,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#0a5cad' }
          ])
        }
      },
      {
        name: '未结转收入',
        type: 'bar',
        barWidth: '40%',
        data: incomeData.unrealizedIncome,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#b8e986' },
            { offset: 0.5, color: '#7ed321' },
            { offset: 1, color: '#4a9e06' }
          ])
        }
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initProfitChart() {
  const chartDom = document.getElementById('profitChart');
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['利润', '利润率'],
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: profitData.categories,
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）'
      },
      {
        type: 'value',
        name: '利润率（%）',
        min: 0,
        max: 50
      }
    ],
    series: [
      {
        name: '利润',
        type: 'bar',
        barWidth: '40%',
        data: profitData.profit,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#34a853' },
            { offset: 0.5, color: '#26833c' },
            { offset: 1, color: '#1a5e28' }
          ])
        }
      },
      {
        name: '利润率',
        type: 'line',
        yAxisIndex: 1,
        data: profitData.profitRate,
        symbolSize: 8,
        itemStyle: {
          color: '#ff5722'
        }
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initFundsChart() {
  const chartDom = document.getElementById('fundsChart');
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: fundsData.categories,
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: fundsData.months,
        axisLabel: {
          interval: 1,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）'
      }
    ],
    series: [
      {
        name: fundsData.categories[0], // 总资金
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#fbbc05'
        },
        lineStyle: {
          width: 3
        },
        data: fundsData.monthlyTrend[0]
      },
      {
        name: fundsData.categories[1], // 短期借款
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#4285f4'
        },
        data: fundsData.monthlyTrend[1]
      },
      {
        name: fundsData.categories[2], // 应付票据
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#ea4335'
        },
        data: fundsData.monthlyTrend[2]
      },
      {
        name: fundsData.categories[3], // 应付保理
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#800080'
        },
        data: fundsData.monthlyTrend[3]
      },
      {
        name: fundsData.categories[4], // 净资金
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#34a853'
        },
        lineStyle: {
          width: 3,
          type: 'dashed'
        },
        data: fundsData.monthlyTrend[4]
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initAssetsChart() {
  const chartDom = document.getElementById('assetsChart');
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['总资产', '应收账款'],
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: assetsData.ageingLabels,
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）'
      }
    ],
    series: [
      {
        name: '总资产',
        type: 'bar',
        barWidth: '30%',
        data: assetsData.ageingData[0],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#800080' },
            { offset: 0.5, color: '#9932CC' },
            { offset: 1, color: '#BA55D3' }
          ])
        }
      },
      {
        name: '应收账款',
        type: 'bar',
        barWidth: '30%',
        data: assetsData.ageingData[1],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#4B0082' },
            { offset: 0.5, color: '#8A2BE2' },
            { offset: 1, color: '#9370DB' }
          ])
        }
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initLiabilitiesChart() {
  const chartDom = document.getElementById('liabilitiesChart');
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'center',
      textStyle: {
        color: '#333'
      }
    },
    series: [
      {
        name: '负债结构',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: liabilitiesData.categories.map((type, index) => {
          return {
            value: liabilitiesData.values[index],
            name: type
          };
        })
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initTaxChart() {
  const chartDom = document.getElementById('taxChart');
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['未开票收入', '未开票金额', '未收票金额'],
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: taxData.months
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）'
      }
    ],
    series: [
      {
        name: '未开票收入',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 128, 128, 0.8)' },
            { offset: 1, color: 'rgba(0, 128, 128, 0.1)' }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#008080'
        },
        data: taxData.monthlyTrend[0]
      },
      {
        name: '未开票金额',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 224, 208, 0.8)' },
            { offset: 1, color: 'rgba(64, 224, 208, 0.1)' }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#40E0D0'
        },
        data: taxData.monthlyTrend[1]
      },
      {
        name: '未收票金额',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 206, 209, 0.8)' },
            { offset: 1, color: 'rgba(0, 206, 209, 0.1)' }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#00CED1'
        },
        data: taxData.monthlyTrend[2]
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}
</script>

<template>
  <div class="data-analysis-container">
    <div class="dashboard-header">
      <h1>财务数据分析总览</h1>
      <div class="dashboard-stats">
        <div class="stat-card income">
          <div class="stat-value">¥ 14,060.0万</div>
          <div class="stat-label">总收入</div>
          <div class="stat-change positive">↑ 12.5%</div>
        </div>
        <div class="stat-card profit">
          <div class="stat-value">¥ 3,050.0万</div>
          <div class="stat-label">总利润</div>
          <div class="stat-change positive">↑ 15.8%</div>
        </div>
        <div class="stat-card funds">
          <div class="stat-value">¥ 12,000.0万</div>
          <div class="stat-label">总资金</div>
          <div class="stat-change positive">↑ 5.2%</div>
        </div>
        <div class="stat-card assets">
          <div class="stat-value">¥ 15,700.0万</div>
          <div class="stat-label">总资产</div>
          <div class="stat-change positive">↑ 7.3%</div>
        </div>
        <div class="stat-card liabilities">
          <div class="stat-value">¥ 11,000.0万</div>
          <div class="stat-label">总负债</div>
          <div class="stat-change negative">↑ 3.2%</div>
        </div>
        <div class="stat-card tax">
          <div class="stat-value">¥ 5,800.0万</div>
          <div class="stat-label">未开票收入</div>
          <div class="stat-change negative">↑ 4.1%</div>
        </div>
      </div>
    </div>

    <div class="charts-grid">
      <!-- 收入区域 -->
      <div class="chart-container income-section">
        <h2 class="section-title">收入分析</h2>
        <div class="section-description">总收入与未结转收入分析</div>
        <div id="incomeChart" class="chart"></div>
      </div>
      
      <!-- 利润区域 -->
      <div class="chart-container profit-section">
        <h2 class="section-title">利润分析</h2>
        <div class="section-description">利润与利润率分析</div>
        <div id="profitChart" class="chart"></div>
      </div>
      
      <!-- 资金区域 -->
      <div class="chart-container funds-section">
        <h2 class="section-title">资金分析</h2>
        <div class="section-description">总资金、短期借款、应付票据、应付保理、净资金</div>
        <div id="fundsChart" class="chart"></div>
      </div>
      
      <!-- 资产区域 -->
      <div class="chart-container assets-section">
        <h2 class="section-title">资产分析</h2>
        <div class="section-description">合同资产、应收账款</div>
        <div id="assetsChart" class="chart"></div>
      </div>
      
      <!-- 负债区域 -->
      <div class="chart-container liabilities-section">
        <h2 class="section-title">负债分析</h2>
        <div class="section-description">应付分包、应付材料、应付劳务、应付保理、应付票据</div>
        <div id="liabilitiesChart" class="chart"></div>
      </div>
      
      <!-- 税务区域 -->
      <div class="chart-container tax-section">
        <h2 class="section-title">税务分析</h2>
        <div class="section-description">未开票收入、未开票金额、未收票金额</div>
        <div id="taxChart" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.data-analysis-container {
  width: 100%;
  height: 100%;
  padding: 0;
  overflow-y: auto;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  background: linear-gradient(135deg, #1967d2, #4285f4);
  color: white;
  padding: 24px;
  border-radius: 12px;
  margin: 24px 24px 0 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.dashboard-header h1 {
  margin: 0 0 16px 0;
  font-weight: 500;
  font-size: 1.8rem;
  text-align: center;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
}

.stat-card {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 16px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Specific card styling */
.stat-card.income {
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.1), rgba(66, 133, 244, 0.2));
  border-bottom: 3px solid #4285f4;
}

.stat-card.profit {
  background: linear-gradient(135deg, rgba(52, 168, 83, 0.1), rgba(52, 168, 83, 0.2));
  border-bottom: 3px solid #34a853;
}

.stat-card.funds {
  background: linear-gradient(135deg, rgba(251, 188, 5, 0.1), rgba(251, 188, 5, 0.2));
  border-bottom: 3px solid #fbbc05;
}

.stat-card.assets {
  background: linear-gradient(135deg, rgba(128, 0, 128, 0.1), rgba(128, 0, 128, 0.2));
  border-bottom: 3px solid #800080;
}

.stat-card.liabilities {
  background: linear-gradient(135deg, rgba(234, 67, 53, 0.1), rgba(234, 67, 53, 0.2));
  border-bottom: 3px solid #ea4335;
}

.stat-card.tax {
  background: linear-gradient(135deg, rgba(0, 128, 128, 0.1), rgba(0, 128, 128, 0.2));
  border-bottom: 3px solid #008080;
}

.stat-value {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.85rem;
  opacity: 0.9;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 0.8rem;
  font-weight: 500;
}

.stat-change.positive {
  color: #a7f3d0;
}

.stat-change.negative {
  color: #fecaca;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  padding: 24px;
  flex: 1;
  min-height: 0;
}

.chart-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  flex-direction: column;
  min-height: 350px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chart-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Specific chart section styling */
.income-section {
  border-top: 5px solid #4285f4;
}

.profit-section {
  border-top: 5px solid #34a853;
}

.funds-section {
  border-top: 5px solid #fbbc05;
}

.assets-section {
  border-top: 5px solid #800080;
}

.liabilities-section {
  border-top: 5px solid #ea4335;
}

.tax-section {
  border-top: 5px solid #008080;
}

.section-title {
  font-size: 1.3rem;
  font-weight: 500;
  margin: 0 0 5px 0;
  color: #333;
}

.section-description {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 15px;
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 280px;
  flex-grow: 1;
}

@media (max-width: 1200px) {
  .dashboard-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }
  
  .dashboard-header {
    margin: 16px 16px 0 16px;
  }
}

@media (max-width: 480px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
}
</style>
