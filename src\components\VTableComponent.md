# VTableComponent 使用文档

## 概述

VTableComponent 是一个基于 @visactor/vtable 的 Vue 3 表格组件，支持从二维数组自动生成表格，内置筛选功能和复制粘贴功能。

## 特性

- ✅ **自动表格生成**: 传入二维数组即可自动生成表格
- ✅ **内置筛选功能**: 支持按列筛选和全局筛选，回车快速应用
- ✅ **复制粘贴支持**: 支持 Ctrl+C / Ctrl+V 操作，兼容Excel等外部应用
- ✅ **可编辑单元格**: 双击单元格即可编辑，智能识别数据类型
- ✅ **自动列宽调整**: 根据内容自动计算最佳列宽，支持手动拖拽调整
- ✅ **智能编辑器**: 自动为不同数据类型配置合适的编辑器（文本、日期、下拉）
- ✅ **响应式数据**: 数据变化时自动更新表格，支持双向绑定
- ✅ **丰富的API**: 提供完整的方法接口，支持程序化操作
- ✅ **现代化UI**: 优化的样式和交互体验
- ✅ **数据导出**: 内置CSV导出功能

## 安装依赖

```bash
npm install @visactor/vtable @visactor/vtable-editors
```

## 基础用法

```vue
<template>
  <VTableComponent
    :data="tableData"
    :width="600"
    :height="300"
    :show-filter="true"
  />
</template>

<script setup>
import VTableComponent from '@/components/VTableComponent.vue'

const tableData = [
  ['姓名', '年龄', '城市'],  // 第一行为标题
  ['张三', 28, '北京'],
  ['李四', 32, '上海'],
  ['王五', 25, '广州']
]
</script>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `data` | `Array` | `[]` | 二维数组数据，第一行为标题 |
| `width` | `Number` | `600` | 表格宽度（像素） |
| `height` | `Number` | `300` | 表格高度（像素） |
| `showFilter` | `Boolean` | `true` | 是否显示筛选面板 |
| `editable` | `Boolean` | `true` | 是否启用单元格编辑功能 |
| `enableCopyPaste` | `Boolean` | `true` | 是否启用复制粘贴功能 |
| `autoWidth` | `Boolean` | `true` | 是否启用自动列宽调整 |
| `tableOptions` | `Object` | `{}` | 额外的表格配置选项 |

## 数据格式

数据必须是二维数组格式，第一行为列标题：

```javascript
const data = [
  ['列1', '列2', '列3'],    // 标题行
  ['数据1', '数据2', '数据3'], // 数据行
  ['数据4', '数据5', '数据6']  // 数据行
]
```

## 高级用法

### 自定义表格配置

```vue
<VTableComponent
  :data="data"
  :table-options="{
    rowHeight: 40,
    headerHeight: 45,
    theme: 'dark'
  }"
/>
```

### 获取表格实例

```vue
<template>
  <VTableComponent
    ref="tableRef"
    :data="data"
  />
  <button @click="handleExport">导出数据</button>
</template>

<script setup>
import { ref } from 'vue'

const tableRef = ref()

function handleExport() {
  const tableInstance = tableRef.value.getTableInstance()
  // 使用表格实例进行操作
}
</script>
```

### 动态数据更新

```vue
<template>
  <VTableComponent :data="dynamicData" />
  <button @click="addRow">添加行</button>
</template>

<script setup>
import { ref } from 'vue'

const dynamicData = ref([
  ['产品', '价格'],
  ['手机', 3999]
])

function addRow() {
  dynamicData.value.push(['新产品', 1999])
}
</script>
```

## 暴露的方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| `resetFilter()` | 重置筛选条件 | - |
| `doFilter()` | 执行筛选 | - |
| `getTableInstance()` | 获取 VTable 实例 | - |
| `getData()` | 获取当前表格数据 | - |
| `setData(data)` | 设置表格数据 | `data`: 二维数组 |
| `startEdit(row, col)` | 开始编辑指定单元格 | `row`: 行索引, `col`: 列索引 |
| `endEdit()` | 结束当前编辑 | - |
| `selectCell(row, col)` | 选择指定单元格 | `row`: 行索引, `col`: 列索引 |
| `getSelectedData()` | 获取选中的数据 | - |
| `copy()` | 复制选中内容 | - |
| `paste()` | 粘贴内容 | - |
| `autoFitColumnWidth(col)` | 自动调整指定列宽 | `col`: 列索引 |
| `autoFitAllColumnWidth()` | 自动调整所有列宽 | - |
| `resize()` | 重新计算表格尺寸 | - |

## 筛选功能

组件内置了筛选功能：

1. **列筛选**: 选择特定列进行筛选
2. **全局筛选**: 在所有列中搜索关键词
3. **重置筛选**: 一键清除所有筛选条件

## 复制粘贴功能

- **复制**: 选中单元格后按 `Ctrl+C`
- **粘贴**: 选中目标单元格后按 `Ctrl+V`
- **右键菜单**: 支持右键复制粘贴操作

## 编辑功能

- **智能编辑器**: 根据数据类型自动选择合适的编辑器
  - 文本列: 文本输入框
  - 日期列: 日期选择器
  - 选项列: 下拉选择框（当唯一值≤10个时）
- **编辑操作**:
  - 双击单元格进入编辑模式
  - 按 `Enter` 确认编辑
  - 按 `Esc` 取消编辑
  - 方向键可在编辑模式下移动到相邻单元格
- **数据验证**: 支持自定义验证规则
- **实时更新**: 编辑后立即更新数据并触发事件

## 样式自定义

组件使用 scoped 样式，可以通过以下方式自定义：

```vue
<style>
.vtable-component {
  /* 自定义组件样式 */
}

.filter-panel {
  /* 自定义筛选面板样式 */
}
</style>
```

## 注意事项

1. 数据数组的第一行必须是标题行
2. 所有数据行的列数应该与标题行保持一致
3. 组件会自动处理数据类型转换
4. 大量数据时建议启用虚拟滚动（通过 tableOptions 配置）

## 示例项目

查看 `src/views/TableComponentDemo.vue` 获取完整的使用示例。
