<template>
  <div class="smart-reconciliation-container">
    <div class="header">
      <h1 class="title">智能对账抵消</h1>
      <el-button type="primary" @click="executeReconciliation" :loading="loading" class="execute-btn">
        {{ loading ? '执行中...' : '开始执行' }}
      </el-button>
    </div>

    <div v-if="!loading && reportOffsetData.length === 0 && internalOffsetData.length === 0" class="placeholder">
      <p>点击“开始执行”按钮以生成对账抵消报表。</p>
    </div>

    <div v-if="reportOffsetData.length > 0 || internalOffsetData.length > 0" class="results-container">
      <el-tabs v-model="activeTab" class="results-tabs">
        <el-tab-pane label="报表抵消情况表" name="reportOffset">
          <div class="table-container">
            <VTableComponent
              v-if="activeTab === 'reportOffset' && reportOffsetData.length > 0"
              :data="reportOffsetData"
              :width="tableWidth"
              :height="tableHeight"
              :show-filter="true"
              :editable="false"
              :enable-copy-paste="true"
              :auto-width="true"
              :key="`report-offset-${reportOffsetData.length}`"
            />
            <div v-else-if="activeTab === 'reportOffset' && reportOffsetData.length === 0" class="no-data">
              <p>暂无报表抵消数据</p>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="内部交易抵消情况表" name="internalOffset">
          <div class="table-container">
            <VTableComponent
              v-if="activeTab === 'internalOffset' && internalOffsetData.length > 0"
              :data="internalOffsetData"
              :width="tableWidth"
              :height="tableHeight"
              :show-filter="true"
              :editable="false"
              :enable-copy-paste="true"
              :auto-width="true"
              :key="`internal-offset-${internalOffsetData.length}`"
            />
            <div v-else-if="activeTab === 'internalOffset' && internalOffsetData.length === 0" class="no-data">
              <p>暂无内部交易抵消数据</p>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import axios from 'axios';
import { ElButton, ElTabs, ElTabPane, ElMessage, ElLoading } from 'element-plus';
import VTableComponent from '@/components/VTableComponent.vue';

const loading = ref(false);
const activeTab = ref('reportOffset');
const reportOffsetData = ref([]);
const internalOffsetData = ref([]);

// 表格尺寸配置
const tableWidth = ref(1200);
const tableHeight = ref(500);

// 计算表格容器尺寸
const updateTableSize = () => {
  nextTick(() => {
    // 根据容器大小动态调整表格尺寸
    const container = document.querySelector('.results-container');
    if (container) {
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;

      // 预留一些边距
      tableWidth.value = Math.max(800, containerWidth - 80);
      tableHeight.value = Math.max(400, containerHeight - 150);
    }
  });
};

// 组件挂载后初始化表格尺寸
onMounted(() => {
  updateTableSize();
  // 监听窗口大小变化
  window.addEventListener('resize', updateTableSize);
});

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateTableSize);
});

const executeReconciliation = async () => {
  loading.value = true;
  reportOffsetData.value = [];
  internalOffsetData.value = [];

  try {
    // 开发环境模拟数据（用于测试VTable显示）
    const isDevelopment = false;

    if (isDevelopment) {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 模拟成功响应
      const mockResponse = {
        data: {
          success: true,
          data: {
            reportOffset: [
              ['抵消项目', '抵消科目', '本期抵消金额', '累计抵消金额', '抵消方向', '抵消依据', '状态'],
              ['内部销售收入', '主营业务收入', 2500000, 8500000, '借方', '内部交易确认单', '已抵消'],
              ['内部采购成本', '主营业务成本', 2500000, 8500000, '贷方', '内部交易确认单', '已抵消'],
              ['内部利息收入', '财务费用', 150000, 450000, '借方', '内部借款协议', '已抵消'],
              ['内部利息支出', '财务费用', 150000, 450000, '贷方', '内部借款协议', '已抵消'],
              ['内部管理费', '管理费用', 300000, 900000, '借方', '内部服务协议', '已抵消']
            ],
            internalOffset: [
              ['交易对手', '交易类型', '交易金额', '抵消金额', '未抵消余额', '交易日期', '抵消状态'],
              ['子公司A', '商品销售', 1500000, 1500000, 0, '2024-01-15', '完全抵消'],
              ['子公司B', '服务费收入', 800000, 800000, 0, '2024-01-20', '完全抵消'],
              ['子公司C', '资金拆借', 2000000, 1800000, 200000, '2024-02-01', '部分抵消'],
              ['子公司D', '管理费分摊', 500000, 500000, 0, '2024-02-10', '完全抵消'],
              ['子公司E', '技术服务费', 300000, 250000, 50000, '2024-02-15', '部分抵消']
            ]
          }
        }
      };

      // 使用模拟数据
      const { reportOffset, internalOffset } = mockResponse.data.data;
      reportOffsetData.value = reportOffset || [];
      internalOffsetData.value = internalOffset || [];

      ElMessage.success('执行成功！（开发模式 - 使用模拟数据）');

      // 默认显示有数据的tab
      if (reportOffsetData.value.length > 0) {
        activeTab.value = 'reportOffset';
      } else if (internalOffsetData.value.length > 0) {
        activeTab.value = 'internalOffset';
      }

      // 数据加载完成后更新表格尺寸
      nextTick(() => {
        updateTableSize();
      });

      return; // 开发模式直接返回，不执行实际API调用
    }

    // 生产环境：发送POST请求到后端API
    const response = await axios.post('http://localhost:8000/api/reconciliation/execute');

    if (response.data && response.data.success) {
      const { reportOffset, internalOffset } = response.data.data;

      reportOffsetData.value = reportOffset || [];
      internalOffsetData.value = internalOffset || [];

      if (reportOffsetData.value.length === 0 && internalOffsetData.value.length === 0) {
        ElMessage.info('执行成功，但未返回任何数据。');
      } else {
        ElMessage.success('执行成功！');
        // 默认显示有数据的tab
        if (reportOffsetData.value.length > 0) {
            activeTab.value = 'reportOffset';
        } else if (internalOffsetData.value.length > 0) {
            activeTab.value = 'internalOffset';
        }

        // 数据加载完成后更新表格尺寸
        nextTick(() => {
          updateTableSize();
        });
      }
    } else {
      throw new Error(response.data?.message || '返回数据格式不正确');
    }
  } catch (error) {
    console.error('执行失败:', error);
    ElMessage.error(`执行失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.smart-reconciliation-container {
  padding: 24px;
  
  .table-container {
    width: 100%;
    flex: 1 1 auto; /* 填满剩余空间，随父容器缩放 */
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 400px; /* 确保最小高度 */
  }
  background-color: #f7f8fa;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 98%;
  margin: 0 auto;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.execute-btn {
  font-size: 16px;
}

.placeholder {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #909399;
  background-color: #fff;
  border-radius: 8px;
  border: 1px dashed #dcdfe6;
}

.no-data {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #909399;
  background-color: #fff;
  border-radius: 8px;
  border: 1px dashed #dcdfe6;
  min-height: 200px;
}

.no-data p {
  margin: 0;
  font-size: 14px;
}

.results-container {
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.results-tabs {
  flex-grow: 1; /* Let el-tabs fill the container */
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content) {
  flex-grow: 1; /* Let content area fill the el-tabs */
  overflow: hidden;
  overflow: hidden; /* 内容区将填充剩余空间 */
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
}

:deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
  min-width: 1200px; /* Ensure minimum width for tab content */
  display: flex;
  flex-direction: column;
}

/* Ensure tables take full width */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}

</style>