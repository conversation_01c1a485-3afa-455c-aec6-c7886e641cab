<template>
  <div class="smart-reconciliation-container">
    <div class="header">
      <h1 class="title">智能对账抵消</h1>
      <el-button type="primary" @click="executeReconciliation" :loading="loading" class="execute-btn">
        {{ loading ? '执行中...' : '开始执行' }}
      </el-button>
    </div>

    <div v-if="!loading && reportOffsetData.length === 0 && internalOffsetData.length === 0" class="placeholder">
      <p>点击“开始执行”按钮以生成对账抵消报表。</p>
    </div>

    <div v-if="reportOffsetData.length > 0 || internalOffsetData.length > 0" class="results-container">
      <el-tabs v-model="activeTab" class="results-tabs">
        <el-tab-pane label="报表抵消情况表" name="reportOffset">
          <div class="table-container">
            <VTableComponent
              :data="reportOffsetData"
              v-if="activeTab === 'reportOffset'"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="内部交易抵消情况表" name="internalOffset">
          <div class="table-container">
            <VTableComponent
              :data="internalOffsetData"
              v-if="activeTab === 'internalOffset'"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import { ElButton, ElTabs, ElTabPane, ElMessage, ElLoading } from 'element-plus';
import VTableComponent from '@/components/VTableComponent.vue';

const loading = ref(false);
const activeTab = ref('reportOffset');
const reportOffsetData = ref([]);
const internalOffsetData = ref([]);

const executeReconciliation = async () => {
  loading.value = true;
  reportOffsetData.value = [];
  internalOffsetData.value = [];

  try {
    // 发送POST请求到后端API
    const response = await axios.post('http://localhost:8000/api/reconciliation/execute');

    if (response.data && response.data.success) {
      const { reportOffset, internalOffset } = response.data.data;

      reportOffsetData.value = reportOffset || [];
      internalOffsetData.value = internalOffset || [];

      if (reportOffsetData.value.length === 0 && internalOffsetData.value.length === 0) {
        ElMessage.info('执行成功，但未返回任何数据。');
      } else {
        ElMessage.success('执行成功！');
        // 默认显示有数据的tab
        if (reportOffsetData.value.length > 0) {
            activeTab.value = 'reportOffset';
        } else if (internalOffsetData.value.length > 0) {
            activeTab.value = 'internalOffset';
        }
      }
    } else {
      throw new Error(response.data?.message || '返回数据格式不正确');
    }
  } catch (error) {
    console.error('执行失败:', error);
    ElMessage.error(`执行失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.smart-reconciliation-container {
  padding: 24px;
  
  .table-container {
    width: 100%;
    flex: 1 1 auto; /* 填满剩余空间，随父容器缩放 */
    /*overflow: auto;*/ /* 显示滚动条 */
    position: relative;
  }
  background-color: #f7f8fa;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 98%;
  margin: 0 auto;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.execute-btn {
  font-size: 16px;
}

.placeholder {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #909399;
  background-color: #fff;
  border-radius: 8px;
  border: 1px dashed #dcdfe6;
}

.results-container {
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.results-tabs {
  flex-grow: 1; /* Let el-tabs fill the container */
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content) {
  flex-grow: 1; /* Let content area fill the el-tabs */
  overflow: hidden;
  overflow: hidden; /* 内容区将填充剩余空间 */
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
}

:deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
  min-width: 1200px; /* Ensure minimum width for tab content */
  display: flex;
  flex-direction: column;
}

/* Ensure tables take full width */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}

</style>