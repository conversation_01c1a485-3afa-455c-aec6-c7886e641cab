<template>
  <div class="smart-reconciliation-container">
    <div class="header">
      <h1 class="title">智能对账抵消</h1>
      <el-button type="primary" @click="executeReconciliation" :loading="loading" class="execute-btn">
        {{ loading ? '执行中...' : '开始执行' }}
      </el-button>
    </div>

    <div v-if="!loading && reportOffsetData.length === 0 && internalOffsetData.length === 0" class="placeholder">
      <p>点击“开始执行”按钮以生成对账抵消报表。</p>
    </div>

    <div v-if="reportOffsetData.length > 0 || internalOffsetData.length > 0" class="results-container">
      <el-tabs v-model="activeTab" class="results-tabs">
        <el-tab-pane label="报表抵消情况表" name="reportOffset">
          <div class="table-container">
            <VTableComponent
              v-if="activeTab === 'reportOffset' && reportOffsetData.length > 0"
              :data="reportOffsetData"
              :width="tableWidth"
              :height="tableHeight"
              :show-filter="true"
              :editable="false"
              :enable-copy-paste="true"
              :auto-width="true"
              :table-options="{
                scrollOption: {
                  horizontalMode: 'always',
                  verticalMode: 'always',
                  scrollbarVisible: true,
                  scrollbarFadeOut: false
                }
              }"
              :key="`report-offset-${reportOffsetData.length}`"
            />
            <div v-else-if="activeTab === 'reportOffset' && reportOffsetData.length === 0" class="no-data">
              <p>暂无报表抵消数据</p>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="内部交易抵消情况表" name="internalOffset">
          <div class="table-container">
            <VTableComponent
              v-if="activeTab === 'internalOffset' && internalOffsetData.length > 0"
              :data="internalOffsetData"
              :width="tableWidth"
              :height="tableHeight"
              :show-filter="true"
              :editable="false"
              :enable-copy-paste="true"
              :auto-width="true"
              :table-options="{
                scrollOption: {
                  horizontalMode: 'always',
                  verticalMode: 'always',
                  scrollbarVisible: true,
                  scrollbarFadeOut: false
                }
              }"
              :key="`internal-offset-${internalOffsetData.length}`"
            />
            <div v-else-if="activeTab === 'internalOffset' && internalOffsetData.length === 0" class="no-data">
              <p>暂无内部交易抵消数据</p>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import axios from 'axios';
import { ElButton, ElTabs, ElTabPane, ElMessage, ElLoading } from 'element-plus';
import VTableComponent from '@/components/VTableComponent.vue';

const loading = ref(false);
const activeTab = ref('reportOffset');
const reportOffsetData = ref([]);
const internalOffsetData = ref([]);

// 表格尺寸配置
const tableWidth = ref(1400);
const tableHeight = ref(800); // 大幅增加默认高度

// 计算表格容器尺寸
const updateTableSize = () => {
  nextTick(() => {
    // 根据容器大小动态调整表格尺寸
    const container = document.querySelector('.results-container');
    const tabPane = document.querySelector('.el-tab-pane');

    if (container && tabPane) {
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      const tabPaneHeight = tabPane.clientHeight;

      // 设置更大的表格尺寸，确保有足够空间显示滚动条
      tableWidth.value = Math.max(1200, containerWidth - 40);
      tableHeight.value = Math.max(600, Math.min(800, containerHeight - 100));

      console.log('更新表格尺寸:', {
        containerWidth,
        containerHeight,
        tabPaneHeight,
        tableWidth: tableWidth.value,
        tableHeight: tableHeight.value
      });
    } else {
      // 如果容器还没有渲染，使用默认尺寸
      tableWidth.value = 1400;
      tableHeight.value = 800;
      console.log('使用默认表格尺寸:', {
        tableWidth: tableWidth.value,
        tableHeight: tableHeight.value
      });
    }
  });
};

// 组件挂载后初始化表格尺寸
onMounted(() => {
  updateTableSize();
  // 监听窗口大小变化
  window.addEventListener('resize', updateTableSize);
});

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateTableSize);
});

const executeReconciliation = async () => {
  loading.value = true;
  reportOffsetData.value = [];
  internalOffsetData.value = [];

  try {
    // 开发环境模拟数据（用于测试VTable显示）
    const isDevelopment = false; // 启用开发模式以显示测试数据

    if (isDevelopment) {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 模拟成功响应
      const mockResponse = {
        data: {
          success: true,
          data: {
            reportOffset: [
              ['抵消项目', '抵消科目', '本期抵消金额', '累计抵消金额', '抵消方向', '抵消依据', '状态', '备注', '审批人', '审批日期'],
              ['内部销售收入', '主营业务收入', 2500000, 8500000, '借方', '内部交易确认单', '已抵消', '第一季度抵消', '张经理', '2024-01-15'],
              ['内部采购成本', '主营业务成本', 2500000, 8500000, '贷方', '内部交易确认单', '已抵消', '第一季度抵消', '李经理', '2024-01-15'],
              ['内部利息收入', '财务费用', 150000, 450000, '借方', '内部借款协议', '已抵消', '资金拆借利息', '王经理', '2024-01-20'],
              ['内部利息支出', '财务费用', 150000, 450000, '贷方', '内部借款协议', '已抵消', '资金拆借利息', '王经理', '2024-01-20'],
              ['内部管理费', '管理费用', 300000, 900000, '借方', '内部服务协议', '已抵消', '管理费分摊', '赵经理', '2024-01-25'],
              ['内部租金收入', '其他业务收入', 180000, 540000, '借方', '内部租赁合同', '已抵消', '办公场所租金', '孙经理', '2024-02-01'],
              ['内部租金支出', '管理费用', 180000, 540000, '贷方', '内部租赁合同', '已抵消', '办公场所租金', '孙经理', '2024-02-01'],
              ['内部技术服务费收入', '其他业务收入', 200000, 600000, '借方', '技术服务协议', '已抵消', 'IT服务费', '周经理', '2024-02-05'],
              ['内部技术服务费支出', '管理费用', 200000, 600000, '贷方', '技术服务协议', '已抵消', 'IT服务费', '周经理', '2024-02-05'],
              ['内部咨询费收入', '其他业务收入', 120000, 360000, '借方', '咨询服务合同', '已抵消', '财务咨询', '吴经理', '2024-02-10'],
              ['内部咨询费支出', '管理费用', 120000, 360000, '贷方', '咨询服务合同', '已抵消', '财务咨询', '吴经理', '2024-02-10'],
              ['内部培训费收入', '其他业务收入', 80000, 240000, '借方', '培训服务协议', '已抵消', '员工培训', '郑经理', '2024-02-15'],
              ['内部培训费支出', '管理费用', 80000, 240000, '贷方', '培训服务协议', '已抵消', '员工培训', '郑经理', '2024-02-15']
            ],
            internalOffset: [
              ['交易对手', '交易类型', '交易金额', '抵消金额', '未抵消余额', '交易日期', '抵消状态', '业务部门', '负责人', '联系电话'],
              ['子公司A', '商品销售', 1500000, 1500000, 0, '2024-01-15', '完全抵消', '销售部', '张三', '13800138001'],
              ['子公司B', '服务费收入', 800000, 800000, 0, '2024-01-20', '完全抵消', '服务部', '李四', '13800138002'],
              ['子公司C', '资金拆借', 2000000, 1800000, 200000, '2024-02-01', '部分抵消', '财务部', '王五', '13800138003'],
              ['子公司D', '管理费分摊', 500000, 500000, 0, '2024-02-10', '完全抵消', '管理部', '赵六', '13800138004'],
              ['子公司E', '技术服务费', 300000, 250000, 50000, '2024-02-15', '部分抵消', '技术部', '孙七', '13800138005'],
              ['子公司F', '租金收入', 240000, 240000, 0, '2024-02-20', '完全抵消', '资产部', '周八', '13800138006'],
              ['子公司G', '咨询费', 180000, 150000, 30000, '2024-02-25', '部分抵消', '咨询部', '吴九', '13800138007'],
              ['子公司H', '培训费', 120000, 120000, 0, '2024-03-01', '完全抵消', '人事部', '郑十', '13800138008'],
              ['子公司I', '设备租赁', 350000, 300000, 50000, '2024-03-05', '部分抵消', '设备部', '刘一', '13800138009'],
              ['子公司J', '物流费用', 280000, 280000, 0, '2024-03-10', '完全抵消', '物流部', '陈二', '13800138010'],
              ['子公司K', '广告费用', 160000, 140000, 20000, '2024-03-15', '部分抵消', '市场部', '杨三', '13800138011'],
              ['子公司L', '保险费用', 90000, 90000, 0, '2024-03-20', '完全抵消', '风控部', '黄四', '13800138012']
            ]
          }
        }
      };

      // 使用模拟数据
      const { reportOffset, internalOffset } = mockResponse.data.data;
      reportOffsetData.value = reportOffset || [];
      internalOffsetData.value = internalOffset || [];

      ElMessage.success('执行成功！（开发模式 - 使用模拟数据）');

      // 默认显示有数据的tab
      if (reportOffsetData.value.length > 0) {
        activeTab.value = 'reportOffset';
      } else if (internalOffsetData.value.length > 0) {
        activeTab.value = 'internalOffset';
      }

      // 数据加载完成后更新表格尺寸
      nextTick(() => {
        updateTableSize();
      });

      return; // 开发模式直接返回，不执行实际API调用
    }

    // 生产环境：发送POST请求到后端API
    const response = await axios.post('http://localhost:8000/api/reconciliation/execute');

    if (response.data && response.data.success) {
      const { reportOffset, internalOffset } = response.data.data;

      reportOffsetData.value = reportOffset || [];
      internalOffsetData.value = internalOffset || [];

      if (reportOffsetData.value.length === 0 && internalOffsetData.value.length === 0) {
        ElMessage.info('执行成功，但未返回任何数据。');
      } else {
        ElMessage.success('执行成功！');
        // 默认显示有数据的tab
        if (reportOffsetData.value.length > 0) {
            activeTab.value = 'reportOffset';
        } else if (internalOffsetData.value.length > 0) {
            activeTab.value = 'internalOffset';
        }

        // 数据加载完成后更新表格尺寸
        nextTick(() => {
          updateTableSize();
        });
      }
    } else {
      throw new Error(response.data?.message || '返回数据格式不正确');
    }
  } catch (error) {
    console.error('执行失败:', error);
    ElMessage.error(`执行失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.smart-reconciliation-container {
  padding: 24px;
  
  .table-container {
    width: 100%;
    flex: 1 1 auto; /* 填满剩余空间，随父容器缩放 */
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 600px; /* 大幅增加最小高度 */
    height: 100%; /* 占满父容器高度 */
    overflow: visible; /* 允许VTable内部滚动条显示 */
  }
  background-color: #f7f8fa;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 98%;
  margin: 0 auto;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.execute-btn {
  font-size: 16px;
}

.placeholder {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #909399;
  background-color: #fff;
  border-radius: 8px;
  border: 1px dashed #dcdfe6;
}

.no-data {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #909399;
  background-color: #fff;
  border-radius: 8px;
  border: 1px dashed #dcdfe6;
  min-height: 200px;
}

.no-data p {
  margin: 0;
  font-size: 14px;
}

.results-container {
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: visible; /* 改为visible，允许内部滚动 */
  background-color: #fff;
  min-height: 800px; /* 大幅增加最小高度 */
  height: calc(100vh - 150px); /* 使用视口高度 */
}

.results-tabs {
  flex-grow: 1; /* Let el-tabs fill the container */
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 750px; /* 确保tabs有足够高度 */
}

:deep(.el-tabs__content) {
  flex-grow: 1; /* Let content area fill the el-tabs */
  overflow: visible; /* 允许内部滚动 */
  height: calc(100% - 60px); /* 减去tab头部高度 */
  min-height: 700px; /* 确保内容区有足够高度 */
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
  flex-shrink: 0; /* 防止头部被压缩 */
}

:deep(.el-tabs__item) {
  font-size: 16px;
}

:deep(.el-tab-pane) {
  height: 100%;
  overflow: visible; /* 允许VTable内部滚动 */
  min-width: 1200px; /* 增加最小宽度 */
  min-height: 650px; /* 确保tab面板有足够高度 */
  display: flex;
  flex-direction: column;
}

/* VTable 特定样式 */
:deep(.vtable-container) {
  width: 100% !important;
  height: 100% !important;
  min-height: 600px !important;
}

/* 确保VTable滚动条正常显示 */
:deep(.vtable-scroll-container) {
  overflow: auto !important;
}

/* VTable 内部滚动区域样式 */
:deep(.vtable-body) {
  overflow: auto !important;
}

/* 强制显示滚动条 */
:deep(.vtable-scroll-bar) {
  display: block !important;
  opacity: 1 !important;
}

/* 调整VTable的canvas容器 */
:deep(.vtable-canvas-container) {
  overflow: visible !important;
}

</style>