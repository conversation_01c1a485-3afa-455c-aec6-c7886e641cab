<template>
  <BaseQueryComponent
    title="付款台账"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// 查询字段配置
const queryFields = [
  {
    key: 'paymentNumber',
    label: '付款单号',
    type: 'text',
    placeholder: '请输入付款单号',
    width: '180px'
  },
  {
    key: 'payeeName',
    label: '收款方',
    type: 'text',
    placeholder: '请输入收款方名称',
    width: '200px'
  },
  {
    key: 'paymentDate',
    label: '付款日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'amount',
    label: '付款金额',
    type: 'amount-range'
  },
  {
    key: 'paymentMethod',
    label: '付款方式',
    type: 'select',
    placeholder: '请选择付款方式',
    width: '150px',
    options: [
      { label: '银行转账', value: 'transfer' },
      { label: '现金支付', value: 'cash' },
      { label: '支票', value: 'check' },
      { label: '承兑汇票', value: 'draft' }
    ]
  },
  {
    key: 'purpose',
    label: '付款用途',
    type: 'text',
    placeholder: '请输入付款用途',
    width: '200px'
  }
]

// 模拟数据
const mockData = [
  ['付款单号', '收款方', '付款金额', '付款日期', '付款方式', '付款用途', '关联合同', '经办人', '审批人', '备注'],
  ['FK202401001', '北京建材有限公司', '100000.00', '2024-01-15', '银行转账', '材料款', 'HT202401001', '张三', '李经理', '首期付款'],
  ['FK202401002', '上海钢铁集团', '200000.00', '2024-01-18', '银行转账', '钢材采购', 'HT202401002', '王五', '李经理', ''],
  ['FK202401003', '广州运输公司', '15000.00', '2024-01-20', '现金支付', '运输费', '', '赵六', '张主管', '紧急运输'],
  ['FK202401004', '深圳装饰公司', '50000.00', '2024-01-22', '银行转账', '装修款', 'HT202401004', '孙七', '李经理', '进度款'],
  ['FK202401005', '天津机械厂', '300000.00', '2024-01-25', '承兑汇票', '设备款', 'HT202401005', '周八', '王总', '设备采购'],
  ['FK202401006', '重庆电力公司', '80000.00', '2024-01-28', '银行转账', '电力工程', 'HT202401006', '吴九', '李经理', ''],
  ['FK202401007', '成都保洁公司', '8000.00', '2024-01-30', '现金支付', '清洁服务', 'HT202401007', '郑十', '张主管', '月度服务费'],
  ['FK202401008', '西安科技公司', '120000.00', '2024-02-02', '银行转账', '技术服务', 'HT202401008', '刘一', '王总', '技术改造']
]
</script>
