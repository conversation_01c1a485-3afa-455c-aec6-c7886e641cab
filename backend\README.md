# 财务台账查询系统 API

基于 FastAPI 和 DuckDB 的财务台账查询系统后端服务。

## 功能特性

- 支持19个财务台账表的查询
- 基于DuckDB的高性能数据存储
- RESTful API接口
- 支持复杂查询条件（日期范围、金额范围、文本搜索等）
- 自动生成API文档

## 支持的台账表

1. 一体化合同台账 (`/api/query/integrated-contract`)
2. 专项储备 (`/api/query/special-reserve`)
3. 主数据 (`/api/query/master-data`)
4. 付款台账 (`/api/query/payment-ledger`)
5. 保证金台账 (`/api/query/guarantee-ledger`)
6. 内行查询 (`/api/query/internal-bank`)
7. 内部对账 (`/api/query/internal-reconciliation`)
8. 分供结算台账 (`/api/query/subcontractor-settlement`)
9. 外部确权台账 (`/api/query/external-confirmation`)
10. 应付汇总按供应商 (`/api/query/payable-by-supplier`)
11. 应付汇总按合同 (`/api/query/payable-by-contract`)
12. 总台账 (`/api/query/general-ledger`)
13. 成本表 (`/api/query/cost-ledger`)
14. 收款台账 (`/api/query/receipt-ledger`)
15. 资金整理 (`/api/query/fund-management`)

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

```bash
python start.py
```

或者直接运行：

```bash
python main.py
```

### 3. 访问服务

- API服务: http://localhost:8000
- API文档: http://localhost:8000/docs
- 交互式文档: http://localhost:8000/redoc

## API 使用示例

### 请求格式

所有查询API都使用POST方法，请求体格式如下：

```json
{
  "filters": {
    "fiscalYear": "2024",
    "postingDate": ["2024-01-01", "2024-12-31"],
    "contractAmountMin": 1000000,
    "contractAmountMax": 50000000,
    "supplierDesc": "建筑"
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 响应格式

```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    ["列名1", "列名2", "列名3"],
    ["数据1", "数据2", "数据3"],
    ["数据4", "数据5", "数据6"]
  ],
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 查询条件说明

- **文本字段**: 支持模糊匹配，如 `"supplierDesc": "建筑"` 会匹配包含"建筑"的供应商
- **日期范围**: 使用数组格式，如 `"postingDate": ["2024-01-01", "2024-12-31"]`
- **金额范围**: 使用Min/Max后缀，如 `"contractAmountMin": 1000000, "contractAmountMax": 50000000`
- **下拉选择**: 精确匹配，如 `"fiscalYear": "2024"`

## 数据库结构

系统使用DuckDB作为数据存储，数据库文件为 `financial_data.duckdb`。

### 表结构示例

#### 一体化合同台账 (integrated_contract)
- search_key: 查找主键
- organization_name: 组织机构名称
- project_name: 项目名称
- contract_amount: 合同金额
- settlement_amount: 结算金额
- 等...

## 开发说明

### 添加新的查询表

1. 在 `main.py` 的 `init_database()` 函数中添加表结构
2. 在 `additional_endpoints.py` 中添加查询函数
3. 在 `main.py` 中注册新的API端点
4. 在前端添加对应的查询组件

### 自定义查询条件

在 `build_where_clause()` 函数中可以自定义查询条件的构建逻辑。

## 注意事项

- 数据库文件会在首次运行时自动创建
- 示例数据会在启动时自动插入
- 所有查询都有1000条记录的限制
- 支持CORS，允许前端跨域访问

## 故障排除

### 常见问题

1. **端口被占用**: 修改 `main.py` 中的端口号
2. **依赖安装失败**: 确保Python版本 >= 3.8
3. **数据库连接失败**: 检查文件权限和磁盘空间

### 日志查看

服务器运行时会在控制台输出详细的请求日志，便于调试。
