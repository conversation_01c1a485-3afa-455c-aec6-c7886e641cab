# 项目报表页面改造说明

## 改造概述

本次改造将原有的项目报表页面完全重构，实现了左右分栏布局，左侧展示项目财务数据，右侧展示明细台账表格。

## 主要功能特性

### 1. 项目选择功能
- **远程搜索**: 支持输入关键词远程搜索项目
- **POST请求**: 使用POST请求切换项目，符合API规范
- **实时加载**: 项目切换时实时加载对应数据

### 2. 左侧数据展示区

#### 项目基本信息
- 项目名称
- 项目利润中心编码

#### 收入成本费用情况
- 主营业务收入（财务口径）
- 主营业务成本（财务口径）
- 附加税
- 累计确权（不含税）
- 项目累计间接费用（商务口径）
- 项目累计机械费用（商务口径）
- 非分包安全费（即财务一体化报销的安全支出）

#### 财务检查部分
- 专项储备余额
- 合同履约成本余额
- 原材料余额
- 暂估应付余额
- 总包结算额
- 总包付款额
- 总包暂估额
- 往来对账检查
- 应交税费待转销
- 由税额反推开票额(只适用一般计税）

#### 资金收付情况
- 累计含税确权
- 累计业主收款
- 累计保证金收款
- 累计分供结算
- 累计分供付款
- 累计税金付款
- 累计管理费用
- 累计其他类型收付款(反向推出）
- 原始存量
- 内部借款
- 保理借款
- 内部往来挂总部(一般为票据）
- 内部往来需调整
- **真实资金余额**（高亮显示）

### 3. 右侧明细表格区

#### 支持的台账类型
1. **应付供应商汇总台账** - 供应商应付款汇总信息
2. **应付合同汇总台账** - 合同应付款汇总信息
3. **付款台账** - 详细付款记录
4. **分供结算台账** - 分包商结算记录
5. **成本台账** - 项目成本明细
6. **资金整理** - 资金流水整理
7. **收款台账** - 详细收款记录
8. **外部确权台账** - 外部确权记录
9. **安全费台账** - 安全费用支出记录
10. **内部对账** - 内部对账记录

#### 表格功能特性
- **VTable组件**: 使用高性能的VTable组件展示数据
- **筛选功能**: 内置筛选面板，支持按列筛选和全局搜索
- **复制粘贴**: 支持Ctrl+C/Ctrl+V操作
- **响应式**: 自动适应不同屏幕尺寸

## 技术实现

### 前端技术栈
- **Vue 3**: 使用Composition API
- **Element Plus**: UI组件库
- **VTable**: 高性能表格组件
- **Axios**: HTTP请求库

### 关键组件
- `ProjectReportView.vue`: 主页面组件
- `VTableComponent.vue`: 可复用的表格组件

### 数据流
1. 用户搜索项目 → 调用项目搜索API
2. 用户选择项目 → 调用项目数据API (POST请求)
3. 数据加载完成 → 更新左侧数据展示和右侧表格
4. 用户切换台账 → 更新右侧表格数据

## API接口

### 项目数据查询
```
POST /api/project/data
```

请求格式：
```json
{
  "projectId": "PRJ001",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

详细API文档请参考：[API规范文档](./api-specification.md)

## 样式特性

### 布局设计
- **左右分栏**: 左侧400px固定宽度，右侧自适应
- **响应式**: 小屏幕下自动切换为上下布局
- **卡片式**: 使用卡片式设计，层次分明

### 数据展示
- **表格样式**: 左侧数据采用标签-值的表格样式
- **金额格式化**: 自动格式化为货币格式
- **高亮显示**: 重要数据（如真实资金余额）高亮显示
- **状态标识**: 不同状态使用不同颜色标识

### 交互体验
- **加载状态**: 数据加载时显示loading状态
- **空状态**: 无数据时显示友好的空状态提示
- **错误处理**: 网络错误时显示错误提示

## 使用说明

### 基本操作流程
1. 在顶部搜索框输入项目关键词
2. 从下拉列表中选择目标项目
3. 系统自动加载项目数据并展示在左侧
4. 点击右侧的台账按钮切换不同的明细表格
5. 使用表格内置的筛选功能查找特定数据

### 快捷操作
- **刷新数据**: 点击"刷新数据"按钮重新加载当前项目数据
- **表格筛选**: 在表格筛选面板中输入关键词快速筛选
- **复制数据**: 选中表格数据后使用Ctrl+C复制

## 注意事项

1. **数据权限**: 用户只能查看有权限的项目数据
2. **性能优化**: 大数据量表格使用虚拟滚动优化性能
3. **缓存策略**: 项目数据有5分钟缓存，避免频繁请求
4. **错误处理**: 网络异常时会使用模拟数据保证页面可用性

## 后续优化建议

1. **数据导出**: 增加Excel导出功能
2. **图表展示**: 为关键指标增加图表展示
3. **历史对比**: 增加历史数据对比功能
4. **自定义筛选**: 增加更多自定义筛选条件
5. **实时更新**: 考虑使用WebSocket实现数据实时更新

## 文件结构

```
src/views/ProjectReportView.vue          # 主页面组件
src/components/VTableComponent.vue       # 表格组件
docs/api-specification.md               # API规范文档
docs/ProjectReportView-README.md        # 本说明文档
src/test/ProjectReportView.test.js       # 测试文件
```
