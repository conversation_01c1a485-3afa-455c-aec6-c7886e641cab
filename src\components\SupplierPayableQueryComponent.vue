<template>
  <BaseQueryComponent
    title="应付供应商汇总台账"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// 查询字段配置
const queryFields = [
  {
    key: 'supplierName',
    label: '供应商名称',
    type: 'text',
    placeholder: '请输入供应商名称',
    width: '200px'
  },
  {
    key: 'contractNumber',
    label: '合同编号',
    type: 'text',
    placeholder: '请输入合同编号',
    width: '180px'
  },
  {
    key: 'paymentDate',
    label: '应付日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'amount',
    label: '应付金额',
    type: 'amount-range'
  },
  {
    key: 'status',
    label: '付款状态',
    type: 'select',
    placeholder: '请选择状态',
    width: '150px',
    options: [
      { label: '未付款', value: 'unpaid' },
      { label: '部分付款', value: 'partial' },
      { label: '已付款', value: 'paid' },
      { label: '逾期', value: 'overdue' }
    ]
  },
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    placeholder: '请输入项目名称',
    width: '200px'
  }
]

// 模拟数据
const mockData = [
  ['供应商名称', '合同编号', '项目名称', '应付金额', '已付金额', '未付金额', '应付日期', '付款状态', '联系人', '联系电话'],
  ['北京建材有限公司', 'HT202401001', '办公楼建设项目', '500000.00', '300000.00', '200000.00', '2024-01-15', '部分付款', '张经理', '13800138001'],
  ['上海钢铁集团', 'HT202401002', '厂房改造工程', '800000.00', '800000.00', '0.00', '2024-01-20', '已付款', '李总监', '13800138002'],
  ['广州水泥厂', 'HT202401003', '道路硬化项目', '350000.00', '0.00', '350000.00', '2024-01-25', '未付款', '王主任', '13800138003'],
  ['深圳装饰公司', 'HT202401004', '室内装修工程', '120000.00', '60000.00', '60000.00', '2024-01-30', '部分付款', '赵设计师', '13800138004'],
  ['天津机械厂', 'HT202401005', '设备采购项目', '950000.00', '0.00', '950000.00', '2024-02-05', '逾期', '孙工程师', '13800138005'],
  ['重庆电力公司', 'HT202401006', '电力设施建设', '600000.00', '400000.00', '200000.00', '2024-02-10', '部分付款', '周经理', '13800138006'],
  ['成都运输集团', 'HT202401007', '物流配送服务', '280000.00', '280000.00', '0.00', '2024-02-15', '已付款', '吴队长', '13800138007'],
  ['西安科技公司', 'HT202401008', '智能化改造', '450000.00', '150000.00', '300000.00', '2024-02-20', '部分付款', '郑工', '13800138008']
]
</script>
