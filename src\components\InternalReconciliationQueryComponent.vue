<template>
  <BaseQueryComponent
    title="内部对账"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/internal-reconciliation'

// 查询字段配置
const queryFields = [
  {
    key: 'customerCode',
    label: '客商编码',
    type: 'text',
    placeholder: '请输入客商编码',
    width: '150px'
  },
  {
    key: 'customerName',
    label: '客商名称',
    type: 'text',
    placeholder: '请输入客商名称',
    width: '200px'
  },
  {
    key: 'postingDate',
    label: '过帐日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'voucherNumber',
    label: '凭证编号',
    type: 'text',
    placeholder: '请输入凭证编号',
    width: '180px'
  },
  {
    key: 'reason',
    label: '事由',
    type: 'text',
    placeholder: '请输入事由',
    width: '200px'
  },
  {
    key: 'settlementAmount',
    label: '结算额',
    type: 'amount-range'
  },
  {
    key: 'paymentAmount',
    label: '付款额',
    type: 'amount-range'
  },
  {
    key: 'estimatedAmount',
    label: '暂估额',
    type: 'amount-range'
  },
  {
    key: 'profitCenter',
    label: '利润中心',
    type: 'text',
    placeholder: '请输入利润中心',
    width: '150px'
  }
]
</script>
