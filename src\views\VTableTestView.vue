<template>
  <div class="vtable-test-view">
    <h1>VTable 测试页面</h1>
    
    <div class="test-section">
      <h2>测试自动列宽、滚动和明亮主题</h2>
      <p>这个表格应该：</p>
      <ul>
        <li>✅ 自动调整列宽以适应内容</li>
        <li>✅ 支持水平滚动</li>
        <li>✅ 使用明亮主题</li>
        <li>✅ 列宽可以手动调整</li>
      </ul>
      
      <div class="table-wrapper">
        <VTableComponent
          :data="testData"
          :width="800"
          :height="400"
          :show-filter="true"
        />
      </div>
    </div>

    <div class="test-section">
      <h2>宽数据测试</h2>
      <p>这个表格有很多列，测试水平滚动：</p>
      
      <div class="table-wrapper">
        <VTableComponent
          :data="wideData"
          :width="600"
          :height="300"
          :show-filter="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VTableComponent from '@/components/VTableComponent.vue'

// 测试数据 - 包含不同长度的内容
const testData = ref([
  ['姓名', '职位', '部门', '邮箱地址', '电话号码', '入职日期', '薪资'],
  ['张三', '高级软件工程师', '技术研发部', '<EMAIL>', '13800138001', '2023-01-15', '15000'],
  ['李四', '产品经理', '产品部', '<EMAIL>', '13800138002', '2022-06-20', '18000'],
  ['王五', 'UI设计师', '设计部', '<EMAIL>', '13800138003', '2023-03-10', '12000'],
  ['赵六', '数据分析师', '数据部', '<EMAIL>', '13800138004', '2022-11-05', '14000'],
  ['钱七', '运营专员', '市场运营部', '<EMAIL>', '13800138005', '2023-05-18', '10000'],
  ['孙八', '人力资源专员', '人力资源部', '<EMAIL>', '13800138006', '2022-08-12', '9000'],
  ['周九', '财务分析师', '财务部', '<EMAIL>', '13800138007', '2023-02-28', '13000'],
  ['吴十', '客户服务代表', '客户服务部', '<EMAIL>', '13800138008', '2023-04-03', '8000']
])

// 宽数据测试 - 很多列，测试水平滚动
const wideData = ref([
  ['员工编号', '姓名', '部门', '职位', '入职日期', '基本工资', '绩效奖金', '津贴补助', '社保缴费', '公积金缴费', '个人所得税', '实发工资', '联系电话', '邮箱地址', '家庭住址', '紧急联系人', '紧急联系电话', '学历', '专业', '工作经验'],
  ['EMP001', '张三', '技术研发部', '高级软件工程师', '2023-01-15', '15000', '3000', '1500', '1800', '1200', '2100', '15600', '13800138001', '<EMAIL>', '北京市朝阳区xxx街道xxx号', '张四', '13900139001', '本科', '计算机科学与技术', '5年'],
  ['EMP002', '李四', '产品部', '产品经理', '2022-06-20', '18000', '4000', '2000', '2160', '1440', '2800', '18600', '13800138002', '<EMAIL>', '上海市浦东新区xxx路xxx号', '李五', '13900139002', '硕士', '工商管理', '8年'],
  ['EMP003', '王五', '设计部', 'UI设计师', '2023-03-10', '12000', '2000', '1000', '1440', '960', '1400', '12200', '13800138003', '<EMAIL>', '广州市天河区xxx大道xxx号', '王六', '13900139003', '本科', '视觉传达设计', '3年'],
  ['EMP004', '赵六', '数据部', '数据分析师', '2022-11-05', '14000', '2500', '1200', '1680', '1120', '1800', '14300', '13800138004', '<EMAIL>', '深圳市南山区xxx街xxx号', '赵七', '13900139004', '硕士', '统计学', '6年'],
  ['EMP005', '钱七', '市场运营部', '运营专员', '2023-05-18', '10000', '1500', '800', '1200', '800', '1000', '10100', '13800138005', '<EMAIL>', '杭州市西湖区xxx路xxx号', '钱八', '13900139005', '本科', '市场营销', '2年']
])
</script>

<style scoped>
.vtable-test-view {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.test-section h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.test-section p {
  color: #666;
  margin-bottom: 10px;
}

.test-section ul {
  color: #666;
  margin-bottom: 20px;
}

.test-section li {
  margin-bottom: 5px;
}

.table-wrapper {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
</style>
