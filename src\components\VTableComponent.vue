<template>
  <div class="vtable-component">
    <!-- 筛选面板 -->
    <div v-if="showFilter" class="filter-panel">
      <div class="filter-section">
        <div class="filter-item">
          <select v-model="filterColumn">
            <option value="">所有列</option>
            <option
              v-for="(col, index) in columns"
              :key="index"
              :value="index.toString()"
            >
              {{ col.title }}
            </option>
          </select>
        </div>
        <div class="filter-item">
          <input v-model="filterText" placeholder="输入关键词筛选" @keyup.enter="doFilter" />
        </div>
        <button class="primary-btn" @click="doFilter">筛选</button>
        <button class="default-btn" @click="resetFilter">重置</button>
      </div>

      <!-- 功能按钮区域 -->
      <div class="action-section" v-if="editable || enableCopyPaste">
        <button v-if="enableCopyPaste" class="action-btn" @click="handleCopy" title="复制选中内容">
          📋 复制
        </button>
        <button v-if="enableCopyPaste" class="action-btn" @click="handlePaste" title="粘贴内容">
          📄 粘贴
        </button>
        <button v-if="autoWidth" class="action-btn" @click="handleAutoFitColumns" title="自动调整所有列宽">
          📏 自适应列宽
        </button>
        <button class="action-btn" @click="handleExportData" title="导出表格数据">
          💾 导出数据
        </button>
      </div>
    </div>
    
    <!-- 表格容器 -->
    <div 
      ref="tableRef" 
      :style="{ width: width + 'px', height: height + 'px' }"
      class="table-container"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'
import * as VTable from '@visactor/vtable'
import { InputEditor, DateInputEditor, ListEditor } from '@visactor/vtable-editors'

// Props定义
const props = defineProps({
  // 二维数组数据，第一行为标题
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  // 表格宽度
  width: {
    type: Number,
    default: 600
  },
  // 表格高度
  height: {
    type: Number,
    default: 300
  },
  // 是否显示筛选面板
  showFilter: {
    type: Boolean,
    default: true
  },
  // 额外的表格配置
  tableOptions: {
    type: Object,
    default: () => ({})
  },
  // 是否启用编辑功能
  editable: {
    type: Boolean,
    default: true
  },
  // 是否启用复制粘贴
  enableCopyPaste: {
    type: Boolean,
    default: true
  },
  // 是否自动调整列宽
  autoWidth: {
    type: Boolean,
    default: true
  }
})

// 响应式数据
const tableRef = ref()
const filterText = ref('')
const filterColumn = ref('')
const columns = ref([])
const records = ref([])
const originalRecords = ref([])
let vtableInstance = null

// 注册编辑器 - 按照官方文档的正确方式
try {
  // 创建编辑器实例
  const inputEditor = new InputEditor();
  const dateInputEditor = new DateInputEditor();
  const listEditor = new ListEditor({ values: ['是', '否'] });

  // 注册编辑器到VTable
  VTable.register.editor('input-editor', inputEditor);
  VTable.register.editor('date-input-editor', dateInputEditor);
  VTable.register.editor('list-editor', listEditor);

  console.log('编辑器注册成功');
} catch (error) {
  console.error('编辑器注册失败:', error);
}

// 基础表格配置 - 增强版
const getBaseTableOptions = () => ({
  theme: VTable.themes.BRIGHT,
  // 自动列宽配置
  widthMode: props.autoWidth ? 'autoWidth' : 'standard',
  autoFillWidth: false,
  limitMaxAutoWidth: 300, // 限制最大自动列宽
  limitMinWidth: 60, // 限制最小列宽

  // 编辑配置 - 使用VTable 1.18.4的正确API
  editCellTrigger: props.editable ? 'doubleclick' : 'none',

  // 复制粘贴配置
  keyboardOptions: {
    copySelected: props.enableCopyPaste,
    pasteValueToCell: props.enableCopyPaste,
    selectAllOnCtrlA: true,
    moveEditCellOnArrowKeys: true,
    moveEditCellOnEnter: true
  },

  // 选择配置
  select: {
    headerSelectMode: 'inline',
    disableHeaderSelect: false,
    disableSelect: false
  },

  // 样式配置
  autoWrapText: false,
  headerStyle: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: 'bold',
    lineHeight: 20,
    paddingTop: 4,
    paddingBottom: 4
  },
  bodyStyle: {
    fontSize: 11,
    textAlign: 'left',
    lineHeight: 18,
    paddingTop: 3,
    paddingBottom: 3
  },

  // 布局配置
  frozenColCount: 1, // 冻结第一列
  defaultColWidth: 120,
  rowHeight: 32, // 设置行高
  headerRowHeight: 36, // 表头行高

  // 滚动配置
  scrollOption: {
    horizontalMode: 'always',
    verticalMode: 'auto',
    scrollbarSize: 12,
    scrollbarFadeOut: false,
    scrollbarVisible: true,
    wheelDeltaX: 60,
    wheelDeltaY: 60
  },

  // 工具提示
  tooltip: {
    enable: true,
    placement: 'top',
    trigger: 'hover',
    showDelay: 500,
    hideDelay: 200
  }
})

// 从二维数组生成列配置和记录
function generateTableData(data) {
  if (!data || data.length === 0) {
    columns.value = []
    records.value = []
    originalRecords.value = []
    return
  }

  // 第一行作为标题
  const headers = data[0]
  const dataRows = data.slice(1)

  // 智能检测列数据类型
  const detectColumnType = (columnIndex) => {
    let isNumeric = true;
    let isDate = true;
    let hasOptions = false;
    const uniqueValues = new Set();

    for (let i = 0; i < Math.min(dataRows.length, 20); i++) {
      const cellValue = dataRows[i][columnIndex];
      const strValue = String(cellValue || '').trim();

      if (strValue) {
        uniqueValues.add(strValue);

        // 检查是否是数字
        if (isNumeric && isNaN(Number(cellValue)) && cellValue !== null && cellValue !== undefined) {
          isNumeric = false;
        }

        // 检查是否可能是日期
        if (isDate && !/^\d{4}[-/]\d{1,2}[-/]\d{1,2}/.test(strValue)) {
          isDate = false;
        }
      }
    }

    // 如果唯一值少于10个且不是数字/日期，可能适合下拉选择
    hasOptions = uniqueValues.size <= 10 && uniqueValues.size > 1 && !isNumeric && !isDate;

    return {
      isNumeric,
      isDate,
      hasOptions,
      uniqueValues: Array.from(uniqueValues)
    };
  };

  // 智能计算列宽度
  const calculateColumnWidth = (columnIndex) => {
    if (!props.autoWidth) {
      return 120; // 默认宽度
    }

    const title = headers[columnIndex] || '';
    const titleLength = title.toString().length;

    let maxContentLength = 0;
    for (let i = 0; i < Math.min(dataRows.length, 10); i++) {
      const cellValue = dataRows[i][columnIndex];
      const strValue = String(cellValue || '');
      maxContentLength = Math.max(maxContentLength, strValue.length);
    }

    const { isNumeric, isDate } = detectColumnType(columnIndex);

    // 根据类型和长度设置宽度
    if (isNumeric) {
      return Math.max(80, Math.min(maxContentLength * 8 + 20, 150));
    } else if (isDate) {
      return 120;
    } else {
      const baseWidth = Math.max(titleLength, maxContentLength) * 8 + 20;
      return Math.min(Math.max(100, baseWidth), 250);
    }
  };

  // 生成列配置
  columns.value = headers.map((title, index) => {
    const width = calculateColumnWidth(index);
    const columnType = detectColumnType(index);

    const columnConfig = {
      field: index.toString(),
      title: title,
      cellType: 'text',
      style: {
        textAlign: columnType.isNumeric ? 'right' : 'left',
        padding: [4, 8, 4, 8]
      }
    };

    // 设置列宽
    if (props.autoWidth) {
      columnConfig.minWidth = 60;
      columnConfig.maxWidth = 300;
    } else {
      columnConfig.width = width;
      columnConfig.minWidth = Math.min(width, 120);
    }

    // 配置编辑器 - 按照官方文档的正确方式
    if (props.editable) {
      // 根据数据类型配置编辑器
      if (columnType.isDate) {
        columnConfig.editor = 'date-input-editor';
      } else if (columnType.hasOptions && columnType.uniqueValues.length > 0) {
        columnConfig.editor = 'list-editor';
      } else {
        columnConfig.editor = 'input-editor';
      }
    }

    return columnConfig;
  });

  // 生成记录数据
  const recordsData = dataRows.map(row => {
    const record = {}
    row.forEach((cell, index) => {
      record[index.toString()] = cell
    })
    return record
  })

  records.value = [...recordsData]
  originalRecords.value = [...recordsData]
}

// 初始化表格
function initTable() {
  if (!tableRef.value || columns.value.length === 0) return

  // 获取基础配置
  const baseOptions = getBaseTableOptions();

  // 合并配置
  const options = {
    ...baseOptions,
    ...props.tableOptions,
    columns: columns.value,
    records: records.value,
    container: tableRef.value,
    width: props.width,
    height: props.height
  };

  // 调试输出
  console.log('VTable配置:', {
    editCellTrigger: options.editCellTrigger,
    editable: props.editable,
    columnsWithEditor: columns.value.filter(col => col.editor).length,
    columns: columns.value.map(col => ({ title: col.title, editor: col.editor }))
  });

  // 创建表格实例
  try {
    vtableInstance = new VTable.ListTable(tableRef.value, options)

    // 添加事件监听
    setupEventListeners();

    // 在实例创建后，确保DOM更新和布局计算完成后再更新滚动条和尺寸
    nextTick(() => {
      if (vtableInstance) {
        vtableInstance.resize()
        vtableInstance.updateScrollBar()

        // 延迟再次更新，确保滚动条正常显示
        setTimeout(() => {
          if (vtableInstance) {
            vtableInstance.updateScrollBar()
          }
        }, 300)
      }
    })
  } catch (error) {
    console.error('创建VTable实例失败:', error)
  }
}

// 设置事件监听器
function setupEventListeners() {
  if (!vtableInstance) return;

  // 监听单元格编辑开始事件
  vtableInstance.on('start_edit_cell', (args) => {
    console.log('开始编辑单元格:', args);
  });

  // 监听单元格编辑结束事件
  vtableInstance.on('end_edit_cell', (args) => {
    console.log('结束编辑单元格:', args);
  });

  // 监听单元格值变化事件
  vtableInstance.on('change_cell_value', (args) => {
    const { col, row, rawValue, oldValue } = args;
    console.log('单元格值变化:', { col, row, newValue: rawValue, oldValue });

    // 更新原始数据
    if (records.value[row] && originalRecords.value[row]) {
      records.value[row][col] = rawValue;
      originalRecords.value[row][col] = rawValue;
    }

    // 触发数据变化事件
    emitDataChange();
    emit('cell-edit', { col, row, newValue: rawValue, oldValue });
  });

  // 监听复制事件
  vtableInstance.on('copy_data', (args) => {
    console.log('复制数据:', args);
    emit('copy', args);
  });

  // 监听粘贴事件
  vtableInstance.on('paste_data', (args) => {
    console.log('粘贴数据:', args);
    // 粘贴后更新数据
    emitDataChange();
    emit('paste', args);
  });

  // 监听选择变化
  vtableInstance.on('selected_cell', (args) => {
    console.log('选择单元格:', args);
  });

  // 监听双击事件（用于调试编辑触发）
  vtableInstance.on('dblclick_cell', (args) => {
    console.log('双击单元格:', args);
    if (props.editable) {
      console.log('应该开始编辑');
    }
  });
}

// 定义事件发射
const emit = defineEmits(['data-change', 'cell-edit', 'copy', 'paste'])

// 发射数据变化事件
function emitDataChange() {
  // 将表格数据转换回二维数组格式
  const headers = columns.value.map(col => col.title);
  const dataRows = records.value.map(record =>
    columns.value.map(col => record[col.field])
  );
  const newData = [headers, ...dataRows];

  emit('data-change', newData);
}

// 筛选功能
function doFilter() {
  const text = filterText.value.trim().toLowerCase()
  const column = filterColumn.value

  if (!text) {
    records.value = [...originalRecords.value]
  } else {
    records.value = originalRecords.value.filter(row => {
      if (column && column !== '') {
        const cellValue = row[column]
        return String(cellValue).toLowerCase().includes(text)
      } else {
        return Object.values(row).some(value => 
          String(value).toLowerCase().includes(text)
        )
      }
    })
  }

  // 更新表格数据
  if (vtableInstance) {
    vtableInstance.setRecords(records.value)
    vtableInstance.render(true)
  }
}

// 重置筛选
function resetFilter() {
  filterText.value = ''
  filterColumn.value = ''
  records.value = [...originalRecords.value]
  
  if (vtableInstance) {
    vtableInstance.setRecords(records.value)
    vtableInstance.render(true)
  }
}

// 监听数据变化
watch(() => props.data, (newData) => {
  generateTableData(newData)
  nextTick(() => {
    if (vtableInstance) {
      vtableInstance.release()
    }
    initTable()
  })
}, { deep: true, immediate: true })

// 监听表格尺寸变化
watch([() => props.width, () => props.height], () => {
  if (vtableInstance) {
    nextTick(() => {
      vtableInstance.resize()
      vtableInstance.updateScrollBar()
    })
  }
})

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initTable()
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleWindowResize)
  })
})

// 组件卸载前清理资源
onUnmounted(() => {
  if (vtableInstance) {
    vtableInstance.release()
    vtableInstance = null
  }
  
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleWindowResize)
})

// 处理窗口大小变化
const handleWindowResize = () => {
  if (vtableInstance) {
    setTimeout(() => {
      vtableInstance.resize()
      vtableInstance.updateScrollBar()
    }, 200)
  }
}

// 处理复制操作
const handleCopy = () => {
  if (vtableInstance && props.enableCopyPaste) {
    try {
      vtableInstance.copy();
      console.log('复制成功');
    } catch (error) {
      console.error('复制失败:', error);
    }
  }
}

// 处理粘贴操作
const handlePaste = () => {
  if (vtableInstance && props.enableCopyPaste) {
    try {
      vtableInstance.paste();
      console.log('粘贴成功');
    } catch (error) {
      console.error('粘贴失败:', error);
    }
  }
}

// 处理自动调整列宽
const handleAutoFitColumns = () => {
  if (vtableInstance) {
    try {
      for (let i = 0; i < columns.value.length; i++) {
        vtableInstance.autoFitColumnWidth(i);
      }
      console.log('自动调整列宽完成');
    } catch (error) {
      console.error('自动调整列宽失败:', error);
    }
  }
}

// 处理导出数据
const handleExportData = () => {
  try {
    const headers = columns.value.map(col => col.title);
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field])
    );
    const exportData = [headers, ...dataRows];

    // 转换为CSV格式
    const csvContent = exportData.map(row =>
      row.map(cell => `"${String(cell || '').replace(/"/g, '""')}"`).join(',')
    ).join('\n');

    // 创建下载链接
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `table_data_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('导出数据成功');
  } catch (error) {
    console.error('导出数据失败:', error);
  }
}

// 暴露方法给父组件
defineExpose({
  resetFilter,
  doFilter,
  getTableInstance: () => vtableInstance,

  // 数据操作方法
  getData: () => {
    const headers = columns.value.map(col => col.title);
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field])
    );
    return [headers, ...dataRows];
  },

  setData: (newData) => {
    generateTableData(newData);
    if (vtableInstance) {
      vtableInstance.setRecords(records.value);
      vtableInstance.render(true);
    }
  },

  // 编辑相关方法
  startEdit: (row, col) => {
    if (vtableInstance && props.editable) {
      vtableInstance.startEditCell(col, row);
    }
  },

  endEdit: () => {
    if (vtableInstance) {
      vtableInstance.completeEdit();
    }
  },

  // 选择相关方法
  selectCell: (row, col) => {
    if (vtableInstance) {
      vtableInstance.selectCell(col, row);
    }
  },

  getSelectedData: () => {
    if (vtableInstance) {
      return vtableInstance.getSelectedData();
    }
    return null;
  },

  // 复制粘贴方法
  copy: () => {
    if (vtableInstance && props.enableCopyPaste) {
      vtableInstance.copy();
    }
  },

  paste: () => {
    if (vtableInstance && props.enableCopyPaste) {
      vtableInstance.paste();
    }
  },

  // 布局方法
  updateScrollBar: () => {
    if (vtableInstance) {
      vtableInstance.updateScrollBar()
      setTimeout(() => {
        if (vtableInstance) {
          vtableInstance.updateScrollBar()
        }
      }, 300)
    }
  },

  resize: () => {
    if (vtableInstance) {
      vtableInstance.resize()
      setTimeout(() => {
        if (vtableInstance) {
          vtableInstance.updateScrollBar()
        }
      }, 300)
    }
  },

  // 列宽调整
  autoFitColumnWidth: (col) => {
    if (vtableInstance) {
      vtableInstance.autoFitColumnWidth(col);
    }
  },

  autoFitAllColumnWidth: () => {
    if (vtableInstance) {
      for (let i = 0; i < columns.value.length; i++) {
        vtableInstance.autoFitColumnWidth(i);
      }
    }
  }
})
</script>

<style scoped>
.vtable-component {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.table-container {
  overflow: visible !important; /* 确保容器不会阻止表格内部的滚动条 */
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding-bottom: 15px; /* 为水平滚动条预留空间 */
}

/* 确保表格内部滚动容器正常工作 */
:deep(.vtable-scroll-container) {
  overflow: auto !important;
}

/* 调整滚动条样式 */
:deep(.vtable-scroll-container::-webkit-scrollbar) {
  width: 10px;
  height: 10px;
}

:deep(.vtable-scroll-container::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.vtable-scroll-container::-webkit-scrollbar-thumb) {
  background: #c0c4cc;
  border-radius: 4px;
}

:deep(.vtable-scroll-container::-webkit-scrollbar-thumb:hover) {
  background: #909399;
}

/* 调整单元格样式 */
:deep(.vtable-body-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filter-panel {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  border: 1px solid #e4e7ed;
}

.filter-section {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
}

.action-section {
  display: flex;
  gap: 8px;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-item select,
.filter-item input {
  padding: 6px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 140px;
  font-size: 12px;
  transition: all 0.3s;
}

.filter-item select:focus,
.filter-item input:focus {
  border-color: #409eff;
  outline: none;
}

.primary-btn,
.default-btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.primary-btn {
  background: #409eff;
  color: #fff;
}

.primary-btn:hover {
  background: #66b1ff;
}

.default-btn {
  background: #f4f4f5;
  color: #606266;
}

.default-btn:hover {
  background: #e9e9eb;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background: #fff;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
  color: #409eff;
}

.action-btn:active {
  background: #e6f7ff;
  border-color: #409eff;
}

/* 表格编辑状态样式 */
:deep(.vtable-cell-editing) {
  background-color: #e6f7ff !important;
  border: 2px solid #409eff !important;
}

/* 选中单元格样式 */
:deep(.vtable-cell-selected) {
  background-color: #f0f9ff !important;
  border: 1px solid #409eff !important;
}

/* 复制选择区域样式 */
:deep(.vtable-selection-border) {
  border: 2px dashed #67c23a !important;
}

/* 表格行悬停效果 */
:deep(.vtable-body-row:hover) {
  background-color: #f5f7fa !important;
}

</style>
