# BasicTableComponent 增强版使用指南

## 🚀 快速开始

### 基础用法

```vue
<template>
  <BasicTableComponent
    :data="tableData"
    :width="800"
    :height="400"
    :show-filter="true"
  />
</template>

<script setup>
import BasicTableComponent from '@/components/BasicTableComponent.vue'

const tableData = [
  ['姓名', '年龄', '城市', '薪资'],  // 表头
  ['张三', 28, '北京', '¥8000'],
  ['李四', 32, '上海', '¥12000'],
  ['王五', 25, '广州', '¥9000']
]
</script>
```

## 📋 Props 配置

### 基础配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `data` | Array | `[]` | 表格数据，第一行为表头 |
| `width` | Number | `800` | 表格宽度 |
| `height` | Number | `400` | 表格高度 |
| `showFilter` | Boolean | `true` | 是否显示筛选面板 |

### 高级配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enableVirtualScroll` | Boolean | `true` | 启用虚拟滚动 |
| `rowHeight` | Number | `32` | 行高(用于虚拟滚动) |
| `frozenColumns` | Number | `1` | 固定前几列 |
| `showPagination` | Boolean | `false` | 显示分页器 |
| `pageSize` | Number | `20` | 每页显示条数 |

## 🔍 筛选功能

### 筛选模式

1. **包含匹配** (默认) - 单元格包含关键词
2. **精确匹配** - 单元格完全等于关键词
3. **开头匹配** - 单元格以关键词开头
4. **结尾匹配** - 单元格以关键词结尾
5. **正则表达式** - 使用正则表达式匹配

### 高级筛选

#### 数值范围筛选
```
最小值: 1000
最大值: 5000
```

#### 日期范围筛选
```
开始日期: 2024-01-01
结束日期: 2024-12-31
```

### 筛选历史
- 自动保存最近10次筛选条件
- 点击历史记录快速应用
- 支持清空历史记录

## 🎯 API 方法

### 获取组件引用

```vue
<template>
  <BasicTableComponent ref="tableRef" :data="tableData" />
</template>

<script setup>
import { ref } from 'vue'

const tableRef = ref(null)
</script>
```

### 可用方法

```javascript
// 重置筛选
tableRef.value.resetFilter()

// 应用筛选
tableRef.value.applyFilter()

// 排序
tableRef.value.sortByColumn(columnIndex)

// 翻页
tableRef.value.changePage(pageNumber)

// 切换高级筛选
tableRef.value.toggleAdvancedFilter()

// 清空筛选历史
tableRef.value.clearFilterHistory()

// 获取筛选状态
const filterState = tableRef.value.getFilterState()

// 设置筛选状态
tableRef.value.setFilterState({
  column: '1',
  text: '张',
  mode: 'contains'
})
```

## 💡 使用技巧

### 1. 大数据量优化

```vue
<BasicTableComponent
  :data="largeData"
  :enable-virtual-scroll="true"
  :row-height="32"
  :page-size="50"
/>
```

### 2. 自定义列宽

表格支持拖拽调整列宽，将鼠标悬停在表头右边缘即可看到调整光标。

### 3. 快捷键

- `Enter` - 在筛选输入框中按Enter快速应用筛选
- 鼠标拖拽 - 调整列宽

### 4. 数据格式建议

```javascript
// 推荐的数据格式
const tableData = [
  // 表头 - 必须是第一行
  ['ID', '姓名', '年龄', '薪资', '入职日期'],
  
  // 数据行
  [1, '张三', 28, '¥8000', '2024-01-15'],
  [2, '李四', 32, '¥12000', '2023-06-20'],
  
  // 支持的数据类型
  [3, '王五', 25, 9000, '2024-03-10'],  // 数字
  [4, '赵六', 30, '¥15000', new Date().toISOString().split('T')[0]]  // 日期
]
```

## 🎨 样式自定义

### CSS变量

```css
.basic-table-component {
  --table-border-color: #e4e7ed;
  --table-header-bg: #fafbfc;
  --table-hover-bg: #f8f9fa;
  --filter-panel-bg: #fff;
}
```

### 自定义样式

```vue
<style>
/* 自定义筛选面板 */
.filter-panel {
  background: #f0f9ff;
  border: 2px solid #0ea5e9;
}

/* 自定义表格行 */
.data-table tbody tr:hover td {
  background-color: #e0f2fe;
}
</style>
```

## 🔧 故障排除

### 常见问题

1. **表格不显示数据**
   - 检查data格式，确保第一行是表头
   - 确保数据是响应式的

2. **滚动不流畅**
   - 对于大数据量，启用虚拟滚动
   - 调整rowHeight匹配实际行高

3. **筛选不生效**
   - 检查筛选模式是否正确
   - 确保数据类型匹配

4. **列宽调整不生效**
   - 确保表格容器有足够空间
   - 检查CSS是否有冲突

### 性能优化建议

1. **数据量 < 100行**: 关闭虚拟滚动
2. **数据量 100-1000行**: 启用虚拟滚动，pageSize设为50
3. **数据量 > 1000行**: 启用虚拟滚动，pageSize设为20-30

## 📱 响应式支持

表格组件支持响应式布局：

```vue
<BasicTableComponent
  :width="screenWidth < 768 ? 350 : 800"
  :height="screenHeight < 600 ? 300 : 500"
  :data="tableData"
/>
```

## 🔗 相关链接

- [演示页面](/enhanced-table-demo)
- [源码](src/components/BasicTableComponent.vue)
- [改进文档](ENHANCED_TABLE_IMPROVEMENTS.md)

---

**提示**: 访问 `/enhanced-table-demo` 查看完整的功能演示和示例代码。
