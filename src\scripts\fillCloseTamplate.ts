import { createUniver, defaultTheme, FUniver, LocaleType, merge, Univer } from '@univerjs/presets';

export default async function fillCloseTamplate(univerAPIInstance:FUniver){
      const sheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('快速取数');
      if (sheet) {
        var arr=sheet.getRange(0,0,sheet.getLastRow()+1,sheet.getLastColumn()+1).getValues();
        var groupIndex=0;
        var projectIndex=0;
        var reserveIndex=0;
        for(let i=0;i<arr[0].length;i++){
            if(arr[0][i]=='利润中心组名称'){groupIndex=i}
            if(arr[0][i]=='项目名称'){projectIndex=i}
            if(arr[0][i]=='专项储备余额'){reserveIndex=i}
        }}

      const sheet2 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('独立结账模板安全费');
      if (sheet2) {
        var arr2=sheet2.getRange(0,0,1,sheet2.getLastColumn()+1).getValues();
        var groupIndex2=0;
        var projectIndex2=0;
        var reserveIndex2=0;
        var ifenableIndex2=0;
        for(let i=0;i<arr2[0].length;i++){
            if(arr2[0][i]=='组织机构'){groupIndex2=i}
            if(arr2[0][i]=='项目名称'){projectIndex2=i}
            if(arr2[0][i]=='专项储备余额'){reserveIndex2=i}
            if(arr2[0][i]=='是否'){ifenableIndex2=i}
        }
        if (sheet2.getLastRow()>2){sheet2.deleteRows(3, sheet2.getLastRow()-2)}
        sheet2.getRange(1,ifenableIndex2).setValue('否');
        var k=2;
        for(let i=1;i<arr.length;i++){
            if (Number(arr[i][reserveIndex]) > 0.001 || Number(arr[i][reserveIndex]) < -0.001){
                sheet2.insertRowAfter(k)
                sheet2.getRange(k,groupIndex2).setValue(arr[i][groupIndex])
                sheet2.getRange(k,projectIndex2).setValue(arr[i][projectIndex])
                sheet2.getRange(k,reserveIndex2).setValue(arr[i][reserveIndex])
                sheet2.getRange(k,ifenableIndex2).setValue('是')
                k++
            }
        }

    }}

function fillCloseTamplate2(univerAPIInstance:FUniver){  
    const sheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
    if (sheet) {
      var arr=sheet.getRange(0,0,sheet.getLastRow()+1,sheet.getLastColumn()+1).getValues();
      var groupIndex=0;
      var projectIndex=0;
      var projectStatusIndex=0;
      var profitCenterIndex=0;
      var projectCodeIndex=0;
      var expectedIncomeIndex=0;
      var expectedCostIndex=0;
      var contractNumberIndex=0;
      var profitIndex=0;
      for(let i=0;i<arr[0].length;i++){
          if(arr[0][i]=='组织机构'){groupIndex=i}
          if(arr[0][i]=='项目名称'){projectIndex=i}
          if(arr[0][i]=='项目状态'){projectStatusIndex=i}
          if(arr[0][i]=='利润中心编码'){profitCenterIndex=i}
          if(arr[0][i]=='项目编码'){projectCodeIndex=i}
          if(arr[0][i]=='结账预计收入'){expectedIncomeIndex=i}
          if(arr[0][i]=='结账预计成本'){expectedCostIndex=i}
          if(arr[0][i]=='收入合同编号'){contractNumberIndex=i}
          if(arr[0][i]=='本期毛利'){profitIndex=i}
      }}

    const sheet2 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('独立结账模板收入成本');
    if (sheet2) {
      var arr2=sheet2.getRange(0,0,1,sheet2.getLastColumn()+1).getValues();
      var groupIndex2=0;
      var projectIndex2=0;
      var projectStatusIndex2=0;
      var profitCenterIndex2=0;
      var projectCodeIndex2=0;
      var expectedIncomeIndex2=0;
      var expectedCostIndex2=0;
      var contractNumberIndex2=0;
      for(let i=0;i<arr2[0].length;i++){
          if(arr2[0][i]=='组织机构'){groupIndex2=i}
          if(arr2[0][i]=='项目名称'){projectIndex2=i}
          if(arr2[0][i]=='项目状态'){projectStatusIndex2=i}
          if(arr2[0][i]=='利润中心编码'){profitCenterIndex2=i}
          if(arr2[0][i]=='项目编码'){projectCodeIndex2=i}
          if(arr2[0][i]=='结账预计收入'){expectedIncomeIndex2=i}
          if(arr2[0][i]=='结账预计成本'){expectedCostIndex2=i}
          if(arr2[0][i]=='收入合同编号'){contractNumberIndex2=i}
      }
      if (sheet2.getLastRow()>2){sheet2.deleteRows(3, sheet2.getLastRow()-2)}
      var k=2;
      for(let i=1;i<arr.length;i++){
          if (Number(arr[i][profitIndex]) > 0.001 || Number(arr[i][profitIndex]) < -0.001){
              sheet2.insertRowAfter(k)
              sheet2.getRange(k,groupIndex2).setValue(arr[i][groupIndex])
              sheet2.getRange(k,projectIndex2).setValue(arr[i][projectIndex])
              sheet2.getRange(k,projectStatusIndex2).setValue(arr[i][projectStatusIndex])
              sheet2.getRange(k,profitCenterIndex2).setValue(arr[i][profitCenterIndex])
              sheet2.getRange(k,projectCodeIndex2).setValue(arr[i][projectCodeIndex])
              sheet2.getRange(k,expectedIncomeIndex2).setValue(arr[i][expectedIncomeIndex])
              sheet2.getRange(k,expectedCostIndex2).setValue(arr[i][expectedCostIndex])
              sheet2.getRange(k,contractNumberIndex2).setValue(arr[i][contractNumberIndex])
              k++
          }
      }

  }


}



