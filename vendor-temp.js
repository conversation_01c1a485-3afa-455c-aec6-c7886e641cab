import{d1 as Qa,d2 as Pl}from"./deps-BQXE2yEJ.js";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function dt(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Oe={},dr=[],ft=()=>{},Lr=()=>!1,Zn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ks=e=>e.startsWith("onUpdate:"),Pe=Object.assign,$s=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Rv=Object.prototype.hasOwnProperty,$e=(e,t)=>Rv.call(e,t),ve=Array.isArray,pr=e=>wr(e)==="[object Map]",er=e=>wr(e)==="[object Set]",Ya=e=>wr(e)==="[object Date]",oh=e=>wr(e)==="[object RegExp]",_e=e=>typeof e=="function",xe=e=>typeof e=="string",kt=e=>typeof e=="symbol",Le=e=>e!==null&&typeof e=="object",Ns=e=>(Le(e)||_e(e))&&_e(e.then)&&_e(e.catch),Al=Object.prototype.toString,wr=e=>Al.call(e),sh=e=>wr(e).slice(8,-1),no=e=>wr(e)==="[object Object]",Ms=e=>xe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,wn=dt(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ah=dt("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),Rs=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Lv=/-(\w)/g,He=Rs(e=>e.replace(Lv,(t,n)=>n?n.toUpperCase():"")),Dv=/\B([A-Z])/g,xt=Rs(e=>e.replace(Dv,"-$1").toLowerCase()),$n=Rs(e=>e.charAt(0).toUpperCase()+e.slice(1)),Xn=Rs(e=>e?`on${$n(e)}`:""),bt=(e,t)=>!Object.is(e,t),hr=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Il=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Li=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Di=e=>{const t=xe(e)?Number(e):NaN;return isNaN(t)?e:t};let Yu;const ro=()=>Yu||(Yu=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),Fv=/^[_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*$/;function jv(e){return Fv.test(e)?`__props.${e}`:`__props[${JSON.stringify(e)}]`}function Bv(e,t){return e+JSON.stringify(t,(n,r)=>typeof r=="function"?r.toString():r)}const Vv={TEXT:1,1:"TEXT",CLASS:2,2:"CLASS",STYLE:4,4:"STYLE",PROPS:8,8:"PROPS",FULL_PROPS:16,16:"FULL_PROPS",NEED_HYDRATION:32,32:"NEED_HYDRATION",STABLE_FRAGMENT:64,64:"STABLE_FRAGMENT",KEYED_FRAGMENT:128,128:"KEYED_FRAGMENT",UNKEYED_FRAGMENT:256,256:"UNKEYED_FRAGMENT",NEED_PATCH:512,512:"NEED_PATCH",DYNAMIC_SLOTS:1024,1024:"DYNAMIC_SLOTS",DEV_ROOT_FRAGMENT:2048,2048:"DEV_ROOT_FRAGMENT",CACHED:-1,"-1":"CACHED",BAIL:-2,"-2":"BAIL"},Hv={1:"TEXT",2:"CLASS",4:"STYLE",8:"PROPS",16:"FULL_PROPS",32:"NEED_HYDRATION",64:"STABLE_FRAGMENT",128:"KEYED_FRAGMENT",256:"UNKEYED_FRAGMENT",512:"NEED_PATCH",1024:"DYNAMIC_SLOTS",2048:"DEV_ROOT_FRAGMENT",[-1]:"HOISTED",[-2]:"BAIL"},Gv={ELEMENT:1,1:"ELEMENT",FUNCTIONAL_COMPONENT:2,2:"FUNCTIONAL_COMPONENT",STATEFUL_COMPONENT:4,4:"STATEFUL_COMPONENT",TEXT_CHILDREN:8,8:"TEXT_CHILDREN",ARRAY_CHILDREN:16,16:"ARRAY_CHILDREN",SLOTS_CHILDREN:32,32:"SLOTS_CHILDREN",TELEPORT:64,64:"TELEPORT",SUSPENSE:128,128:"SUSPENSE",COMPONENT_SHOULD_KEEP_ALIVE:256,256:"COMPONENT_SHOULD_KEEP_ALIVE",COMPONENT_KEPT_ALIVE:512,512:"COMPONENT_KEPT_ALIVE",COMPONENT:6,6:"COMPONENT"},Uv={STABLE:1,1:"STABLE",DYNAMIC:2,2:"DYNAMIC",FORWARDED:3,3:"FORWARDED"},Wv={1:"STABLE",2:"DYNAMIC",3:"FORWARDED"},Xv="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol",kl=dt(Xv),Kv=kl,Zu=2;function lh(e,t=0,n=e.length){if(t=Math.max(0,Math.min(t,e.length)),n=Math.max(0,Math.min(n,e.length)),t>n)return"";let r=e.split(/(\r?\n)/);const i=r.filter((a,l)=>l%2===1);r=r.filter((a,l)=>l%2===0);let o=0;const s=[];for(let a=0;a<r.length;a++)if(o+=r[a].length+(i[a]&&i[a].length||0),o>=t){for(let l=a-Zu;l<=a+Zu||n>o;l++){if(l<0||l>=r.length)continue;const u=l+1;s.push(`${u}${" ".repeat(Math.max(3-String(u).length,0))}|  ${r[l]}`);const c=r[l].length,f=i[l]&&i[l].length||0;if(l===a){const p=t-(o-(c+f)),d=Math.max(1,n>o?c-p:n-t);s.push("   |  "+" ".repeat(p)+"^".repeat(d))}else if(l>a){if(n>o){const p=Math.max(Math.min(n-o,c),1);s.push("   |  "+"^".repeat(p))}o+=c+f}}break}return s.join(`
`)}function Ve(e){if(ve(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],i=xe(r)?$l(r):Ve(r);if(i)for(const o in i)t[o]=i[o]}return t}else if(xe(e)||Le(e))return e}const qv=/;(?![^(]*\))/g,zv=/:([^]+)/,Jv=/\/\*[^]*?\*\//g;function $l(e){const t={};return e.replace(Jv,"").split(qv).forEach(n=>{if(n){const r=n.split(zv);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Qv(e){if(!e)return"";if(xe(e))return e;let t="";for(const n in e){const r=e[n];if(xe(r)||typeof r=="number"){const i=n.startsWith("--")?n:xt(n);t+=`${i}:${r};`}}return t}function tr(e){let t="";if(xe(e))t=e;else if(ve(e))for(let n=0;n<e.length;n++){const r=tr(e[n]);r&&(t+=r+" ")}else if(Le(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function Nl(e){if(!e)return null;let{class:t,style:n}=e;return t&&!xe(t)&&(e.class=tr(t)),n&&(e.style=Ve(n)),e}const Yv="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",Zv="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",ey="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",ty="area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr",uh=dt(Yv),ch=dt(Zv),fh=dt(ey),dh=dt(ty),ph="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",hh=dt(ph),ny=dt(ph+",async,autofocus,autoplay,controls,default,defer,disabled,hidden,inert,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected");function Ml(e){return!!e||e===""}const ry=/[>/="'\u0009\u000a\u000c\u0020]/,Ia={};function iy(e){if(Ia.hasOwnProperty(e))return Ia[e];const t=ry.test(e);return t&&console.error(`unsafe attribute name: ${e}`),Ia[e]=!t}const oy={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv"},sy=dt("accept,accept-charset,accesskey,action,align,allow,alt,async,autocapitalize,autocomplete,autofocus,autoplay,background,bgcolor,border,buffered,capture,challenge,charset,checked,cite,class,code,codebase,color,cols,colspan,content,contenteditable,contextmenu,controls,coords,crossorigin,csp,data,datetime,decoding,default,defer,dir,dirname,disabled,download,draggable,dropzone,enctype,enterkeyhint,for,form,formaction,formenctype,formmethod,formnovalidate,formtarget,headers,height,hidden,high,href,hreflang,http-equiv,icon,id,importance,inert,integrity,ismap,itemprop,keytype,kind,label,lang,language,loading,list,loop,low,manifest,max,maxlength,minlength,media,min,multiple,muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,preload,radiogroup,readonly,referrerpolicy,rel,required,reversed,rows,rowspan,sandbox,scope,scoped,selected,shape,size,sizes,slot,span,spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,target,title,translate,type,usemap,value,width,wrap"),ay=dt("xmlns,accent-height,accumulate,additive,alignment-baseline,alphabetic,amplitude,arabic-form,ascent,attributeName,attributeType,azimuth,baseFrequency,baseline-shift,baseProfile,bbox,begin,bias,by,calcMode,cap-height,class,clip,clipPathUnits,clip-path,clip-rule,color,color-interpolation,color-interpolation-filters,color-profile,color-rendering,contentScriptType,contentStyleType,crossorigin,cursor,cx,cy,d,decelerate,descent,diffuseConstant,direction,display,divisor,dominant-baseline,dur,dx,dy,edgeMode,elevation,enable-background,end,exponent,fill,fill-opacity,fill-rule,filter,filterRes,filterUnits,flood-color,flood-opacity,font-family,font-size,font-size-adjust,font-stretch,font-style,font-variant,font-weight,format,from,fr,fx,fy,g1,g2,glyph-name,glyph-orientation-horizontal,glyph-orientation-vertical,glyphRef,gradientTransform,gradientUnits,hanging,height,href,hreflang,horiz-adv-x,horiz-origin-x,id,ideographic,image-rendering,in,in2,intercept,k,k1,k2,k3,k4,kernelMatrix,kernelUnitLength,kerning,keyPoints,keySplines,keyTimes,lang,lengthAdjust,letter-spacing,lighting-color,limitingConeAngle,local,marker-end,marker-mid,marker-start,markerHeight,markerUnits,markerWidth,mask,maskContentUnits,maskUnits,mathematical,max,media,method,min,mode,name,numOctaves,offset,opacity,operator,order,orient,orientation,origin,overflow,overline-position,overline-thickness,panose-1,paint-order,path,pathLength,patternContentUnits,patternTransform,patternUnits,ping,pointer-events,points,pointsAtX,pointsAtY,pointsAtZ,preserveAlpha,preserveAspectRatio,primitiveUnits,r,radius,referrerPolicy,refX,refY,rel,rendering-intent,repeatCount,repeatDur,requiredExtensions,requiredFeatures,restart,result,rotate,rx,ry,scale,seed,shape-rendering,slope,spacing,specularConstant,specularExponent,speed,spreadMethod,startOffset,stdDeviation,stemh,stemv,stitchTiles,stop-color,stop-opacity,strikethrough-position,strikethrough-thickness,string,stroke,stroke-dasharray,stroke-dashoffset,stroke-linecap,stroke-linejoin,stroke-miterlimit,stroke-opacity,stroke-width,style,surfaceScale,systemLanguage,tabindex,tableValues,target,targetX,targetY,text-anchor,text-decoration,text-rendering,textLength,to,transform,transform-origin,type,u1,u2,underline-position,underline-thickness,unicode,unicode-bidi,unicode-range,units-per-em,v-alphabetic,v-hanging,v-ideographic,v-mathematical,values,vector-effect,version,vert-adv-y,vert-origin-x,vert-origin-y,viewBox,viewTarget,visibility,width,widths,word-spacing,writing-mode,x,x-height,x1,x2,xChannelSelector,xlink:actuate,xlink:arcrole,xlink:href,xlink:role,xlink:show,xlink:title,xlink:type,xmlns:xlink,xml:base,xml:lang,xml:space,y,y1,y2,yChannelSelector,z,zoomAndPan"),ly=dt("accent,accentunder,actiontype,align,alignmentscope,altimg,altimg-height,altimg-valign,altimg-width,alttext,bevelled,close,columnsalign,columnlines,columnspan,denomalign,depth,dir,display,displaystyle,encoding,equalcolumns,equalrows,fence,fontstyle,fontweight,form,frame,framespacing,groupalign,height,href,id,indentalign,indentalignfirst,indentalignlast,indentshift,indentshiftfirst,indentshiftlast,indextype,justify,largetop,largeop,lquote,lspace,mathbackground,mathcolor,mathsize,mathvariant,maxsize,minlabelspacing,mode,other,overflow,position,rowalign,rowlines,rowspan,rquote,rspace,scriptlevel,scriptminsize,scriptsizemultiplier,selection,separator,separators,shift,side,src,stackalign,stretchy,subscriptshift,superscriptshift,symmetric,voffset,width,widths,xlink:href,xlink:show,xlink:type,xmlns");function uy(e){if(e==null)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"}const cy=/["'&<>]/;function fy(e){const t=""+e,n=cy.exec(t);if(!n)return t;let r="",i,o,s=0;for(o=n.index;o<t.length;o++){switch(t.charCodeAt(o)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 39:i="&#39;";break;case 60:i="&lt;";break;case 62:i="&gt;";break;default:continue}s!==o&&(r+=t.slice(s,o)),s=o+1,r+=i}return s!==o?r+t.slice(s,o):r}const dy=/^-?>|<!--|-->|--!>|<!-$/g;function py(e){return e.replace(dy,"")}const gh=/[ !"#$%&'()*+,./:;<=>?@[\\\]^`{|}~]/g;function hy(e,t){return e.replace(gh,n=>t?n==='"'?'\\\\\\"':`\\\\${n}`:`\\${n}`)}function gy(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=In(e[r],t[r]);return n}function In(e,t){if(e===t)return!0;let n=Ya(e),r=Ya(t);if(n||r)return n&&r?e.getTime()===t.getTime():!1;if(n=kt(e),r=kt(t),n||r)return e===t;if(n=ve(e),r=ve(t),n||r)return n&&r?gy(e,t):!1;if(n=Le(e),r=Le(t),n||r){if(!n||!r)return!1;const i=Object.keys(e).length,o=Object.keys(t).length;if(i!==o)return!1;for(const s in e){const a=e.hasOwnProperty(s),l=t.hasOwnProperty(s);if(a&&!l||!a&&l||!In(e[s],t[s]))return!1}}return String(e)===String(t)}function io(e,t){return e.findIndex(n=>In(n,t))}const mh=e=>!!(e&&e.__v_isRef===!0),ct=e=>xe(e)?e:e==null?"":ve(e)||Le(e)&&(e.toString===Al||!_e(e.toString))?mh(e)?ct(e.value):JSON.stringify(e,vh,2):String(e),vh=(e,t)=>mh(t)?vh(e,t.value):pr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,i],o)=>(n[ka(r,o)+" =>"]=i,n),{})}:er(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ka(n))}:kt(t)?ka(t):Le(t)&&!ve(t)&&!no(t)?String(t):t,ka=(e,t="")=>{var n;return kt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e},my=Object.freeze(Object.defineProperty({__proto__:null,EMPTY_ARR:dr,EMPTY_OBJ:Oe,NO:Lr,NOOP:ft,PatchFlagNames:Hv,PatchFlags:Vv,ShapeFlags:Gv,SlotFlags:Uv,camelize:He,capitalize:$n,cssVarNameEscapeSymbolsRE:gh,def:Il,escapeHtml:fy,escapeHtmlComment:py,extend:Pe,genCacheKey:Bv,genPropsAccessExp:jv,generateCodeFrame:lh,getEscapedCssVarName:hy,getGlobalThis:ro,hasChanged:bt,hasOwn:$e,hyphenate:xt,includeBooleanAttr:Ml,invokeArrayFns:hr,isArray:ve,isBooleanAttr:ny,isBuiltInDirective:ah,isDate:Ya,isFunction:_e,isGloballyAllowed:kl,isGloballyWhitelisted:Kv,isHTMLTag:uh,isIntegerKey:Ms,isKnownHtmlAttr:sy,isKnownMathMLAttr:ly,isKnownSvgAttr:ay,isMap:pr,isMathMLTag:fh,isModelListener:ks,isObject:Le,isOn:Zn,isPlainObject:no,isPromise:Ns,isRegExp:oh,isRenderableAttrValue:uy,isReservedProp:wn,isSSRSafeAttrName:iy,isSVGTag:ch,isSet:er,isSpecialBooleanAttr:hh,isString:xe,isSymbol:kt,isVoidTag:dh,looseEqual:In,looseIndexOf:io,looseToNumber:Li,makeMap:dt,normalizeClass:tr,normalizeProps:Nl,normalizeStyle:Ve,objectToString:Al,parseStringStyle:$l,propsToAttrMap:oy,remove:$s,slotFlagsText:Wv,stringifyStyle:Qv,toDisplayString:ct,toHandlerKey:Xn,toNumber:Di,toRawType:sh,toTypeString:wr},Symbol.toStringTag,{value:"Module"}));/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ot;class Ls{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ot,!t&&Ot&&(this.index=(Ot.scopes||(Ot.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ot;try{return Ot=this,t()}finally{Ot=n}}}on(){Ot=this}off(){Ot=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function yh(e){return new Ls(e)}function oo(){return Ot}function Ds(e,t=!1){Ot&&Ot.cleanups.push(e)}let Ge;const $a=new WeakSet;class Gr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ot&&Ot.active&&Ot.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,$a.has(this)&&($a.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||_h(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ec(this),Ch(this);const t=Ge,n=tn;Ge=this,tn=!0;try{return this.fn()}finally{xh(this),Ge=t,tn=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Dl(t);this.deps=this.depsTail=void 0,ec(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?$a.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Za(this)&&this.run()}get dirty(){return Za(this)}}let bh=0,Oi,Pi;function _h(e,t=!1){if(e.flags|=8,t){e.next=Pi,Pi=e;return}e.next=Oi,Oi=e}function Rl(){bh++}function Ll(){if(--bh>0)return;if(Pi){let t=Pi;for(Pi=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Oi;){let t=Oi;for(Oi=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Ch(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function xh(e){let t,n=e.depsTail,r=n;for(;r;){const i=r.prevDep;r.version===-1?(r===n&&(n=i),Dl(r),vy(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=i}e.deps=t,e.depsTail=n}function Za(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Eh(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Eh(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Fi))return;e.globalVersion=Fi;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Za(e)){e.flags&=-3;return}const n=Ge,r=tn;Ge=e,tn=!0;try{Ch(e);const i=e.fn(e._value);(t.version===0||bt(i,e._value))&&(e._value=i,t.version++)}catch(i){throw t.version++,i}finally{Ge=n,tn=r,xh(e),e.flags&=-3}}function Dl(e,t=!1){const{dep:n,prevSub:r,nextSub:i}=e;if(r&&(r.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Dl(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function vy(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}function Sh(e,t){e.effect instanceof Gr&&(e=e.effect.fn);const n=new Gr(e);t&&Pe(n,t);try{n.run()}catch(i){throw n.stop(),i}const r=n.run.bind(n);return r.effect=n,r}function wh(e){e.effect.stop()}let tn=!0;const Th=[];function nr(){Th.push(tn),tn=!1}function rr(){const e=Th.pop();tn=e===void 0?!0:e}function ec(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Ge;Ge=void 0;try{t()}finally{Ge=n}}}let Fi=0;class yy{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Fs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Ge||!tn||Ge===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Ge)n=this.activeLink=new yy(Ge,this),Ge.deps?(n.prevDep=Ge.depsTail,Ge.depsTail.nextDep=n,Ge.depsTail=n):Ge.deps=Ge.depsTail=n,Oh(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=Ge.depsTail,n.nextDep=void 0,Ge.depsTail.nextDep=n,Ge.depsTail=n,Ge.deps===n&&(Ge.deps=r)}return n}trigger(t){this.version++,Fi++,this.notify(t)}notify(t){Rl();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ll()}}}function Oh(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Oh(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const as=new WeakMap,gr=Symbol(""),el=Symbol(""),ji=Symbol("");function _t(e,t,n){if(tn&&Ge){let r=as.get(e);r||as.set(e,r=new Map);let i=r.get(n);i||(r.set(n,i=new Fs),i.map=r,i.key=n),i.track()}}function Cn(e,t,n,r,i,o){const s=as.get(e);if(!s){Fi++;return}const a=l=>{l&&l.trigger()};if(Rl(),t==="clear")s.forEach(a);else{const l=ve(e),u=l&&Ms(n);if(l&&n==="length"){const c=Number(r);s.forEach((f,p)=>{(p==="length"||p===ji||!kt(p)&&p>=c)&&a(f)})}else switch((n!==void 0||s.has(void 0))&&a(s.get(n)),u&&a(s.get(ji)),t){case"add":l?u&&a(s.get("length")):(a(s.get(gr)),pr(e)&&a(s.get(el)));break;case"delete":l||(a(s.get(gr)),pr(e)&&a(s.get(el)));break;case"set":pr(e)&&a(s.get(gr));break}}Ll()}function by(e,t){const n=as.get(e);return n&&n.get(t)}function Pr(e){const t=Ae(e);return t===e?t:(_t(t,"iterate",ji),jt(e)?t:t.map(Ct))}function js(e){return _t(e=Ae(e),"iterate",ji),e}const _y={__proto__:null,[Symbol.iterator](){return Na(this,Symbol.iterator,Ct)},concat(...e){return Pr(this).concat(...e.map(t=>ve(t)?Pr(t):t))},entries(){return Na(this,"entries",e=>(e[1]=Ct(e[1]),e))},every(e,t){return mn(this,"every",e,t,void 0,arguments)},filter(e,t){return mn(this,"filter",e,t,n=>n.map(Ct),arguments)},find(e,t){return mn(this,"find",e,t,Ct,arguments)},findIndex(e,t){return mn(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return mn(this,"findLast",e,t,Ct,arguments)},findLastIndex(e,t){return mn(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return mn(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ma(this,"includes",e)},indexOf(...e){return Ma(this,"indexOf",e)},join(e){return Pr(this).join(e)},lastIndexOf(...e){return Ma(this,"lastIndexOf",e)},map(e,t){return mn(this,"map",e,t,void 0,arguments)},pop(){return ci(this,"pop")},push(...e){return ci(this,"push",e)},reduce(e,...t){return tc(this,"reduce",e,t)},reduceRight(e,...t){return tc(this,"reduceRight",e,t)},shift(){return ci(this,"shift")},some(e,t){return mn(this,"some",e,t,void 0,arguments)},splice(...e){return ci(this,"splice",e)},toReversed(){return Pr(this).toReversed()},toSorted(e){return Pr(this).toSorted(e)},toSpliced(...e){return Pr(this).toSpliced(...e)},unshift(...e){return ci(this,"unshift",e)},values(){return Na(this,"values",Ct)}};function Na(e,t,n){const r=js(e),i=r[t]();return r!==e&&!jt(e)&&(i._next=i.next,i.next=()=>{const o=i._next();return o.value&&(o.value=n(o.value)),o}),i}const Cy=Array.prototype;function mn(e,t,n,r,i,o){const s=js(e),a=s!==e&&!jt(e),l=s[t];if(l!==Cy[t]){const f=l.apply(e,o);return a?Ct(f):f}let u=n;s!==e&&(a?u=function(f,p){return n.call(this,Ct(f),p,e)}:n.length>2&&(u=function(f,p){return n.call(this,f,p,e)}));const c=l.call(s,u,r);return a&&i?i(c):c}function tc(e,t,n,r){const i=js(e);let o=n;return i!==e&&(jt(e)?n.length>3&&(o=function(s,a,l){return n.call(this,s,a,l,e)}):o=function(s,a,l){return n.call(this,s,Ct(a),l,e)}),i[t](o,...r)}function Ma(e,t,n){const r=Ae(e);_t(r,"iterate",ji);const i=r[t](...n);return(i===-1||i===!1)&&so(n[0])?(n[0]=Ae(n[0]),r[t](...n)):i}function ci(e,t,n=[]){nr(),Rl();const r=Ae(e)[t].apply(e,n);return Ll(),rr(),r}const xy=dt("__proto__,__v_isRef,__isVue"),Ph=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(kt));function Ey(e){kt(e)||(e=String(e));const t=Ae(this);return _t(t,"has",e),t.hasOwnProperty(e)}class Ah{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(i?o?Rh:Mh:o?Nh:$h).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const s=ve(t);if(!i){let l;if(s&&(l=_y[n]))return l;if(n==="hasOwnProperty")return Ey}const a=Reflect.get(t,n,rt(t)?t:r);return(kt(n)?Ph.has(n):xy(n))||(i||_t(t,"get",n),o)?a:rt(a)?s&&Ms(n)?a:a.value:Le(a)?i?ii(a):ke(a):a}}class Ih extends Ah{constructor(t=!1){super(!1,t)}set(t,n,r,i){let o=t[n];if(!this._isShallow){const l=kn(o);if(!jt(r)&&!kn(r)&&(o=Ae(o),r=Ae(r)),!ve(t)&&rt(o)&&!rt(r))return l?!1:(o.value=r,!0)}const s=ve(t)&&Ms(n)?Number(n)<t.length:$e(t,n),a=Reflect.set(t,n,r,rt(t)?t:i);return t===Ae(i)&&(s?bt(r,o)&&Cn(t,"set",n,r):Cn(t,"add",n,r)),a}deleteProperty(t,n){const r=$e(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&r&&Cn(t,"delete",n,void 0),i}has(t,n){const r=Reflect.has(t,n);return(!kt(n)||!Ph.has(n))&&_t(t,"has",n),r}ownKeys(t){return _t(t,"iterate",ve(t)?"length":gr),Reflect.ownKeys(t)}}class kh extends Ah{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Sy=new Ih,wy=new kh,Ty=new Ih(!0),Oy=new kh(!0),tl=e=>e,To=e=>Reflect.getPrototypeOf(e);function Py(e,t,n){return function(...r){const i=this.__v_raw,o=Ae(i),s=pr(o),a=e==="entries"||e===Symbol.iterator&&s,l=e==="keys"&&s,u=i[e](...r),c=n?tl:t?nl:Ct;return!t&&_t(o,"iterate",l?el:gr),{next(){const{value:f,done:p}=u.next();return p?{value:f,done:p}:{value:a?[c(f[0]),c(f[1])]:c(f),done:p}},[Symbol.iterator](){return this}}}}function Oo(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Ay(e,t){const n={get(i){const o=this.__v_raw,s=Ae(o),a=Ae(i);e||(bt(i,a)&&_t(s,"get",i),_t(s,"get",a));const{has:l}=To(s),u=t?tl:e?nl:Ct;if(l.call(s,i))return u(o.get(i));if(l.call(s,a))return u(o.get(a));o!==s&&o.get(i)},get size(){const i=this.__v_raw;return!e&&_t(Ae(i),"iterate",gr),Reflect.get(i,"size",i)},has(i){const o=this.__v_raw,s=Ae(o),a=Ae(i);return e||(bt(i,a)&&_t(s,"has",i),_t(s,"has",a)),i===a?o.has(i):o.has(i)||o.has(a)},forEach(i,o){const s=this,a=s.__v_raw,l=Ae(a),u=t?tl:e?nl:Ct;return!e&&_t(l,"iterate",gr),a.forEach((c,f)=>i.call(o,u(c),u(f),s))}};return Pe(n,e?{add:Oo("add"),set:Oo("set"),delete:Oo("delete"),clear:Oo("clear")}:{add(i){!t&&!jt(i)&&!kn(i)&&(i=Ae(i));const o=Ae(this);return To(o).has.call(o,i)||(o.add(i),Cn(o,"add",i,i)),this},set(i,o){!t&&!jt(o)&&!kn(o)&&(o=Ae(o));const s=Ae(this),{has:a,get:l}=To(s);let u=a.call(s,i);u||(i=Ae(i),u=a.call(s,i));const c=l.call(s,i);return s.set(i,o),u?bt(o,c)&&Cn(s,"set",i,o):Cn(s,"add",i,o),this},delete(i){const o=Ae(this),{has:s,get:a}=To(o);let l=s.call(o,i);l||(i=Ae(i),l=s.call(o,i)),a&&a.call(o,i);const u=o.delete(i);return l&&Cn(o,"delete",i,void 0),u},clear(){const i=Ae(this),o=i.size!==0,s=i.clear();return o&&Cn(i,"clear",void 0,void 0),s}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=Py(i,e,t)}),n}function Bs(e,t){const n=Ay(e,t);return(r,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?r:Reflect.get($e(n,i)&&i in r?n:r,i,o)}const Iy={get:Bs(!1,!1)},ky={get:Bs(!1,!0)},$y={get:Bs(!0,!1)},Ny={get:Bs(!0,!0)},$h=new WeakMap,Nh=new WeakMap,Mh=new WeakMap,Rh=new WeakMap;function My(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ry(e){return e.__v_skip||!Object.isExtensible(e)?0:My(sh(e))}function ke(e){return kn(e)?e:Hs(e,!1,Sy,Iy,$h)}function Vs(e){return Hs(e,!1,Ty,ky,Nh)}function ii(e){return Hs(e,!0,wy,$y,Mh)}function Lh(e){return Hs(e,!0,Oy,Ny,Rh)}function Hs(e,t,n,r,i){if(!Le(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=i.get(e);if(o)return o;const s=Ry(e);if(s===0)return e;const a=new Proxy(e,s===2?r:n);return i.set(e,a),a}function Tn(e){return kn(e)?Tn(e.__v_raw):!!(e&&e.__v_isReactive)}function kn(e){return!!(e&&e.__v_isReadonly)}function jt(e){return!!(e&&e.__v_isShallow)}function so(e){return e?!!e.__v_raw:!1}function Ae(e){const t=e&&e.__v_raw;return t?Ae(t):e}function Gs(e){return!$e(e,"__v_skip")&&Object.isExtensible(e)&&Il(e,"__v_skip",!0),e}const Ct=e=>Le(e)?ke(e):e,nl=e=>Le(e)?ii(e):e;function rt(e){return e?e.__v_isRef===!0:!1}function ye(e){return Dh(e,!1)}function ao(e){return Dh(e,!0)}function Dh(e,t){return rt(e)?e:new Ly(e,t)}class Ly{constructor(t,n){this.dep=new Fs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Ae(t),this._value=n?t:Ct(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||jt(t)||kn(t);t=r?t:Ae(t),bt(t,n)&&(this._rawValue=t,this._value=r?t:Ct(t),this.dep.trigger())}}function Fh(e){e.dep&&e.dep.trigger()}function v(e){return rt(e)?e.value:e}function jh(e){return _e(e)?e():v(e)}const Dy={get:(e,t,n)=>t==="__v_raw"?e:v(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const i=e[t];return rt(i)&&!rt(n)?(i.value=n,!0):Reflect.set(e,t,n,r)}};function Us(e){return Tn(e)?e:new Proxy(e,Dy)}class Fy{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new Fs,{get:r,set:i}=t(n.track.bind(n),n.trigger.bind(n));this._get=r,this._set=i}get value(){return this._value=this._get()}set value(t){this._set(t)}}function Ws(e){return new Fy(e)}function Bh(e){const t=ve(e)?new Array(e.length):{};for(const n in e)t[n]=Vh(e,n);return t}class jy{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return by(Ae(this._object),this._key)}}class By{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function $t(e,t,n){return rt(e)?e:_e(e)?new By(e):Le(e)&&arguments.length>1?Vh(e,t,n):ye(e)}function Vh(e,t,n){const r=e[t];return rt(r)?r:new jy(e,t,n)}class Vy{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Fs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Fi-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&Ge!==this)return _h(this,!0),!0}get value(){const t=this.dep.track();return Eh(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Hy(e,t,n=!1){let r,i;return _e(e)?r=e:(r=e.get,i=e.set),new Vy(r,i,n)}const Hh={GET:"get",HAS:"has",ITERATE:"iterate"},Gh={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},Po={},ls=new WeakMap;let jn;function Uh(){return jn}function Fl(e,t=!1,n=jn){if(n){let r=ls.get(n);r||ls.set(n,r=[]),r.push(e)}}function Gy(e,t,n=Oe){const{immediate:r,deep:i,once:o,scheduler:s,augmentJob:a,call:l}=n,u=C=>i?C:jt(C)||i===!1||i===0?xn(C,1):xn(C);let c,f,p,d,h=!1,x=!1;if(rt(e)?(f=()=>e.value,h=jt(e)):Tn(e)?(f=()=>u(e),h=!0):ve(e)?(x=!0,h=e.some(C=>Tn(C)||jt(C)),f=()=>e.map(C=>{if(rt(C))return C.value;if(Tn(C))return u(C);if(_e(C))return l?l(C,2):C()})):_e(e)?t?f=l?()=>l(e,2):e:f=()=>{if(p){nr();try{p()}finally{rr()}}const C=jn;jn=c;try{return l?l(e,3,[d]):e(d)}finally{jn=C}}:f=ft,t&&i){const C=f,b=i===!0?1/0:i;f=()=>xn(C(),b)}const j=oo(),L=()=>{c.stop(),j&&j.active&&$s(j.effects,c)};if(o&&t){const C=t;t=(...b)=>{C(...b),L()}}let A=x?new Array(e.length).fill(Po):Po;const g=C=>{if(!(!(c.flags&1)||!c.dirty&&!C))if(t){const b=c.run();if(i||h||(x?b.some((R,T)=>bt(R,A[T])):bt(b,A))){p&&p();const R=jn;jn=c;try{const T=[b,A===Po?void 0:x&&A[0]===Po?[]:A,d];l?l(t,3,T):t(...T),A=b}finally{jn=R}}}else c.run()};return a&&a(g),c=new Gr(f),c.scheduler=s?()=>s(g,!1):g,d=C=>Fl(C,!1,c),p=c.onStop=()=>{const C=ls.get(c);if(C){if(l)l(C,4);else for(const b of C)b();ls.delete(c)}},t?r?g(!0):A=c.run():s?s(g.bind(null,!0),!0):c.run(),L.pause=c.pause.bind(c),L.resume=c.resume.bind(c),L.stop=L,L}function xn(e,t=1/0,n){if(t<=0||!Le(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,rt(e))xn(e.value,t,n);else if(ve(e))for(let r=0;r<e.length;r++)xn(e[r],t,n);else if(er(e)||pr(e))e.forEach(r=>{xn(r,t,n)});else if(no(e)){for(const r in e)xn(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&xn(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Wh=[];function Uy(e){Wh.push(e)}function Wy(){Wh.pop()}function Xh(e,t){}const Kh={SETUP_FUNCTION:0,0:"SETUP_FUNCTION",RENDER_FUNCTION:1,1:"RENDER_FUNCTION",NATIVE_EVENT_HANDLER:5,5:"NATIVE_EVENT_HANDLER",COMPONENT_EVENT_HANDLER:6,6:"COMPONENT_EVENT_HANDLER",VNODE_HOOK:7,7:"VNODE_HOOK",DIRECTIVE_HOOK:8,8:"DIRECTIVE_HOOK",TRANSITION_HOOK:9,9:"TRANSITION_HOOK",APP_ERROR_HANDLER:10,10:"APP_ERROR_HANDLER",APP_WARN_HANDLER:11,11:"APP_WARN_HANDLER",FUNCTION_REF:12,12:"FUNCTION_REF",ASYNC_COMPONENT_LOADER:13,13:"ASYNC_COMPONENT_LOADER",SCHEDULER:14,14:"SCHEDULER",COMPONENT_UPDATE:15,15:"COMPONENT_UPDATE",APP_UNMOUNT_CLEANUP:16,16:"APP_UNMOUNT_CLEANUP"},Xy={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function Tr(e,t,n,r){try{return r?e(...r):e()}catch(i){ir(i,t,n)}}function Ut(e,t,n,r){if(_e(e)){const i=Tr(e,t,n,r);return i&&Ns(i)&&i.catch(o=>{ir(o,t,n)}),i}if(ve(e)){const i=[];for(let o=0;o<e.length;o++)i.push(Ut(e[o],t,n,r));return i}}function ir(e,t,n,r=!0){const i=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:s}=t&&t.appContext.config||Oe;if(t){let a=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,l,u)===!1)return}a=a.parent}if(o){nr(),Tr(o,null,10,[e,l,u]),rr();return}}Ky(e,n,i,r,s)}function Ky(e,t,n,r=!0,i=!1){if(i)throw e;console.error(e)}const Pt=[];let un=-1;const Fr=[];let Bn=null,Nr=0;const qh=Promise.resolve();let us=null;function on(e){const t=us||qh;return e?t.then(this?e.bind(this):e):t}function qy(e){let t=un+1,n=Pt.length;for(;t<n;){const r=t+n>>>1,i=Pt[r],o=Bi(i);o<e||o===e&&i.flags&2?t=r+1:n=r}return t}function jl(e){if(!(e.flags&1)){const t=Bi(e),n=Pt[Pt.length-1];!n||!(e.flags&2)&&t>=Bi(n)?Pt.push(e):Pt.splice(qy(t),0,e),e.flags|=1,zh()}}function zh(){us||(us=qh.then(Jh))}function Ur(e){ve(e)?Fr.push(...e):Bn&&e.id===-1?Bn.splice(Nr+1,0,e):e.flags&1||(Fr.push(e),e.flags|=1),zh()}function nc(e,t,n=un+1){for(;n<Pt.length;n++){const r=Pt[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Pt.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function cs(e){if(Fr.length){const t=[...new Set(Fr)].sort((n,r)=>Bi(n)-Bi(r));if(Fr.length=0,Bn){Bn.push(...t);return}for(Bn=t,Nr=0;Nr<Bn.length;Nr++){const n=Bn[Nr];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Bn=null,Nr=0}}const Bi=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Jh(e){try{for(un=0;un<Pt.length;un++){const t=Pt[un];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Tr(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;un<Pt.length;un++){const t=Pt[un];t&&(t.flags&=-2)}un=-1,Pt.length=0,cs(),us=null,(Pt.length||Fr.length)&&Jh()}}let Mr,Ao=[];function Qh(e,t){var n,r;Mr=e,Mr?(Mr.enabled=!0,Ao.forEach(({event:i,args:o})=>Mr.emit(i,...o)),Ao=[]):typeof window<"u"&&window.HTMLElement&&!((r=(n=window.navigator)==null?void 0:n.userAgent)!=null&&r.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(o=>{Qh(o,t)}),setTimeout(()=>{Mr||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,Ao=[])},3e3)):Ao=[]}let st=null,Xs=null;function Vi(e){const t=st;return st=e,Xs=e&&e.type.__scopeId||null,t}function Yh(e){Xs=e}function Zh(){Xs=null}const eg=e=>Ks;function Ks(e,t=st,n){if(!t||e._n)return e;const r=(...i)=>{r._d&&hs(-1);const o=Vi(t);let s;try{s=e(...i)}finally{Vi(o),r._d&&hs(1)}return s};return r._n=!0,r._c=!0,r._d=!0,r}function tg(e,t){if(st===null)return e;const n=po(st),r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,s,a,l=Oe]=t[i];o&&(_e(o)&&(o={mounted:o,updated:o}),o.deep&&xn(s),r.push({dir:o,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function cn(e,t,n,r){const i=e.dirs,o=t&&t.dirs;for(let s=0;s<i.length;s++){const a=i[s];o&&(a.oldValue=o[s].value);let l=a.dir[r];l&&(nr(),Ut(l,n,8,[e.el,a,e,t]),rr())}}const ng=Symbol("_vte"),rg=e=>e.__isTeleport,Ai=e=>e&&(e.disabled||e.disabled===""),rc=e=>e&&(e.defer||e.defer===""),ic=e=>typeof SVGElement<"u"&&e instanceof SVGElement,oc=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,rl=(e,t)=>{const n=e&&e.to;return xe(n)?t?t(n):null:n},ig={name:"Teleport",__isTeleport:!0,process(e,t,n,r,i,o,s,a,l,u){const{mc:c,pc:f,pbc:p,o:{insert:d,querySelector:h,createText:x,createComment:j}}=u,L=Ai(t.props);let{shapeFlag:A,children:g,dynamicChildren:C}=t;if(e==null){const b=t.el=x(""),R=t.anchor=x("");d(b,n,r),d(R,n,r);const T=(y,S)=>{A&16&&(i&&i.isCE&&(i.ce._teleportTarget=y),c(g,y,S,i,o,s,a,l))},O=()=>{const y=t.target=rl(t.props,h),S=sg(y,t,x,d);y&&(s!=="svg"&&ic(y)?s="svg":s!=="mathml"&&oc(y)&&(s="mathml"),L||(T(y,S),Qo(t,!1)))};L&&(T(n,R),Qo(t,!0)),rc(t.props)?it(()=>{O(),t.el.__isMounted=!0},o):O()}else{if(rc(t.props)&&!e.el.__isMounted){it(()=>{ig.process(e,t,n,r,i,o,s,a,l,u),delete e.el.__isMounted},o);return}t.el=e.el,t.targetStart=e.targetStart;const b=t.anchor=e.anchor,R=t.target=e.target,T=t.targetAnchor=e.targetAnchor,O=Ai(e.props),y=O?n:R,S=O?b:T;if(s==="svg"||ic(R)?s="svg":(s==="mathml"||oc(R))&&(s="mathml"),C?(p(e.dynamicChildren,C,y,i,o,s,a),tu(e,t,!0)):l||f(e,t,y,S,i,o,s,a,!1),L)O?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Io(t,n,b,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const H=t.target=rl(t.props,h);H&&Io(t,H,null,u,0)}else O&&Io(t,R,T,u,1);Qo(t,L)}},remove(e,t,n,{um:r,o:{remove:i}},o){const{shapeFlag:s,children:a,anchor:l,targetStart:u,targetAnchor:c,target:f,props:p}=e;if(f&&(i(u),i(c)),o&&i(l),s&16){const d=o||!Ai(p);for(let h=0;h<a.length;h++){const x=a[h];r(x,t,n,d,!!x.dynamicChildren)}}},move:Io,hydrate:zy};function Io(e,t,n,{o:{insert:r},m:i},o=2){o===0&&r(e.targetAnchor,t,n);const{el:s,anchor:a,shapeFlag:l,children:u,props:c}=e,f=o===2;if(f&&r(s,t,n),(!f||Ai(c))&&l&16)for(let p=0;p<u.length;p++)i(u[p],t,n,2);f&&r(a,t,n)}function zy(e,t,n,r,i,o,{o:{nextSibling:s,parentNode:a,querySelector:l,insert:u,createText:c}},f){const p=t.target=rl(t.props,l);if(p){const d=Ai(t.props),h=p._lpa||p.firstChild;if(t.shapeFlag&16)if(d)t.anchor=f(s(e),t,a(e),n,r,i,o),t.targetStart=h,t.targetAnchor=h&&s(h);else{t.anchor=s(e);let x=h;for(;x;){if(x&&x.nodeType===8){if(x.data==="teleport start anchor")t.targetStart=x;else if(x.data==="teleport anchor"){t.targetAnchor=x,p._lpa=t.targetAnchor&&s(t.targetAnchor);break}}x=s(x)}t.targetAnchor||sg(p,t,c,u),f(h&&s(h),t,p,n,r,i,o)}Qo(t,d)}return t.anchor&&s(t.anchor)}const og=ig;function Qo(e,t){const n=e.ctx;if(n&&n.ut){let r,i;for(t?(r=e.el,i=e.anchor):(r=e.targetStart,i=e.targetAnchor);r&&r!==i;)r.nodeType===1&&r.setAttribute("data-v-owner",n.uid),r=r.nextSibling;n.ut()}}function sg(e,t,n,r){const i=t.targetStart=n(""),o=t.targetAnchor=n("");return i[ng]=o,e&&(r(i,e),r(o,e)),o}const Vn=Symbol("_leaveCb"),ko=Symbol("_enterCb");function qs(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ue(()=>{e.isMounted=!0}),oi(()=>{e.isUnmounting=!0}),e}const qt=[Function,Array],zs={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:qt,onEnter:qt,onAfterEnter:qt,onEnterCancelled:qt,onBeforeLeave:qt,onLeave:qt,onAfterLeave:qt,onLeaveCancelled:qt,onBeforeAppear:qt,onAppear:qt,onAfterAppear:qt,onAppearCancelled:qt},ag=e=>{const t=e.subTree;return t.component?ag(t.component):t},Jy={name:"BaseTransition",props:zs,setup(e,{slots:t}){const n=gt(),r=qs();return()=>{const i=t.default&&lo(t.default(),!0);if(!i||!i.length)return;const o=lg(i),s=Ae(e),{mode:a}=s;if(r.isLeaving)return Ra(o);const l=sc(o);if(!l)return Ra(o);let u=yr(l,s,r,n,f=>u=f);l.type!==et&&dn(l,u);let c=n.subTree&&sc(n.subTree);if(c&&c.type!==et&&!en(l,c)&&ag(n).type!==et){let f=yr(c,s,r,n);if(dn(c,f),a==="out-in"&&l.type!==et)return r.isLeaving=!0,f.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,c=void 0},Ra(o);a==="in-out"&&l.type!==et?f.delayLeave=(p,d,h)=>{const x=ug(r,c);x[String(c.key)]=c,p[Vn]=()=>{d(),p[Vn]=void 0,delete u.delayedLeave,c=void 0},u.delayedLeave=()=>{h(),delete u.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return o}}};function lg(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==et){t=n;break}}return t}const Bl=Jy;function ug(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function yr(e,t,n,r,i){const{appear:o,mode:s,persisted:a=!1,onBeforeEnter:l,onEnter:u,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:p,onLeave:d,onAfterLeave:h,onLeaveCancelled:x,onBeforeAppear:j,onAppear:L,onAfterAppear:A,onAppearCancelled:g}=t,C=String(e.key),b=ug(n,e),R=(y,S)=>{y&&Ut(y,r,9,S)},T=(y,S)=>{const H=S[1];R(y,S),ve(y)?y.every(I=>I.length<=1)&&H():y.length<=1&&H()},O={mode:s,persisted:a,beforeEnter(y){let S=l;if(!n.isMounted)if(o)S=j||l;else return;y[Vn]&&y[Vn](!0);const H=b[C];H&&en(e,H)&&H.el[Vn]&&H.el[Vn](),R(S,[y])},enter(y){let S=u,H=c,I=f;if(!n.isMounted)if(o)S=L||u,H=A||c,I=g||f;else return;let M=!1;const z=y[ko]=Z=>{M||(M=!0,Z?R(I,[y]):R(H,[y]),O.delayedLeave&&O.delayedLeave(),y[ko]=void 0)};S?T(S,[y,z]):z()},leave(y,S){const H=String(e.key);if(y[ko]&&y[ko](!0),n.isUnmounting)return S();R(p,[y]);let I=!1;const M=y[Vn]=z=>{I||(I=!0,S(),z?R(x,[y]):R(h,[y]),y[Vn]=void 0,b[H]===e&&delete b[H])};b[H]=e,d?T(d,[y,M]):M()},clone(y){const S=yr(y,t,n,r,i);return i&&i(S),S}};return O}function Ra(e){if(uo(e))return e=nn(e),e.children=null,e}function sc(e){if(!uo(e))return rg(e.type)&&e.children?lg(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&_e(n.default))return n.default()}}function dn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,dn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function lo(e,t=!1,n){let r=[],i=0;for(let o=0;o<e.length;o++){let s=e[o];const a=n==null?s.key:String(n)+String(s.key!=null?s.key:o);s.type===Se?(s.patchFlag&128&&i++,r=r.concat(lo(s.children,t,a))):(t||s.type!==et)&&r.push(a!=null?nn(s,{key:a}):s)}if(i>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Ne(e,t){return _e(e)?Pe({name:e.name},t,{setup:e}):e}function cg(){const e=gt();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function Vl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function fg(e){const t=gt(),n=ao(null);if(t){const i=t.refs===Oe?t.refs={}:t.refs;Object.defineProperty(i,e,{enumerable:!0,get:()=>n.value,set:o=>n.value=o})}return n}function Hi(e,t,n,r,i=!1){if(ve(e)){e.forEach((h,x)=>Hi(h,t&&(ve(t)?t[x]:t),n,r,i));return}if(Kn(r)&&!i){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&Hi(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?po(r.component):r.el,s=i?null:o,{i:a,r:l}=e,u=t&&t.r,c=a.refs===Oe?a.refs={}:a.refs,f=a.setupState,p=Ae(f),d=f===Oe?()=>!1:h=>$e(p,h);if(u!=null&&u!==l&&(xe(u)?(c[u]=null,d(u)&&(f[u]=null)):rt(u)&&(u.value=null)),_e(l))Tr(l,a,12,[s,c]);else{const h=xe(l),x=rt(l);if(h||x){const j=()=>{if(e.f){const L=h?d(l)?f[l]:c[l]:l.value;i?ve(L)&&$s(L,o):ve(L)?L.includes(o)||L.push(o):h?(c[l]=[o],d(l)&&(f[l]=c[l])):(l.value=[o],e.k&&(c[e.k]=l.value))}else h?(c[l]=s,d(l)&&(f[l]=s)):x&&(l.value=s,e.k&&(c[e.k]=s))};s?(j.id=-1,it(j,n)):j()}}}let ac=!1;const Ar=()=>{ac||(console.error("Hydration completed but contains mismatches."),ac=!0)},Qy=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",Yy=e=>e.namespaceURI.includes("MathML"),$o=e=>{if(e.nodeType===1){if(Qy(e))return"svg";if(Yy(e))return"mathml"}},Dr=e=>e.nodeType===8;function Zy(e){const{mt:t,p:n,o:{patchProp:r,createText:i,nextSibling:o,parentNode:s,remove:a,insert:l,createComment:u}}=e,c=(g,C)=>{if(!C.hasChildNodes()){n(null,g,C),cs(),C._vnode=g;return}f(C.firstChild,g,null,null,null),cs(),C._vnode=g},f=(g,C,b,R,T,O=!1)=>{O=O||!!C.dynamicChildren;const y=Dr(g)&&g.data==="[",S=()=>x(g,C,b,R,T,y),{type:H,ref:I,shapeFlag:M,patchFlag:z}=C;let Z=g.nodeType;C.el=g,z===-2&&(O=!1,C.dynamicChildren=null);let J=null;switch(H){case On:Z!==3?C.children===""?(l(C.el=i(""),s(g),g),J=g):J=S():(g.data!==C.children&&(Ar(),g.data=C.children),J=o(g));break;case et:A(g)?(J=o(g),L(C.el=g.content.firstChild,g,b)):Z!==8||y?J=S():J=o(g);break;case qn:if(y&&(g=o(g),Z=g.nodeType),Z===1||Z===3){J=g;const P=!C.children.length;for(let W=0;W<C.staticCount;W++)P&&(C.children+=J.nodeType===1?J.outerHTML:J.data),W===C.staticCount-1&&(C.anchor=J),J=o(J);return y?o(J):J}else S();break;case Se:y?J=h(g,C,b,R,T,O):J=S();break;default:if(M&1)(Z!==1||C.type.toLowerCase()!==g.tagName.toLowerCase())&&!A(g)?J=S():J=p(g,C,b,R,T,O);else if(M&6){C.slotScopeIds=T;const P=s(g);if(y?J=j(g):Dr(g)&&g.data==="teleport start"?J=j(g,g.data,"teleport end"):J=o(g),t(C,P,null,b,R,$o(P),O),Kn(C)&&!C.type.__asyncResolved){let W;y?(W=ee(Se),W.anchor=J?J.previousSibling:P.lastChild):W=g.nodeType===3?na(""):ee("div"),W.el=g,C.component.subTree=W}}else M&64?Z!==8?J=S():J=C.type.hydrate(g,C,b,R,T,O,e,d):M&128&&(J=C.type.hydrate(g,C,b,R,$o(s(g)),T,O,e,f))}return I!=null&&Hi(I,null,R,C),J},p=(g,C,b,R,T,O)=>{O=O||!!C.dynamicChildren;const{type:y,props:S,patchFlag:H,shapeFlag:I,dirs:M,transition:z}=C,Z=y==="input"||y==="option";if(Z||H!==-1){M&&cn(C,null,b,"created");let J=!1;if(A(g)){J=Qg(null,z)&&b&&b.vnode.props&&b.vnode.props.appear;const W=g.content.firstChild;J&&z.beforeEnter(W),L(W,g,b),C.el=g=W}if(I&16&&!(S&&(S.innerHTML||S.textContent))){let W=d(g.firstChild,C,g,b,R,T,O);for(;W;){No(g,1)||Ar();const D=W;W=W.nextSibling,a(D)}}else if(I&8){let W=C.children;W[0]===`
`&&(g.tagName==="PRE"||g.tagName==="TEXTAREA")&&(W=W.slice(1)),g.textContent!==W&&(No(g,0)||Ar(),g.textContent=C.children)}if(S){if(Z||!O||H&48){const W=g.tagName.includes("-");for(const D in S)(Z&&(D.endsWith("value")||D==="indeterminate")||Zn(D)&&!wn(D)||D[0]==="."||W)&&r(g,D,null,S[D],void 0,b)}else if(S.onClick)r(g,"onClick",null,S.onClick,void 0,b);else if(H&4&&Tn(S.style))for(const W in S.style)S.style[W]}let P;(P=S&&S.onVnodeBeforeMount)&&Lt(P,b,C),M&&cn(C,null,b,"beforeMount"),((P=S&&S.onVnodeMounted)||M||J)&&sm(()=>{P&&Lt(P,b,C),J&&z.enter(g),M&&cn(C,null,b,"mounted")},R)}return g.nextSibling},d=(g,C,b,R,T,O,y)=>{y=y||!!C.dynamicChildren;const S=C.children,H=S.length;for(let I=0;I<H;I++){const M=y?S[I]:S[I]=Dt(S[I]),z=M.type===On;g?(z&&!y&&I+1<H&&Dt(S[I+1]).type===On&&(l(i(g.data.slice(M.children.length)),b,o(g)),g.data=M.children),g=f(g,M,R,T,O,y)):z&&!M.children?l(M.el=i(""),b):(No(b,1)||Ar(),n(null,M,b,null,R,T,$o(b),O))}return g},h=(g,C,b,R,T,O)=>{const{slotScopeIds:y}=C;y&&(T=T?T.concat(y):y);const S=s(g),H=d(o(g),C,S,b,R,T,O);return H&&Dr(H)&&H.data==="]"?o(C.anchor=H):(Ar(),l(C.anchor=u("]"),S,H),H)},x=(g,C,b,R,T,O)=>{if(No(g.parentElement,1)||Ar(),C.el=null,O){const H=j(g);for(;;){const I=o(g);if(I&&I!==H)a(I);else break}}const y=o(g),S=s(g);return a(g),n(null,C,S,y,b,R,$o(S),T),b&&(b.vnode.el=C.el,ta(b,C.el)),y},j=(g,C="[",b="]")=>{let R=0;for(;g;)if(g=o(g),g&&Dr(g)&&(g.data===C&&R++,g.data===b)){if(R===0)return o(g);R--}return g},L=(g,C,b)=>{const R=C.parentNode;R&&R.replaceChild(g,C);let T=b;for(;T;)T.vnode.el===C&&(T.vnode.el=T.subTree.el=g),T=T.parent},A=g=>g.nodeType===1&&g.tagName==="TEMPLATE";return[c,f]}const lc="data-allow-mismatch",eb={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function No(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(lc);)e=e.parentElement;const n=e&&e.getAttribute(lc);if(n==null)return!1;if(n==="")return!0;{const r=n.split(",");return t===0&&r.includes("children")?!0:n.split(",").includes(eb[t])}}const tb=ro().requestIdleCallback||(e=>setTimeout(e,1)),nb=ro().cancelIdleCallback||(e=>clearTimeout(e)),dg=(e=1e4)=>t=>{const n=tb(t,{timeout:e});return()=>nb(n)};function rb(e){const{top:t,left:n,bottom:r,right:i}=e.getBoundingClientRect(),{innerHeight:o,innerWidth:s}=window;return(t>0&&t<o||r>0&&r<o)&&(n>0&&n<s||i>0&&i<s)}const pg=e=>(t,n)=>{const r=new IntersectionObserver(i=>{for(const o of i)if(o.isIntersecting){r.disconnect(),t();break}},e);return n(i=>{if(i instanceof Element){if(rb(i))return t(),r.disconnect(),!1;r.observe(i)}}),()=>r.disconnect()},hg=e=>t=>{if(e){const n=matchMedia(e);if(n.matches)t();else return n.addEventListener("change",t,{once:!0}),()=>n.removeEventListener("change",t)}},gg=(e=[])=>(t,n)=>{xe(e)&&(e=[e]);let r=!1;const i=s=>{r||(r=!0,o(),t(),s.target.dispatchEvent(new s.constructor(s.type,s)))},o=()=>{n(s=>{for(const a of e)s.removeEventListener(a,i)})};return n(s=>{for(const a of e)s.addEventListener(a,i,{once:!0})}),o};function ib(e,t){if(Dr(e)&&e.data==="["){let n=1,r=e.nextSibling;for(;r;){if(r.nodeType===1){if(t(r)===!1)break}else if(Dr(r))if(r.data==="]"){if(--n===0)break}else r.data==="["&&n++;r=r.nextSibling}}else t(e)}const Kn=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function mg(e){_e(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:r,delay:i=200,hydrate:o,timeout:s,suspensible:a=!0,onError:l}=e;let u=null,c,f=0;const p=()=>(f++,u=null,d()),d=()=>{let h;return u||(h=u=t().catch(x=>{if(x=x instanceof Error?x:new Error(String(x)),l)return new Promise((j,L)=>{l(x,()=>j(p()),()=>L(x),f+1)});throw x}).then(x=>h!==u&&u?u:(x&&(x.__esModule||x[Symbol.toStringTag]==="Module")&&(x=x.default),c=x,x)))};return Ne({name:"AsyncComponentWrapper",__asyncLoader:d,__asyncHydrate(h,x,j){const L=o?()=>{const A=o(j,g=>ib(h,g));A&&(x.bum||(x.bum=[])).push(A)}:j;c?L():d().then(()=>!x.isUnmounted&&L())},get __asyncResolved(){return c},setup(){const h=ot;if(Vl(h),c)return()=>La(c,h);const x=g=>{u=null,ir(g,h,13,!r)};if(a&&h.suspense||Xr)return d().then(g=>()=>La(g,h)).catch(g=>(x(g),()=>r?ee(r,{error:g}):null));const j=ye(!1),L=ye(),A=ye(!!i);return i&&setTimeout(()=>{A.value=!1},i),s!=null&&setTimeout(()=>{if(!j.value&&!L.value){const g=new Error(`Async component timed out after ${s}ms.`);x(g),L.value=g}},s),d().then(()=>{j.value=!0,h.parent&&uo(h.parent.vnode)&&h.parent.update()}).catch(g=>{x(g),L.value=g}),()=>{if(j.value&&c)return La(c,h);if(L.value&&r)return ee(r,{error:L.value});if(n&&!A.value)return ee(n)}}})}function La(e,t){const{ref:n,props:r,children:i,ce:o}=t.vnode,s=ee(e,r,i);return s.ref=n,s.ce=o,delete t.vnode.ce,s}const uo=e=>e.type.__isKeepAlive,ob={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=gt(),r=n.ctx;if(!r.renderer)return()=>{const A=t.default&&t.default();return A&&A.length===1?A[0]:A};const i=new Map,o=new Set;let s=null;const a=n.suspense,{renderer:{p:l,m:u,um:c,o:{createElement:f}}}=r,p=f("div");r.activate=(A,g,C,b,R)=>{const T=A.component;u(A,g,C,0,a),l(T.vnode,A,g,C,T,a,b,A.slotScopeIds,R),it(()=>{T.isDeactivated=!1,T.a&&hr(T.a);const O=A.props&&A.props.onVnodeMounted;O&&Lt(O,T.parent,A)},a)},r.deactivate=A=>{const g=A.component;ds(g.m),ds(g.a),u(A,p,null,1,a),it(()=>{g.da&&hr(g.da);const C=A.props&&A.props.onVnodeUnmounted;C&&Lt(C,g.parent,A),g.isDeactivated=!0},a)};function d(A){Da(A),c(A,n,a,!0)}function h(A){i.forEach((g,C)=>{const b=hl(g.type);b&&!A(b)&&x(C)})}function x(A){const g=i.get(A);g&&(!s||!en(g,s))?d(g):s&&Da(s),i.delete(A),o.delete(A)}we(()=>[e.include,e.exclude],([A,g])=>{A&&h(C=>wi(A,C)),g&&h(C=>!wi(g,C))},{flush:"post",deep:!0});let j=null;const L=()=>{j!=null&&(ps(n.subTree.type)?it(()=>{i.set(j,Mo(n.subTree))},n.subTree.suspense):i.set(j,Mo(n.subTree)))};return Ue(L),co(L),oi(()=>{i.forEach(A=>{const{subTree:g,suspense:C}=n,b=Mo(g);if(A.type===b.type&&A.key===b.key){Da(b);const R=b.component.da;R&&it(R,C);return}d(A)})}),()=>{if(j=null,!t.default)return s=null;const A=t.default(),g=A[0];if(A.length>1)return s=null,A;if(!pn(g)||!(g.shapeFlag&4)&&!(g.shapeFlag&128))return s=null,g;let C=Mo(g);if(C.type===et)return s=null,C;const b=C.type,R=hl(Kn(C)?C.type.__asyncResolved||{}:b),{include:T,exclude:O,max:y}=e;if(T&&(!R||!wi(T,R))||O&&R&&wi(O,R))return C.shapeFlag&=-257,s=C,g;const S=C.key==null?b:C.key,H=i.get(S);return C.el&&(C=nn(C),g.shapeFlag&128&&(g.ssContent=C)),j=S,H?(C.el=H.el,C.component=H.component,C.transition&&dn(C,C.transition),C.shapeFlag|=512,o.delete(S),o.add(S)):(o.add(S),y&&o.size>parseInt(y,10)&&x(o.values().next().value)),C.shapeFlag|=256,s=C,ps(g.type)?g:C}}},vg=ob;function wi(e,t){return ve(e)?e.some(n=>wi(n,t)):xe(e)?e.split(",").includes(t):oh(e)?(e.lastIndex=0,e.test(t)):!1}function Js(e,t){yg(e,"a",t)}function Qs(e,t){yg(e,"da",t)}function yg(e,t,n=ot){const r=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(Ys(t,r,n),n){let i=n.parent;for(;i&&i.parent;)uo(i.parent.vnode)&&sb(r,t,n,i),i=i.parent}}function sb(e,t,n,r){const i=Ys(t,e,r,!0);Mn(()=>{$s(r[t],i)},n)}function Da(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Mo(e){return e.shapeFlag&128?e.ssContent:e}function Ys(e,t,n=ot,r=!1){if(n){const i=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...s)=>{nr();const a=_r(n),l=Ut(t,n,e,s);return a(),rr(),l});return r?i.unshift(o):i.push(o),o}}const Nn=e=>(t,n=ot)=>{(!Xr||e==="sp")&&Ys(e,(...r)=>t(...r),n)},Hl=Nn("bm"),Ue=Nn("m"),Zs=Nn("bu"),co=Nn("u"),oi=Nn("bum"),Mn=Nn("um"),Gl=Nn("sp"),Ul=Nn("rtg"),Wl=Nn("rtc");function Xl(e,t=ot){Ys("ec",e,t)}const Kl="components",ab="directives";function bg(e,t){return zl(Kl,e,!0,t)||e}const _g=Symbol.for("v-ndc");function ql(e){return xe(e)?zl(Kl,e,!1)||e:e||_g}function Cg(e){return zl(ab,e)}function zl(e,t,n=!0,r=!1){const i=st||ot;if(i){const o=i.type;if(e===Kl){const a=hl(o,!1);if(a&&(a===t||a===He(t)||a===$n(He(t))))return o}const s=uc(i[e]||o[e],t)||uc(i.appContext[e],t);return!s&&r?o:s}}function uc(e,t){return e&&(e[t]||e[He(t)]||e[$n(He(t))])}function qe(e,t,n,r){let i;const o=n&&n[r],s=ve(e);if(s||xe(e)){const a=s&&Tn(e);let l=!1;a&&(l=!jt(e),e=js(e)),i=new Array(e.length);for(let u=0,c=e.length;u<c;u++)i[u]=t(l?Ct(e[u]):e[u],u,void 0,o&&o[u])}else if(typeof e=="number"){i=new Array(e);for(let a=0;a<e;a++)i[a]=t(a+1,a,void 0,o&&o[a])}else if(Le(e))if(e[Symbol.iterator])i=Array.from(e,(a,l)=>t(a,l,void 0,o&&o[l]));else{const a=Object.keys(e);i=new Array(a.length);for(let l=0,u=a.length;l<u;l++){const c=a[l];i[l]=t(e[c],c,l,o&&o[l])}}else i=[];return n&&(n[r]=i),i}function xg(e,t){for(let n=0;n<t.length;n++){const r=t[n];if(ve(r))for(let i=0;i<r.length;i++)e[r[i].name]=r[i].fn;else r&&(e[r.name]=r.key?(...i)=>{const o=r.fn(...i);return o&&(o.key=r.key),o}:r.fn)}return e}function ze(e,t,n={},r,i){if(st.ce||st.parent&&Kn(st.parent)&&st.parent.ce)return t!=="default"&&(n.name=t),fe(),Wr(Se,null,[ee("slot",n,r&&r())],64);let o=e[t];o&&o._c&&(o._d=!1),fe();const s=o&&Jl(o(n)),a=n.key||s&&s.key,l=Wr(Se,{key:(a&&!kt(a)?a:`_${t}`)+(!s&&r?"_fb":"")},s||(r?r():[]),s&&e._===1?64:-2);return!i&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function Jl(e){return e.some(t=>pn(t)?!(t.type===et||t.type===Se&&!Jl(t.children)):!0)?e:null}function Eg(e,t){const n={};for(const r in e)n[t&&/[A-Z]/.test(r)?`on:${r}`:Xn(r)]=e[r];return n}const il=e=>e?dm(e)?po(e):il(e.parent):null,Ii=Pe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>il(e.parent),$root:e=>il(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ql(e),$forceUpdate:e=>e.f||(e.f=()=>{jl(e.update)}),$nextTick:e=>e.n||(e.n=on.bind(e.proxy)),$watch:e=>Eb.bind(e)}),Fa=(e,t)=>e!==Oe&&!e.__isScriptSetup&&$e(e,t),ol={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:i,props:o,accessCache:s,type:a,appContext:l}=e;let u;if(t[0]!=="$"){const d=s[t];if(d!==void 0)switch(d){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return o[t]}else{if(Fa(r,t))return s[t]=1,r[t];if(i!==Oe&&$e(i,t))return s[t]=2,i[t];if((u=e.propsOptions[0])&&$e(u,t))return s[t]=3,o[t];if(n!==Oe&&$e(n,t))return s[t]=4,n[t];sl&&(s[t]=0)}}const c=Ii[t];let f,p;if(c)return t==="$attrs"&&_t(e.attrs,"get",""),c(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==Oe&&$e(n,t))return s[t]=4,n[t];if(p=l.config.globalProperties,$e(p,t))return p[t]},set({_:e},t,n){const{data:r,setupState:i,ctx:o}=e;return Fa(i,t)?(i[t]=n,!0):r!==Oe&&$e(r,t)?(r[t]=n,!0):$e(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:o}},s){let a;return!!n[s]||e!==Oe&&$e(e,s)||Fa(t,s)||(a=o[0])&&$e(a,s)||$e(r,s)||$e(Ii,s)||$e(i.config.globalProperties,s)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:$e(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}},lb=Pe({},ol,{get(e,t){if(t!==Symbol.unscopables)return ol.get(e,t,e)},has(e,t){return t[0]!=="_"&&!kl(t)}});function Sg(){return null}function wg(){return null}function Tg(e){}function Og(e){}function Pg(){return null}function Ag(){}function Ig(e,t){return null}function kg(){return Ng().slots}function $g(){return Ng().attrs}function Ng(){const e=gt();return e.setupContext||(e.setupContext=vm(e))}function Gi(e){return ve(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function Mg(e,t){const n=Gi(e);for(const r in t){if(r.startsWith("__skip"))continue;let i=n[r];i?ve(i)||_e(i)?i=n[r]={type:i,default:t[r]}:i.default=t[r]:i===null&&(i=n[r]={default:t[r]}),i&&t[`__skip_${r}`]&&(i.skipFactory=!0)}return n}function Rg(e,t){return!e||!t?e||t:ve(e)&&ve(t)?e.concat(t):Pe({},Gi(e),Gi(t))}function Lg(e,t){const n={};for(const r in e)t.includes(r)||Object.defineProperty(n,r,{enumerable:!0,get:()=>e[r]});return n}function Dg(e){const t=gt();let n=e();return fl(),Ns(n)&&(n=n.catch(r=>{throw _r(t),r})),[n,()=>_r(t)]}let sl=!0;function ub(e){const t=Ql(e),n=e.proxy,r=e.ctx;sl=!1,t.beforeCreate&&cc(t.beforeCreate,e,"bc");const{data:i,computed:o,methods:s,watch:a,provide:l,inject:u,created:c,beforeMount:f,mounted:p,beforeUpdate:d,updated:h,activated:x,deactivated:j,beforeDestroy:L,beforeUnmount:A,destroyed:g,unmounted:C,render:b,renderTracked:R,renderTriggered:T,errorCaptured:O,serverPrefetch:y,expose:S,inheritAttrs:H,components:I,directives:M,filters:z}=t;if(u&&cb(u,r,null),s)for(const P in s){const W=s[P];_e(W)&&(r[P]=W.bind(n))}if(i){const P=i.call(n,n);Le(P)&&(e.data=ke(P))}if(sl=!0,o)for(const P in o){const W=o[P],D=_e(W)?W.bind(n,n):_e(W.get)?W.get.bind(n,n):ft,V=!_e(W)&&_e(W.set)?W.set.bind(n):ft,X=Ce({get:D,set:V});Object.defineProperty(r,P,{enumerable:!0,configurable:!0,get:()=>X.value,set:te=>X.value=te})}if(a)for(const P in a)Fg(a[P],r,n,P);if(l){const P=_e(l)?l.call(n):l;Reflect.ownKeys(P).forEach(W=>{jr(W,P[W])})}c&&cc(c,e,"c");function J(P,W){ve(W)?W.forEach(D=>P(D.bind(n))):W&&P(W.bind(n))}if(J(Hl,f),J(Ue,p),J(Zs,d),J(co,h),J(Js,x),J(Qs,j),J(Xl,O),J(Wl,R),J(Ul,T),J(oi,A),J(Mn,C),J(Gl,y),ve(S))if(S.length){const P=e.exposed||(e.exposed={});S.forEach(W=>{Object.defineProperty(P,W,{get:()=>n[W],set:D=>n[W]=D})})}else e.exposed||(e.exposed={});b&&e.render===ft&&(e.render=b),H!=null&&(e.inheritAttrs=H),I&&(e.components=I),M&&(e.directives=M),y&&Vl(e)}function cb(e,t,n=ft){ve(e)&&(e=al(e));for(const r in e){const i=e[r];let o;Le(i)?"default"in i?o=zt(i.from||r,i.default,!0):o=zt(i.from||r):o=zt(i),rt(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:s=>o.value=s}):t[r]=o}}function cc(e,t,n){Ut(ve(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Fg(e,t,n,r){let i=r.includes(".")?em(n,r):()=>n[r];if(xe(e)){const o=t[e];_e(o)&&we(i,o)}else if(_e(e))we(i,e.bind(n));else if(Le(e))if(ve(e))e.forEach(o=>Fg(o,t,n,r));else{const o=_e(e.handler)?e.handler.bind(n):t[e.handler];_e(o)&&we(i,o,e)}}function Ql(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:i,optionsCache:o,config:{optionMergeStrategies:s}}=e.appContext,a=o.get(t);let l;return a?l=a:!i.length&&!n&&!r?l=t:(l={},i.length&&i.forEach(u=>fs(l,u,s,!0)),fs(l,t,s)),Le(t)&&o.set(t,l),l}function fs(e,t,n,r=!1){const{mixins:i,extends:o}=t;o&&fs(e,o,n,!0),i&&i.forEach(s=>fs(e,s,n,!0));for(const s in t)if(!(r&&s==="expose")){const a=fb[s]||n&&n[s];e[s]=a?a(e[s],t[s]):t[s]}return e}const fb={data:fc,props:dc,emits:dc,methods:Ti,computed:Ti,beforeCreate:Tt,created:Tt,beforeMount:Tt,mounted:Tt,beforeUpdate:Tt,updated:Tt,beforeDestroy:Tt,beforeUnmount:Tt,destroyed:Tt,unmounted:Tt,activated:Tt,deactivated:Tt,errorCaptured:Tt,serverPrefetch:Tt,components:Ti,directives:Ti,watch:pb,provide:fc,inject:db};function fc(e,t){return t?e?function(){return Pe(_e(e)?e.call(this,this):e,_e(t)?t.call(this,this):t)}:t:e}function db(e,t){return Ti(al(e),al(t))}function al(e){if(ve(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Tt(e,t){return e?[...new Set([].concat(e,t))]:t}function Ti(e,t){return e?Pe(Object.create(null),e,t):t}function dc(e,t){return e?ve(e)&&ve(t)?[...new Set([...e,...t])]:Pe(Object.create(null),Gi(e),Gi(t??{})):t}function pb(e,t){if(!e)return t;if(!t)return e;const n=Pe(Object.create(null),e);for(const r in t)n[r]=Tt(e[r],t[r]);return n}function jg(){return{app:null,config:{isNativeTag:Lr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let hb=0;function gb(e,t){return function(r,i=null){_e(r)||(r=Pe({},r)),i!=null&&!Le(i)&&(i=null);const o=jg(),s=new WeakSet,a=[];let l=!1;const u=o.app={_uid:hb++,_component:r,_props:i,_container:null,_context:o,_instance:null,version:cu,get config(){return o.config},set config(c){},use(c,...f){return s.has(c)||(c&&_e(c.install)?(s.add(c),c.install(u,...f)):_e(c)&&(s.add(c),c(u,...f))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,f){return f?(o.components[c]=f,u):o.components[c]},directive(c,f){return f?(o.directives[c]=f,u):o.directives[c]},mount(c,f,p){if(!l){const d=u._ceVNode||ee(r,i);return d.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),f&&t?t(d,c):e(d,c,p),l=!0,u._container=c,c.__vue_app__=u,po(d.component)}},onUnmount(c){a.push(c)},unmount(){l&&(Ut(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return o.provides[c]=f,u},runWithContext(c){const f=mr;mr=u;try{return c()}finally{mr=f}}};return u}}let mr=null;function jr(e,t){if(ot){let n=ot.provides;const r=ot.parent&&ot.parent.provides;r===n&&(n=ot.provides=Object.create(r)),n[e]=t}}function zt(e,t,n=!1){const r=ot||st;if(r||mr){const i=mr?mr._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&_e(t)?t.call(r&&r.proxy):t}}function Bg(){return!!(ot||st||mr)}const Vg={},Hg=()=>Object.create(Vg),Gg=e=>Object.getPrototypeOf(e)===Vg;function mb(e,t,n,r=!1){const i={},o=Hg();e.propsDefaults=Object.create(null),Ug(e,t,i,o);for(const s in e.propsOptions[0])s in i||(i[s]=void 0);n?e.props=r?i:Vs(i):e.type.props?e.props=i:e.props=o,e.attrs=o}function vb(e,t,n,r){const{props:i,attrs:o,vnode:{patchFlag:s}}=e,a=Ae(i),[l]=e.propsOptions;let u=!1;if((r||s>0)&&!(s&16)){if(s&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let p=c[f];if(ea(e.emitsOptions,p))continue;const d=t[p];if(l)if($e(o,p))d!==o[p]&&(o[p]=d,u=!0);else{const h=He(p);i[h]=ll(l,a,h,d,e,!1)}else d!==o[p]&&(o[p]=d,u=!0)}}}else{Ug(e,t,i,o)&&(u=!0);let c;for(const f in a)(!t||!$e(t,f)&&((c=xt(f))===f||!$e(t,c)))&&(l?n&&(n[f]!==void 0||n[c]!==void 0)&&(i[f]=ll(l,a,f,void 0,e,!0)):delete i[f]);if(o!==a)for(const f in o)(!t||!$e(t,f))&&(delete o[f],u=!0)}u&&Cn(e.attrs,"set","")}function Ug(e,t,n,r){const[i,o]=e.propsOptions;let s=!1,a;if(t)for(let l in t){if(wn(l))continue;const u=t[l];let c;i&&$e(i,c=He(l))?!o||!o.includes(c)?n[c]=u:(a||(a={}))[c]=u:ea(e.emitsOptions,l)||(!(l in r)||u!==r[l])&&(r[l]=u,s=!0)}if(o){const l=Ae(n),u=a||Oe;for(let c=0;c<o.length;c++){const f=o[c];n[f]=ll(i,l,f,u[f],e,!$e(u,f))}}return s}function ll(e,t,n,r,i,o){const s=e[n];if(s!=null){const a=$e(s,"default");if(a&&r===void 0){const l=s.default;if(s.type!==Function&&!s.skipFactory&&_e(l)){const{propsDefaults:u}=i;if(n in u)r=u[n];else{const c=_r(i);r=u[n]=l.call(null,t),c()}}else r=l;i.ce&&i.ce._setProp(n,r)}s[0]&&(o&&!a?r=!1:s[1]&&(r===""||r===xt(n))&&(r=!0))}return r}const yb=new WeakMap;function Wg(e,t,n=!1){const r=n?yb:t.propsCache,i=r.get(e);if(i)return i;const o=e.props,s={},a=[];let l=!1;if(!_e(e)){const c=f=>{l=!0;const[p,d]=Wg(f,t,!0);Pe(s,p),d&&a.push(...d)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!l)return Le(e)&&r.set(e,dr),dr;if(ve(o))for(let c=0;c<o.length;c++){const f=He(o[c]);pc(f)&&(s[f]=Oe)}else if(o)for(const c in o){const f=He(c);if(pc(f)){const p=o[c],d=s[f]=ve(p)||_e(p)?{type:p}:Pe({},p),h=d.type;let x=!1,j=!0;if(ve(h))for(let L=0;L<h.length;++L){const A=h[L],g=_e(A)&&A.name;if(g==="Boolean"){x=!0;break}else g==="String"&&(j=!1)}else x=_e(h)&&h.name==="Boolean";d[0]=x,d[1]=j,(x||$e(d,"default"))&&a.push(f)}}const u=[s,a];return Le(e)&&r.set(e,u),u}function pc(e){return e[0]!=="$"&&!wn(e)}const Xg=e=>e[0]==="_"||e==="$stable",Yl=e=>ve(e)?e.map(Dt):[Dt(e)],bb=(e,t,n)=>{if(t._n)return t;const r=Ks((...i)=>Yl(t(...i)),n);return r._c=!1,r},Kg=(e,t,n)=>{const r=e._ctx;for(const i in e){if(Xg(i))continue;const o=e[i];if(_e(o))t[i]=bb(i,o,r);else if(o!=null){const s=Yl(o);t[i]=()=>s}}},qg=(e,t)=>{const n=Yl(t);e.slots.default=()=>n},zg=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},_b=(e,t,n)=>{const r=e.slots=Hg();if(e.vnode.shapeFlag&32){const i=t._;i?(zg(r,t,n),n&&Il(r,"_",i,!0)):Kg(t,r)}else t&&qg(e,t)},Cb=(e,t,n)=>{const{vnode:r,slots:i}=e;let o=!0,s=Oe;if(r.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:zg(i,t,n):(o=!t.$stable,Kg(t,i)),s=t}else t&&(qg(e,t),s={default:1});if(o)for(const a in i)!Xg(a)&&s[a]==null&&delete i[a]},it=sm;function Zl(e){return Jg(e)}function eu(e){return Jg(e,Zy)}function Jg(e,t){const n=ro();n.__VUE__=!0;const{insert:r,remove:i,patchProp:o,createElement:s,createText:a,createComment:l,setText:u,setElementText:c,parentNode:f,nextSibling:p,setScopeId:d=ft,insertStaticContent:h}=e,x=(m,_,N,K=null,w=null,B=null,E=void 0,k=null,$=!!_.dynamicChildren)=>{if(m===_)return;m&&!en(m,_)&&(K=U(m),te(m,w,B,!0),m=null),_.patchFlag===-2&&($=!1,_.dynamicChildren=null);const{type:F,ref:Q,shapeFlag:Y}=_;switch(F){case On:j(m,_,N,K);break;case et:L(m,_,N,K);break;case qn:m==null&&A(_,N,K,E);break;case Se:I(m,_,N,K,w,B,E,k,$);break;default:Y&1?b(m,_,N,K,w,B,E,k,$):Y&6?M(m,_,N,K,w,B,E,k,$):(Y&64||Y&128)&&F.process(m,_,N,K,w,B,E,k,$,se)}Q!=null&&w&&Hi(Q,m&&m.ref,B,_||m,!_)},j=(m,_,N,K)=>{if(m==null)r(_.el=a(_.children),N,K);else{const w=_.el=m.el;_.children!==m.children&&u(w,_.children)}},L=(m,_,N,K)=>{m==null?r(_.el=l(_.children||""),N,K):_.el=m.el},A=(m,_,N,K)=>{[m.el,m.anchor]=h(m.children,_,N,K,m.el,m.anchor)},g=({el:m,anchor:_},N,K)=>{let w;for(;m&&m!==_;)w=p(m),r(m,N,K),m=w;r(_,N,K)},C=({el:m,anchor:_})=>{let N;for(;m&&m!==_;)N=p(m),i(m),m=N;i(_)},b=(m,_,N,K,w,B,E,k,$)=>{_.type==="svg"?E="svg":_.type==="math"&&(E="mathml"),m==null?R(_,N,K,w,B,E,k,$):y(m,_,w,B,E,k,$)},R=(m,_,N,K,w,B,E,k)=>{let $,F;const{props:Q,shapeFlag:Y,transition:oe,dirs:ue}=m;if($=m.el=s(m.type,B,Q&&Q.is,Q),Y&8?c($,m.children):Y&16&&O(m.children,$,null,K,w,ja(m,B),E,k),ue&&cn(m,null,K,"created"),T($,m,m.scopeId,E,K),Q){for(const he in Q)he!=="value"&&!wn(he)&&o($,he,null,Q[he],B,K);"value"in Q&&o($,"value",null,Q.value,B),(F=Q.onVnodeBeforeMount)&&Lt(F,K,m)}ue&&cn(m,null,K,"beforeMount");const pe=Qg(w,oe);pe&&oe.beforeEnter($),r($,_,N),((F=Q&&Q.onVnodeMounted)||pe||ue)&&it(()=>{F&&Lt(F,K,m),pe&&oe.enter($),ue&&cn(m,null,K,"mounted")},w)},T=(m,_,N,K,w)=>{if(N&&d(m,N),K)for(let B=0;B<K.length;B++)d(m,K[B]);if(w){let B=w.subTree;if(_===B||ps(B.type)&&(B.ssContent===_||B.ssFallback===_)){const E=w.vnode;T(m,E,E.scopeId,E.slotScopeIds,w.parent)}}},O=(m,_,N,K,w,B,E,k,$=0)=>{for(let F=$;F<m.length;F++){const Q=m[F]=k?Hn(m[F]):Dt(m[F]);x(null,Q,_,N,K,w,B,E,k)}},y=(m,_,N,K,w,B,E)=>{const k=_.el=m.el;let{patchFlag:$,dynamicChildren:F,dirs:Q}=_;$|=m.patchFlag&16;const Y=m.props||Oe,oe=_.props||Oe;let ue;if(N&&ar(N,!1),(ue=oe.onVnodeBeforeUpdate)&&Lt(ue,N,_,m),Q&&cn(_,m,N,"beforeUpdate"),N&&ar(N,!0),(Y.innerHTML&&oe.innerHTML==null||Y.textContent&&oe.textContent==null)&&c(k,""),F?S(m.dynamicChildren,F,k,N,K,ja(_,w),B):E||W(m,_,k,null,N,K,ja(_,w),B,!1),$>0){if($&16)H(k,Y,oe,N,w);else if($&2&&Y.class!==oe.class&&o(k,"class",null,oe.class,w),$&4&&o(k,"style",Y.style,oe.style,w),$&8){const pe=_.dynamicProps;for(let he=0;he<pe.length;he++){const ge=pe[he],be=Y[ge],Te=oe[ge];(Te!==be||ge==="value")&&o(k,ge,be,Te,w,N)}}$&1&&m.children!==_.children&&c(k,_.children)}else!E&&F==null&&H(k,Y,oe,N,w);((ue=oe.onVnodeUpdated)||Q)&&it(()=>{ue&&Lt(ue,N,_,m),Q&&cn(_,m,N,"updated")},K)},S=(m,_,N,K,w,B,E)=>{for(let k=0;k<_.length;k++){const $=m[k],F=_[k],Q=$.el&&($.type===Se||!en($,F)||$.shapeFlag&70)?f($.el):N;x($,F,Q,null,K,w,B,E,!0)}},H=(m,_,N,K,w)=>{if(_!==N){if(_!==Oe)for(const B in _)!wn(B)&&!(B in N)&&o(m,B,_[B],null,w,K);for(const B in N){if(wn(B))continue;const E=N[B],k=_[B];E!==k&&B!=="value"&&o(m,B,k,E,w,K)}"value"in N&&o(m,"value",_.value,N.value,w)}},I=(m,_,N,K,w,B,E,k,$)=>{const F=_.el=m?m.el:a(""),Q=_.anchor=m?m.anchor:a("");let{patchFlag:Y,dynamicChildren:oe,slotScopeIds:ue}=_;ue&&(k=k?k.concat(ue):ue),m==null?(r(F,N,K),r(Q,N,K),O(_.children||[],N,Q,w,B,E,k,$)):Y>0&&Y&64&&oe&&m.dynamicChildren?(S(m.dynamicChildren,oe,N,w,B,E,k),(_.key!=null||w&&_===w.subTree)&&tu(m,_,!0)):W(m,_,N,Q,w,B,E,k,$)},M=(m,_,N,K,w,B,E,k,$)=>{_.slotScopeIds=k,m==null?_.shapeFlag&512?w.ctx.activate(_,N,K,E,$):z(_,N,K,w,B,E,$):Z(m,_,$)},z=(m,_,N,K,w,B,E)=>{const k=m.component=fm(m,K,w);if(uo(m)&&(k.ctx.renderer=se),pm(k,!1,E),k.asyncDep){if(w&&w.registerDep(k,J,E),!m.el){const $=k.subTree=ee(et);L(null,$,_,N)}}else J(k,m,_,N,w,B,E)},Z=(m,_,N)=>{const K=_.component=m.component;if(Pb(m,_,N))if(K.asyncDep&&!K.asyncResolved){P(K,_,N);return}else K.next=_,K.update();else _.el=m.el,K.vnode=_},J=(m,_,N,K,w,B,E)=>{const k=()=>{if(m.isMounted){let{next:Y,bu:oe,u:ue,parent:pe,vnode:he}=m;{const Ye=Yg(m);if(Ye){Y&&(Y.el=he.el,P(m,Y,E)),Ye.asyncDep.then(()=>{m.isUnmounted||k()});return}}let ge=Y,be;ar(m,!1),Y?(Y.el=he.el,P(m,Y,E)):Y=he,oe&&hr(oe),(be=Y.props&&Y.props.onVnodeBeforeUpdate)&&Lt(be,pe,Y,he),ar(m,!0);const Te=Yo(m),tt=m.subTree;m.subTree=Te,x(tt,Te,f(tt.el),U(tt),m,w,B),Y.el=Te.el,ge===null&&ta(m,Te.el),ue&&it(ue,w),(be=Y.props&&Y.props.onVnodeUpdated)&&it(()=>Lt(be,pe,Y,he),w)}else{let Y;const{el:oe,props:ue}=_,{bm:pe,m:he,parent:ge,root:be,type:Te}=m,tt=Kn(_);if(ar(m,!1),pe&&hr(pe),!tt&&(Y=ue&&ue.onVnodeBeforeMount)&&Lt(Y,ge,_),ar(m,!0),oe&&G){const Ye=()=>{m.subTree=Yo(m),G(oe,m.subTree,m,w,null)};tt&&Te.__asyncHydrate?Te.__asyncHydrate(oe,m,Ye):Ye()}else{be.ce&&be.ce._injectChildStyle(Te);const Ye=m.subTree=Yo(m);x(null,Ye,N,K,m,w,B),_.el=Ye.el}if(he&&it(he,w),!tt&&(Y=ue&&ue.onVnodeMounted)){const Ye=_;it(()=>Lt(Y,ge,Ye),w)}(_.shapeFlag&256||ge&&Kn(ge.vnode)&&ge.vnode.shapeFlag&256)&&m.a&&it(m.a,w),m.isMounted=!0,_=N=K=null}};m.scope.on();const $=m.effect=new Gr(k);m.scope.off();const F=m.update=$.run.bind($),Q=m.job=$.runIfDirty.bind($);Q.i=m,Q.id=m.uid,$.scheduler=()=>jl(Q),ar(m,!0),F()},P=(m,_,N)=>{_.component=m;const K=m.vnode.props;m.vnode=_,m.next=null,vb(m,_.props,K,N),Cb(m,_.children,N),nr(),nc(m),rr()},W=(m,_,N,K,w,B,E,k,$=!1)=>{const F=m&&m.children,Q=m?m.shapeFlag:0,Y=_.children,{patchFlag:oe,shapeFlag:ue}=_;if(oe>0){if(oe&128){V(F,Y,N,K,w,B,E,k,$);return}else if(oe&256){D(F,Y,N,K,w,B,E,k,$);return}}ue&8?(Q&16&&me(F,w,B),Y!==F&&c(N,Y)):Q&16?ue&16?V(F,Y,N,K,w,B,E,k,$):me(F,w,B,!0):(Q&8&&c(N,""),ue&16&&O(Y,N,K,w,B,E,k,$))},D=(m,_,N,K,w,B,E,k,$)=>{m=m||dr,_=_||dr;const F=m.length,Q=_.length,Y=Math.min(F,Q);let oe;for(oe=0;oe<Y;oe++){const ue=_[oe]=$?Hn(_[oe]):Dt(_[oe]);x(m[oe],ue,N,null,w,B,E,k,$)}F>Q?me(m,w,B,!0,!1,Y):O(_,N,K,w,B,E,k,$,Y)},V=(m,_,N,K,w,B,E,k,$)=>{let F=0;const Q=_.length;let Y=m.length-1,oe=Q-1;for(;F<=Y&&F<=oe;){const ue=m[F],pe=_[F]=$?Hn(_[F]):Dt(_[F]);if(en(ue,pe))x(ue,pe,N,null,w,B,E,k,$);else break;F++}for(;F<=Y&&F<=oe;){const ue=m[Y],pe=_[oe]=$?Hn(_[oe]):Dt(_[oe]);if(en(ue,pe))x(ue,pe,N,null,w,B,E,k,$);else break;Y--,oe--}if(F>Y){if(F<=oe){const ue=oe+1,pe=ue<Q?_[ue].el:K;for(;F<=oe;)x(null,_[F]=$?Hn(_[F]):Dt(_[F]),N,pe,w,B,E,k,$),F++}}else if(F>oe)for(;F<=Y;)te(m[F],w,B,!0),F++;else{const ue=F,pe=F,he=new Map;for(F=pe;F<=oe;F++){const lt=_[F]=$?Hn(_[F]):Dt(_[F]);lt.key!=null&&he.set(lt.key,F)}let ge,be=0;const Te=oe-pe+1;let tt=!1,Ye=0;const Mt=new Array(Te);for(F=0;F<Te;F++)Mt[F]=0;for(F=ue;F<=Y;F++){const lt=m[F];if(be>=Te){te(lt,w,B,!0);continue}let Bt;if(lt.key!=null)Bt=he.get(lt.key);else for(ge=pe;ge<=oe;ge++)if(Mt[ge-pe]===0&&en(lt,_[ge])){Bt=ge;break}Bt===void 0?te(lt,w,B,!0):(Mt[Bt-pe]=F+1,Bt>=Ye?Ye=Bt:tt=!0,x(lt,_[Bt],N,null,w,B,E,k,$),be++)}const Eo=tt?xb(Mt):dr;for(ge=Eo.length-1,F=Te-1;F>=0;F--){const lt=pe+F,Bt=_[lt],So=lt+1<Q?_[lt+1].el:K;Mt[F]===0?x(null,Bt,N,So,w,B,E,k,$):tt&&(ge<0||F!==Eo[ge]?X(Bt,N,So,2):ge--)}}},X=(m,_,N,K,w=null)=>{const{el:B,type:E,transition:k,children:$,shapeFlag:F}=m;if(F&6){X(m.component.subTree,_,N,K);return}if(F&128){m.suspense.move(_,N,K);return}if(F&64){E.move(m,_,N,se);return}if(E===Se){r(B,_,N);for(let Y=0;Y<$.length;Y++)X($[Y],_,N,K);r(m.anchor,_,N);return}if(E===qn){g(m,_,N);return}if(K!==2&&F&1&&k)if(K===0)k.beforeEnter(B),r(B,_,N),it(()=>k.enter(B),w);else{const{leave:Y,delayLeave:oe,afterLeave:ue}=k,pe=()=>r(B,_,N),he=()=>{Y(B,()=>{pe(),ue&&ue()})};oe?oe(B,pe,he):he()}else r(B,_,N)},te=(m,_,N,K=!1,w=!1)=>{const{type:B,props:E,ref:k,children:$,dynamicChildren:F,shapeFlag:Q,patchFlag:Y,dirs:oe,cacheIndex:ue}=m;if(Y===-2&&(w=!1),k!=null&&Hi(k,null,N,m,!0),ue!=null&&(_.renderCache[ue]=void 0),Q&256){_.ctx.deactivate(m);return}const pe=Q&1&&oe,he=!Kn(m);let ge;if(he&&(ge=E&&E.onVnodeBeforeUnmount)&&Lt(ge,_,m),Q&6)ce(m.component,N,K);else{if(Q&128){m.suspense.unmount(N,K);return}pe&&cn(m,null,_,"beforeUnmount"),Q&64?m.type.remove(m,_,N,se,K):F&&!F.hasOnce&&(B!==Se||Y>0&&Y&64)?me(F,_,N,!1,!0):(B===Se&&Y&384||!w&&Q&16)&&me($,_,N),K&&ne(m)}(he&&(ge=E&&E.onVnodeUnmounted)||pe)&&it(()=>{ge&&Lt(ge,_,m),pe&&cn(m,null,_,"unmounted")},N)},ne=m=>{const{type:_,el:N,anchor:K,transition:w}=m;if(_===Se){le(N,K);return}if(_===qn){C(m);return}const B=()=>{i(N),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(m.shapeFlag&1&&w&&!w.persisted){const{leave:E,delayLeave:k}=w,$=()=>E(N,B);k?k(m.el,B,$):$()}else B()},le=(m,_)=>{let N;for(;m!==_;)N=p(m),i(m),m=N;i(_)},ce=(m,_,N)=>{const{bum:K,scope:w,job:B,subTree:E,um:k,m:$,a:F}=m;ds($),ds(F),K&&hr(K),w.stop(),B&&(B.flags|=8,te(E,m,_,N)),k&&it(k,_),it(()=>{m.isUnmounted=!0},_),_&&_.pendingBranch&&!_.isUnmounted&&m.asyncDep&&!m.asyncResolved&&m.suspenseId===_.pendingId&&(_.deps--,_.deps===0&&_.resolve())},me=(m,_,N,K=!1,w=!1,B=0)=>{for(let E=B;E<m.length;E++)te(m[E],_,N,K,w)},U=m=>{if(m.shapeFlag&6)return U(m.component.subTree);if(m.shapeFlag&128)return m.suspense.next();const _=p(m.anchor||m.el),N=_&&_[ng];return N?p(N):_};let re=!1;const ie=(m,_,N)=>{m==null?_._vnode&&te(_._vnode,null,null,!0):x(_._vnode||null,m,_,null,null,null,N),_._vnode=m,re||(re=!0,nc(),cs(),re=!1)},se={p:x,um:te,m:X,r:ne,mt:z,mc:O,pc:W,pbc:S,n:U,o:e};let q,G;return t&&([q,G]=t(se)),{render:ie,hydrate:q,createApp:gb(ie,q)}}function ja({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ar({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Qg(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function tu(e,t,n=!1){const r=e.children,i=t.children;if(ve(r)&&ve(i))for(let o=0;o<r.length;o++){const s=r[o];let a=i[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=i[o]=Hn(i[o]),a.el=s.el),!n&&a.patchFlag!==-2&&tu(s,a)),a.type===On&&(a.el=s.el)}}function xb(e){const t=e.slice(),n=[0];let r,i,o,s,a;const l=e.length;for(r=0;r<l;r++){const u=e[r];if(u!==0){if(i=n[n.length-1],e[i]<u){t[r]=i,n.push(r);continue}for(o=0,s=n.length-1;o<s;)a=o+s>>1,e[n[a]]<u?o=a+1:s=a;u<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,s=n[o-1];o-- >0;)n[o]=s,s=t[s];return n}function Yg(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Yg(t)}function ds(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const nu=Symbol.for("v-scx"),ru=()=>zt(nu);function iu(e,t){return fo(e,null,t)}function Zg(e,t){return fo(e,null,{flush:"post"})}function ou(e,t){return fo(e,null,{flush:"sync"})}function we(e,t,n){return fo(e,t,n)}function fo(e,t,n=Oe){const{immediate:r,deep:i,flush:o,once:s}=n,a=Pe({},n),l=t&&r||!t&&o!=="post";let u;if(Xr){if(o==="sync"){const d=ru();u=d.__watcherHandles||(d.__watcherHandles=[])}else if(!l){const d=()=>{};return d.stop=ft,d.resume=ft,d.pause=ft,d}}const c=ot;a.call=(d,h,x)=>Ut(d,c,h,x);let f=!1;o==="post"?a.scheduler=d=>{it(d,c&&c.suspense)}:o!=="sync"&&(f=!0,a.scheduler=(d,h)=>{h?d():jl(d)}),a.augmentJob=d=>{t&&(d.flags|=4),f&&(d.flags|=2,c&&(d.id=c.uid,d.i=c))};const p=Gy(e,t,a);return Xr&&(u?u.push(p):l&&p()),p}function Eb(e,t,n){const r=this.proxy,i=xe(e)?e.includes(".")?em(r,e):()=>r[e]:e.bind(r,r);let o;_e(t)?o=t:(o=t.handler,n=t);const s=_r(this),a=fo(i,o.bind(r),n);return s(),a}function em(e,t){const n=t.split(".");return()=>{let r=e;for(let i=0;i<n.length&&r;i++)r=r[n[i]];return r}}function tm(e,t,n=Oe){const r=gt(),i=He(t),o=xt(t),s=nm(e,i),a=Ws((l,u)=>{let c,f=Oe,p;return ou(()=>{const d=e[i];bt(c,d)&&(c=d,u())}),{get(){return l(),n.get?n.get(c):c},set(d){const h=n.set?n.set(d):d;if(!bt(h,c)&&!(f!==Oe&&bt(d,f)))return;const x=r.vnode.props;x&&(t in x||i in x||o in x)&&(`onUpdate:${t}`in x||`onUpdate:${i}`in x||`onUpdate:${o}`in x)||(c=d,u()),r.emit(`update:${t}`,h),bt(d,h)&&bt(d,f)&&!bt(h,p)&&u(),f=d,p=h}}});return a[Symbol.iterator]=()=>{let l=0;return{next(){return l<2?{value:l++?s||Oe:a,done:!1}:{done:!0}}}},a}const nm=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${He(t)}Modifiers`]||e[`${xt(t)}Modifiers`];function Sb(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Oe;let i=n;const o=t.startsWith("update:"),s=o&&nm(r,t.slice(7));s&&(s.trim&&(i=n.map(c=>xe(c)?c.trim():c)),s.number&&(i=n.map(Li)));let a,l=r[a=Xn(t)]||r[a=Xn(He(t))];!l&&o&&(l=r[a=Xn(xt(t))]),l&&Ut(l,e,6,i);const u=r[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Ut(u,e,6,i)}}function rm(e,t,n=!1){const r=t.emitsCache,i=r.get(e);if(i!==void 0)return i;const o=e.emits;let s={},a=!1;if(!_e(e)){const l=u=>{const c=rm(u,t,!0);c&&(a=!0,Pe(s,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(Le(e)&&r.set(e,null),null):(ve(o)?o.forEach(l=>s[l]=null):Pe(s,o),Le(e)&&r.set(e,s),s)}function ea(e,t){return!e||!Zn(t)?!1:(t=t.slice(2).replace(/Once$/,""),$e(e,t[0].toLowerCase()+t.slice(1))||$e(e,xt(t))||$e(e,t))}function Yo(e){const{type:t,vnode:n,proxy:r,withProxy:i,propsOptions:[o],slots:s,attrs:a,emit:l,render:u,renderCache:c,props:f,data:p,setupState:d,ctx:h,inheritAttrs:x}=e,j=Vi(e);let L,A;try{if(n.shapeFlag&4){const C=i||r,b=C;L=Dt(u.call(b,C,c,f,d,p,h)),A=a}else{const C=t;L=Dt(C.length>1?C(f,{attrs:a,slots:s,emit:l}):C(f,null)),A=t.props?a:Tb(a)}}catch(C){ki.length=0,ir(C,e,1),L=ee(et)}let g=L;if(A&&x!==!1){const C=Object.keys(A),{shapeFlag:b}=g;C.length&&b&7&&(o&&C.some(ks)&&(A=Ob(A,o)),g=nn(g,A,!1,!0))}return n.dirs&&(g=nn(g,null,!1,!0),g.dirs=g.dirs?g.dirs.concat(n.dirs):n.dirs),n.transition&&dn(g,n.transition),L=g,Vi(j),L}function wb(e,t=!0){let n;for(let r=0;r<e.length;r++){const i=e[r];if(pn(i)){if(i.type!==et||i.children==="v-if"){if(n)return;n=i}}else return}return n}const Tb=e=>{let t;for(const n in e)(n==="class"||n==="style"||Zn(n))&&((t||(t={}))[n]=e[n]);return t},Ob=(e,t)=>{const n={};for(const r in e)(!ks(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Pb(e,t,n){const{props:r,children:i,component:o}=e,{props:s,children:a,patchFlag:l}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?hc(r,s,u):!!s;if(l&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const p=c[f];if(s[p]!==r[p]&&!ea(u,p))return!0}}}else return(i||a)&&(!a||!a.$stable)?!0:r===s?!1:r?s?hc(r,s,u):!0:!!s;return!1}function hc(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let i=0;i<r.length;i++){const o=r[i];if(t[o]!==e[o]&&!ea(n,o))return!0}return!1}function ta({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const ps=e=>e.__isSuspense;let ul=0;const Ab={name:"Suspense",__isSuspense:!0,process(e,t,n,r,i,o,s,a,l,u){if(e==null)Ib(t,n,r,i,o,s,a,l,u);else{if(o&&o.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}kb(e,t,n,r,i,s,a,l,u)}},hydrate:$b,normalize:Nb},im=Ab;function Ui(e,t){const n=e.props&&e.props[t];_e(n)&&n()}function Ib(e,t,n,r,i,o,s,a,l){const{p:u,o:{createElement:c}}=l,f=c("div"),p=e.suspense=om(e,i,r,t,f,n,o,s,a,l);u(null,p.pendingBranch=e.ssContent,f,null,r,p,o,s),p.deps>0?(Ui(e,"onPending"),Ui(e,"onFallback"),u(null,e.ssFallback,t,n,r,null,o,s),Br(p,e.ssFallback)):p.resolve(!1,!0)}function kb(e,t,n,r,i,o,s,a,{p:l,um:u,o:{createElement:c}}){const f=t.suspense=e.suspense;f.vnode=t,t.el=e.el;const p=t.ssContent,d=t.ssFallback,{activeBranch:h,pendingBranch:x,isInFallback:j,isHydrating:L}=f;if(x)f.pendingBranch=p,en(p,x)?(l(x,p,f.hiddenContainer,null,i,f,o,s,a),f.deps<=0?f.resolve():j&&(L||(l(h,d,n,r,i,null,o,s,a),Br(f,d)))):(f.pendingId=ul++,L?(f.isHydrating=!1,f.activeBranch=x):u(x,i,f),f.deps=0,f.effects.length=0,f.hiddenContainer=c("div"),j?(l(null,p,f.hiddenContainer,null,i,f,o,s,a),f.deps<=0?f.resolve():(l(h,d,n,r,i,null,o,s,a),Br(f,d))):h&&en(p,h)?(l(h,p,n,r,i,f,o,s,a),f.resolve(!0)):(l(null,p,f.hiddenContainer,null,i,f,o,s,a),f.deps<=0&&f.resolve()));else if(h&&en(p,h))l(h,p,n,r,i,f,o,s,a),Br(f,p);else if(Ui(t,"onPending"),f.pendingBranch=p,p.shapeFlag&512?f.pendingId=p.component.suspenseId:f.pendingId=ul++,l(null,p,f.hiddenContainer,null,i,f,o,s,a),f.deps<=0)f.resolve();else{const{timeout:A,pendingId:g}=f;A>0?setTimeout(()=>{f.pendingId===g&&f.fallback(d)},A):A===0&&f.fallback(d)}}function om(e,t,n,r,i,o,s,a,l,u,c=!1){const{p:f,m:p,um:d,n:h,o:{parentNode:x,remove:j}}=u;let L;const A=Mb(e);A&&t&&t.pendingBranch&&(L=t.pendingId,t.deps++);const g=e.props?Di(e.props.timeout):void 0,C=o,b={vnode:e,parent:t,parentComponent:n,namespace:s,container:r,hiddenContainer:i,deps:0,pendingId:ul++,timeout:typeof g=="number"?g:-1,activeBranch:null,pendingBranch:null,isInFallback:!c,isHydrating:c,isUnmounted:!1,effects:[],resolve(R=!1,T=!1){const{vnode:O,activeBranch:y,pendingBranch:S,pendingId:H,effects:I,parentComponent:M,container:z}=b;let Z=!1;b.isHydrating?b.isHydrating=!1:R||(Z=y&&S.transition&&S.transition.mode==="out-in",Z&&(y.transition.afterLeave=()=>{H===b.pendingId&&(p(S,z,o===C?h(y):o,0),Ur(I))}),y&&(x(y.el)===z&&(o=h(y)),d(y,M,b,!0)),Z||p(S,z,o,0)),Br(b,S),b.pendingBranch=null,b.isInFallback=!1;let J=b.parent,P=!1;for(;J;){if(J.pendingBranch){J.effects.push(...I),P=!0;break}J=J.parent}!P&&!Z&&Ur(I),b.effects=[],A&&t&&t.pendingBranch&&L===t.pendingId&&(t.deps--,t.deps===0&&!T&&t.resolve()),Ui(O,"onResolve")},fallback(R){if(!b.pendingBranch)return;const{vnode:T,activeBranch:O,parentComponent:y,container:S,namespace:H}=b;Ui(T,"onFallback");const I=h(O),M=()=>{b.isInFallback&&(f(null,R,S,I,y,null,H,a,l),Br(b,R))},z=R.transition&&R.transition.mode==="out-in";z&&(O.transition.afterLeave=M),b.isInFallback=!0,d(O,y,null,!0),z||M()},move(R,T,O){b.activeBranch&&p(b.activeBranch,R,T,O),b.container=R},next(){return b.activeBranch&&h(b.activeBranch)},registerDep(R,T,O){const y=!!b.pendingBranch;y&&b.deps++;const S=R.vnode.el;R.asyncDep.catch(H=>{ir(H,R,0)}).then(H=>{if(R.isUnmounted||b.isUnmounted||b.pendingId!==R.suspenseId)return;R.asyncResolved=!0;const{vnode:I}=R;dl(R,H,!1),S&&(I.el=S);const M=!S&&R.subTree.el;T(R,I,x(S||R.subTree.el),S?null:h(R.subTree),b,s,O),M&&j(M),ta(R,I.el),y&&--b.deps===0&&b.resolve()})},unmount(R,T){b.isUnmounted=!0,b.activeBranch&&d(b.activeBranch,n,R,T),b.pendingBranch&&d(b.pendingBranch,n,R,T)}};return b}function $b(e,t,n,r,i,o,s,a,l){const u=t.suspense=om(t,r,n,e.parentNode,document.createElement("div"),null,i,o,s,a,!0),c=l(e,u.pendingBranch=t.ssContent,n,u,o,s);return u.deps===0&&u.resolve(!1,!0),c}function Nb(e){const{shapeFlag:t,children:n}=e,r=t&32;e.ssContent=gc(r?n.default:n),e.ssFallback=r?gc(n.fallback):ee(et)}function gc(e){let t;if(_e(e)){const n=br&&e._c;n&&(e._d=!1,fe()),e=e(),n&&(e._d=!0,t=Et,am())}return ve(e)&&(e=wb(e)),e=Dt(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(n=>n!==e)),e}function sm(e,t){t&&t.pendingBranch?ve(e)?t.effects.push(...e):t.effects.push(e):Ur(e)}function Br(e,t){e.activeBranch=t;const{vnode:n,parentComponent:r}=e;let i=t.el;for(;!i&&t.component;)t=t.component.subTree,i=t.el;n.el=i,r&&r.subTree===n&&(r.vnode.el=i,ta(r,i))}function Mb(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const Se=Symbol.for("v-fgt"),On=Symbol.for("v-txt"),et=Symbol.for("v-cmt"),qn=Symbol.for("v-stc"),ki=[];let Et=null;function fe(e=!1){ki.push(Et=e?null:[])}function am(){ki.pop(),Et=ki[ki.length-1]||null}let br=1;function hs(e,t=!1){br+=e,e<0&&Et&&t&&(Et.hasOnce=!0)}function lm(e){return e.dynamicChildren=br>0?Et||dr:null,am(),br>0&&Et&&Et.push(e),e}function de(e,t,n,r,i,o){return lm(ae(e,t,n,r,i,o,!0))}function Wr(e,t,n,r,i){return lm(ee(e,t,n,r,i,!0))}function pn(e){return e?e.__v_isVNode===!0:!1}function en(e,t){return e.type===t.type&&e.key===t.key}function um(e){}const cm=({key:e})=>e??null,Zo=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?xe(e)||rt(e)||_e(e)?{i:st,r:e,k:t,f:!!n}:e:null);function ae(e,t=null,n=null,r=0,i=null,o=e===Se?0:1,s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&cm(t),ref:t&&Zo(t),scopeId:Xs,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:st};return a?(au(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=xe(n)?8:16),br>0&&!s&&Et&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&Et.push(l),l}const ee=Rb;function Rb(e,t=null,n=null,r=0,i=null,o=!1){if((!e||e===_g)&&(e=et),pn(e)){const a=nn(e,t,!0);return n&&au(a,n),br>0&&!o&&Et&&(a.shapeFlag&6?Et[Et.indexOf(e)]=a:Et.push(a)),a.patchFlag=-2,a}if(Bb(e)&&(e=e.__vccOpts),t){t=su(t);let{class:a,style:l}=t;a&&!xe(a)&&(t.class=tr(a)),Le(l)&&(so(l)&&!ve(l)&&(l=Pe({},l)),t.style=Ve(l))}const s=xe(e)?1:ps(e)?128:rg(e)?64:Le(e)?4:_e(e)?2:0;return ae(e,t,n,r,i,s,o,!0)}function su(e){return e?so(e)||Gg(e)?Pe({},e):e:null}function nn(e,t,n=!1,r=!1){const{props:i,ref:o,patchFlag:s,children:a,transition:l}=e,u=t?lu(i||{},t):i,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&cm(u),ref:t&&t.ref?n&&o?ve(o)?o.concat(Zo(t)):[o,Zo(t)]:Zo(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Se?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&nn(e.ssContent),ssFallback:e.ssFallback&&nn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&dn(c,l.clone(c)),c}function na(e=" ",t=0){return ee(On,null,e,t)}function si(e,t){const n=ee(qn,null,e);return n.staticCount=t,n}function Re(e="",t=!1){return t?(fe(),Wr(et,null,e)):ee(et,null,e)}function Dt(e){return e==null||typeof e=="boolean"?ee(et):ve(e)?ee(Se,null,e.slice()):pn(e)?Hn(e):ee(On,null,String(e))}function Hn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:nn(e)}function au(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(ve(t))n=16;else if(typeof t=="object")if(r&65){const i=t.default;i&&(i._c&&(i._d=!1),au(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!Gg(t)?t._ctx=st:i===3&&st&&(st.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else _e(t)?(t={default:t,_ctx:st},n=32):(t=String(t),r&64?(n=16,t=[na(t)]):n=8);e.children=t,e.shapeFlag|=n}function lu(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const i in r)if(i==="class")t.class!==r.class&&(t.class=tr([t.class,r.class]));else if(i==="style")t.style=Ve([t.style,r.style]);else if(Zn(i)){const o=t[i],s=r[i];s&&o!==s&&!(ve(o)&&o.includes(s))&&(t[i]=o?[].concat(o,s):s)}else i!==""&&(t[i]=r[i])}return t}function Lt(e,t,n,r=null){Ut(e,t,7,[n,r])}const Lb=jg();let Db=0;function fm(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||Lb,o={uid:Db++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ls(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Wg(r,i),emitsOptions:rm(r,i),emit:null,emitted:null,propsDefaults:Oe,inheritAttrs:r.inheritAttrs,ctx:Oe,data:Oe,props:Oe,attrs:Oe,slots:Oe,refs:Oe,setupState:Oe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Sb.bind(null,o),e.ce&&e.ce(o),o}let ot=null;const gt=()=>ot||st;let gs,cl;{const e=ro(),t=(n,r)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(r),o=>{i.length>1?i.forEach(s=>s(o)):i[0](o)}};gs=t("__VUE_INSTANCE_SETTERS__",n=>ot=n),cl=t("__VUE_SSR_SETTERS__",n=>Xr=n)}const _r=e=>{const t=ot;return gs(e),e.scope.on(),()=>{e.scope.off(),gs(t)}},fl=()=>{ot&&ot.scope.off(),gs(null)};function dm(e){return e.vnode.shapeFlag&4}let Xr=!1;function pm(e,t=!1,n=!1){t&&cl(t);const{props:r,children:i}=e.vnode,o=dm(e);mb(e,r,o,t),_b(e,i,n);const s=o?Fb(e,t):void 0;return t&&cl(!1),s}function Fb(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,ol);const{setup:r}=n;if(r){nr();const i=e.setupContext=r.length>1?vm(e):null,o=_r(e),s=Tr(r,e,0,[e.props,i]),a=Ns(s);if(rr(),o(),(a||e.sp)&&!Kn(e)&&Vl(e),a){if(s.then(fl,fl),t)return s.then(l=>{dl(e,l,t)}).catch(l=>{ir(l,e,0)});e.asyncDep=s}else dl(e,s,t)}else mm(e,t)}function dl(e,t,n){_e(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Le(t)&&(e.setupState=Us(t)),mm(e,n)}let ms,pl;function hm(e){ms=e,pl=t=>{t.render._rc&&(t.withProxy=new Proxy(t.ctx,lb))}}const gm=()=>!ms;function mm(e,t,n){const r=e.type;if(!e.render){if(!t&&ms&&!r.render){const i=r.template||Ql(e).template;if(i){const{isCustomElement:o,compilerOptions:s}=e.appContext.config,{delimiters:a,compilerOptions:l}=r,u=Pe(Pe({isCustomElement:o,delimiters:a},s),l);r.render=ms(i,u)}}e.render=r.render||ft,pl&&pl(e)}{const i=_r(e);nr();try{ub(e)}finally{rr(),i()}}}const jb={get(e,t){return _t(e,"get",""),e[t]}};function vm(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,jb),slots:e.slots,emit:e.emit,expose:t}}function po(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Us(Gs(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ii)return Ii[n](e)},has(t,n){return n in t||n in Ii}})):e.proxy}function hl(e,t=!0){return _e(e)?e.displayName||e.name:e.name||t&&e.__name}function Bb(e){return _e(e)&&"__vccOpts"in e}const Ce=(e,t)=>Hy(e,t,Xr);function ai(e,t,n){const r=arguments.length;return r===2?Le(t)&&!ve(t)?pn(t)?ee(e,null,[t]):ee(e,t):ee(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&pn(n)&&(n=[n]),ee(e,t,n))}function ym(){}function bm(e,t,n,r){const i=n[r];if(i&&uu(i,e))return i;const o=t();return o.memo=e.slice(),o.cacheIndex=r,n[r]=o}function uu(e,t){const n=e.memo;if(n.length!=t.length)return!1;for(let r=0;r<n.length;r++)if(bt(n[r],t[r]))return!1;return br>0&&Et&&Et.push(e),!0}const cu="3.5.13",_m=ft,Cm=Xy,xm=Mr,Em=Qh,Vb={createComponentInstance:fm,setupComponent:pm,renderComponentRoot:Yo,setCurrentRenderingInstance:Vi,isVNode:pn,normalizeVNode:Dt,getComponentPublicInstance:po,ensureValidVNode:Jl,pushWarningContext:Uy,popWarningContext:Wy},Sm=Vb,wm=null,Tm=null,Om=null;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let gl;const mc=typeof window<"u"&&window.trustedTypes;if(mc)try{gl=mc.createPolicy("vue",{createHTML:e=>e})}catch{}const Pm=gl?e=>gl.createHTML(e):e=>e,Hb="http://www.w3.org/2000/svg",Gb="http://www.w3.org/1998/Math/MathML",_n=typeof document<"u"?document:null,vc=_n&&_n.createElement("template"),Ub={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const i=t==="svg"?_n.createElementNS(Hb,e):t==="mathml"?_n.createElementNS(Gb,e):n?_n.createElement(e,{is:n}):_n.createElement(e);return e==="select"&&r&&r.multiple!=null&&i.setAttribute("multiple",r.multiple),i},createText:e=>_n.createTextNode(e),createComment:e=>_n.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>_n.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,i,o){const s=n?n.previousSibling:t.lastChild;if(i&&(i===o||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===o||!(i=i.nextSibling)););else{vc.innerHTML=Pm(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=vc.content;if(r==="svg"||r==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Rn="transition",fi="animation",Kr=Symbol("_vtc"),Am={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Im=Pe({},zs,Am),Wb=e=>(e.displayName="Transition",e.props=Im,e),km=Wb((e,{slots:t})=>ai(Bl,$m(e),t)),lr=(e,t=[])=>{ve(e)?e.forEach(n=>n(...t)):e&&e(...t)},yc=e=>e?ve(e)?e.some(t=>t.length>1):e.length>1:!1;function $m(e){const t={};for(const I in e)I in Am||(t[I]=e[I]);if(e.css===!1)return t;const{name:n="v",type:r,duration:i,enterFromClass:o=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=o,appearActiveClass:u=s,appearToClass:c=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:d=`${n}-leave-to`}=e,h=Xb(i),x=h&&h[0],j=h&&h[1],{onBeforeEnter:L,onEnter:A,onEnterCancelled:g,onLeave:C,onLeaveCancelled:b,onBeforeAppear:R=L,onAppear:T=A,onAppearCancelled:O=g}=t,y=(I,M,z,Z)=>{I._enterCancelled=Z,Fn(I,M?c:a),Fn(I,M?u:s),z&&z()},S=(I,M)=>{I._isLeaving=!1,Fn(I,f),Fn(I,d),Fn(I,p),M&&M()},H=I=>(M,z)=>{const Z=I?T:A,J=()=>y(M,I,z);lr(Z,[M,J]),bc(()=>{Fn(M,I?l:o),ln(M,I?c:a),yc(Z)||_c(M,r,x,J)})};return Pe(t,{onBeforeEnter(I){lr(L,[I]),ln(I,o),ln(I,s)},onBeforeAppear(I){lr(R,[I]),ln(I,l),ln(I,u)},onEnter:H(!1),onAppear:H(!0),onLeave(I,M){I._isLeaving=!0;const z=()=>S(I,M);ln(I,f),I._enterCancelled?(ln(I,p),ml()):(ml(),ln(I,p)),bc(()=>{I._isLeaving&&(Fn(I,f),ln(I,d),yc(C)||_c(I,r,j,z))}),lr(C,[I,z])},onEnterCancelled(I){y(I,!1,void 0,!0),lr(g,[I])},onAppearCancelled(I){y(I,!0,void 0,!0),lr(O,[I])},onLeaveCancelled(I){S(I),lr(b,[I])}})}function Xb(e){if(e==null)return null;if(Le(e))return[Ba(e.enter),Ba(e.leave)];{const t=Ba(e);return[t,t]}}function Ba(e){return Di(e)}function ln(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Kr]||(e[Kr]=new Set)).add(t)}function Fn(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[Kr];n&&(n.delete(t),n.size||(e[Kr]=void 0))}function bc(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Kb=0;function _c(e,t,n,r){const i=e._endId=++Kb,o=()=>{i===e._endId&&r()};if(n!=null)return setTimeout(o,n);const{type:s,timeout:a,propCount:l}=Nm(e,t);if(!s)return r();const u=s+"end";let c=0;const f=()=>{e.removeEventListener(u,p),o()},p=d=>{d.target===e&&++c>=l&&f()};setTimeout(()=>{c<l&&f()},a+1),e.addEventListener(u,p)}function Nm(e,t){const n=window.getComputedStyle(e),r=h=>(n[h]||"").split(", "),i=r(`${Rn}Delay`),o=r(`${Rn}Duration`),s=Cc(i,o),a=r(`${fi}Delay`),l=r(`${fi}Duration`),u=Cc(a,l);let c=null,f=0,p=0;t===Rn?s>0&&(c=Rn,f=s,p=o.length):t===fi?u>0&&(c=fi,f=u,p=l.length):(f=Math.max(s,u),c=f>0?s>u?Rn:fi:null,p=c?c===Rn?o.length:l.length:0);const d=c===Rn&&/\b(transform|all)(,|$)/.test(r(`${Rn}Property`).toString());return{type:c,timeout:f,propCount:p,hasTransform:d}}function Cc(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>xc(n)+xc(e[r])))}function xc(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function ml(){return document.body.offsetHeight}function qb(e,t,n){const r=e[Kr];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const vs=Symbol("_vod"),Mm=Symbol("_vsh"),fu={beforeMount(e,{value:t},{transition:n}){e[vs]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):di(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),di(e,!0),r.enter(e)):r.leave(e,()=>{di(e,!1)}):di(e,t))},beforeUnmount(e,{value:t}){di(e,t)}};function di(e,t){e.style.display=t?e[vs]:"none",e[Mm]=!t}function zb(){fu.getSSRProps=({value:e})=>{if(!e)return{style:{display:"none"}}}}const Rm=Symbol("");function Or(e){const t=gt();if(!t)return;const n=t.ut=(i=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach(o=>ys(o,i))},r=()=>{const i=e(t.proxy);t.ce?ys(t.ce,i):vl(t.subTree,i),n(i)};Zs(()=>{Ur(r)}),Ue(()=>{we(r,ft,{flush:"post"});const i=new MutationObserver(r);i.observe(t.subTree.el.parentNode,{childList:!0}),Mn(()=>i.disconnect())})}function vl(e,t){if(e.shapeFlag&128){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{vl(n.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(e.shapeFlag&1&&e.el)ys(e.el,t);else if(e.type===Se)e.children.forEach(n=>vl(n,t));else if(e.type===qn){let{el:n,anchor:r}=e;for(;n&&(ys(n,t),n!==r);)n=n.nextSibling}}function ys(e,t){if(e.nodeType===1){const n=e.style;let r="";for(const i in t)n.setProperty(`--${i}`,t[i]),r+=`--${i}: ${t[i]};`;n[Rm]=r}}const Jb=/(^|;)\s*display\s*:/;function Qb(e,t,n){const r=e.style,i=xe(n);let o=!1;if(n&&!i){if(t)if(xe(t))for(const s of t.split(";")){const a=s.slice(0,s.indexOf(":")).trim();n[a]==null&&es(r,a,"")}else for(const s in t)n[s]==null&&es(r,s,"");for(const s in n)s==="display"&&(o=!0),es(r,s,n[s])}else if(i){if(t!==n){const s=r[Rm];s&&(n+=";"+s),r.cssText=n,o=Jb.test(n)}}else t&&e.removeAttribute("style");vs in e&&(e[vs]=o?r.display:"",e[Mm]&&(r.display="none"))}const Ec=/\s*!important$/;function es(e,t,n){if(ve(n))n.forEach(r=>es(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Yb(e,t);Ec.test(n)?e.setProperty(xt(r),n.replace(Ec,""),"important"):e[r]=n}}const Sc=["Webkit","Moz","ms"],Va={};function Yb(e,t){const n=Va[t];if(n)return n;let r=He(t);if(r!=="filter"&&r in e)return Va[t]=r;r=$n(r);for(let i=0;i<Sc.length;i++){const o=Sc[i]+r;if(o in e)return Va[t]=o}return t}const wc="http://www.w3.org/1999/xlink";function Tc(e,t,n,r,i,o=hh(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(wc,t.slice(6,t.length)):e.setAttributeNS(wc,t,n):n==null||o&&!Ml(n)?e.removeAttribute(t):e.setAttribute(t,o?"":kt(n)?String(n):n)}function Oc(e,t,n,r,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Pm(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let s=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=Ml(n):n==null&&a==="string"?(n="",s=!0):a==="number"&&(n=0,s=!0)}try{e[t]=n}catch{}s&&e.removeAttribute(i||t)}function En(e,t,n,r){e.addEventListener(t,n,r)}function Zb(e,t,n,r){e.removeEventListener(t,n,r)}const Pc=Symbol("_vei");function e1(e,t,n,r,i=null){const o=e[Pc]||(e[Pc]={}),s=o[t];if(r&&s)s.value=r;else{const[a,l]=t1(t);if(r){const u=o[t]=i1(r,i);En(e,a,u,l)}else s&&(Zb(e,a,s,l),o[t]=void 0)}}const Ac=/(?:Once|Passive|Capture)$/;function t1(e){let t;if(Ac.test(e)){t={};let r;for(;r=e.match(Ac);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):xt(e.slice(2)),t]}let Ha=0;const n1=Promise.resolve(),r1=()=>Ha||(n1.then(()=>Ha=0),Ha=Date.now());function i1(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Ut(o1(r,n.value),t,5,[r])};return n.value=e,n.attached=r1(),n}function o1(e,t){if(ve(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>i=>!i._stopped&&r&&r(i))}else return t}const Ic=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,s1=(e,t,n,r,i,o)=>{const s=i==="svg";t==="class"?qb(e,r,s):t==="style"?Qb(e,n,r):Zn(t)?ks(t)||e1(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):a1(e,t,r,s))?(Oc(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Tc(e,t,r,s,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!xe(r))?Oc(e,He(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Tc(e,t,r,s))};function a1(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ic(t)&&_e(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Ic(t)&&xe(n)?!1:t in e}const kc={};/*! #__NO_SIDE_EFFECTS__ */function du(e,t,n){const r=Ne(e,t);no(r)&&Pe(r,t);class i extends ho{constructor(s){super(r,s,n)}}return i.def=r,i}/*! #__NO_SIDE_EFFECTS__ */const Lm=(e,t)=>du(e,t,vu),l1=typeof HTMLElement<"u"?HTMLElement:class{};class ho extends l1{constructor(t,n={},r=_s){super(),this._def=t,this._props=n,this._createApp=r,this._isVueCE=!0,this._instance=null,this._app=null,this._nonce=this._def.nonce,this._connected=!1,this._resolved=!1,this._numberProps=null,this._styleChildren=new WeakSet,this._ob=null,this.shadowRoot&&r!==_s?this._root=this.shadowRoot:t.shadowRoot!==!1?(this.attachShadow({mode:"open"}),this._root=this.shadowRoot):this._root=this,this._def.__asyncLoader||this._resolveProps(this._def)}connectedCallback(){if(!this.isConnected)return;this.shadowRoot||this._parseSlots(),this._connected=!0;let t=this;for(;t=t&&(t.parentNode||t.host);)if(t instanceof ho){this._parent=t;break}this._instance||(this._resolved?(this._setParent(),this._update()):t&&t._pendingResolve?this._pendingResolve=t._pendingResolve.then(()=>{this._pendingResolve=void 0,this._resolveDef()}):this._resolveDef())}_setParent(t=this._parent){t&&(this._instance.parent=t._instance,this._instance.provides=t._instance.provides)}disconnectedCallback(){this._connected=!1,on(()=>{this._connected||(this._ob&&(this._ob.disconnect(),this._ob=null),this._app&&this._app.unmount(),this._instance&&(this._instance.ce=void 0),this._app=this._instance=null)})}_resolveDef(){if(this._pendingResolve)return;for(let r=0;r<this.attributes.length;r++)this._setAttr(this.attributes[r].name);this._ob=new MutationObserver(r=>{for(const i of r)this._setAttr(i.attributeName)}),this._ob.observe(this,{attributes:!0});const t=(r,i=!1)=>{this._resolved=!0,this._pendingResolve=void 0;const{props:o,styles:s}=r;let a;if(o&&!ve(o))for(const l in o){const u=o[l];(u===Number||u&&u.type===Number)&&(l in this._props&&(this._props[l]=Di(this._props[l])),(a||(a=Object.create(null)))[He(l)]=!0)}this._numberProps=a,i&&this._resolveProps(r),this.shadowRoot&&this._applyStyles(s),this._mount(r)},n=this._def.__asyncLoader;n?this._pendingResolve=n().then(r=>t(this._def=r,!0)):t(this._def)}_mount(t){this._app=this._createApp(t),t.configureApp&&t.configureApp(this._app),this._app._ceVNode=this._createVNode(),this._app.mount(this._root);const n=this._instance&&this._instance.exposed;if(n)for(const r in n)$e(this,r)||Object.defineProperty(this,r,{get:()=>v(n[r])})}_resolveProps(t){const{props:n}=t,r=ve(n)?n:Object.keys(n||{});for(const i of Object.keys(this))i[0]!=="_"&&r.includes(i)&&this._setProp(i,this[i]);for(const i of r.map(He))Object.defineProperty(this,i,{get(){return this._getProp(i)},set(o){this._setProp(i,o,!0,!0)}})}_setAttr(t){if(t.startsWith("data-v-"))return;const n=this.hasAttribute(t);let r=n?this.getAttribute(t):kc;const i=He(t);n&&this._numberProps&&this._numberProps[i]&&(r=Di(r)),this._setProp(i,r,!1,!0)}_getProp(t){return this._props[t]}_setProp(t,n,r=!0,i=!1){if(n!==this._props[t]&&(n===kc?delete this._props[t]:(this._props[t]=n,t==="key"&&this._app&&(this._app._ceVNode.key=n)),i&&this._instance&&this._update(),r)){const o=this._ob;o&&o.disconnect(),n===!0?this.setAttribute(xt(t),""):typeof n=="string"||typeof n=="number"?this.setAttribute(xt(t),n+""):n||this.removeAttribute(xt(t)),o&&o.observe(this,{attributes:!0})}}_update(){mu(this._createVNode(),this._root)}_createVNode(){const t={};this.shadowRoot||(t.onVnodeMounted=t.onVnodeUpdated=this._renderSlots.bind(this));const n=ee(this._def,Pe(t,this._props));return this._instance||(n.ce=r=>{this._instance=r,r.ce=this,r.isCE=!0;const i=(o,s)=>{this.dispatchEvent(new CustomEvent(o,no(s[0])?Pe({detail:s},s[0]):{detail:s}))};r.emit=(o,...s)=>{i(o,s),xt(o)!==o&&i(xt(o),s)},this._setParent()}),n}_applyStyles(t,n){if(!t)return;if(n){if(n===this._def||this._styleChildren.has(n))return;this._styleChildren.add(n)}const r=this._nonce;for(let i=t.length-1;i>=0;i--){const o=document.createElement("style");r&&o.setAttribute("nonce",r),o.textContent=t[i],this.shadowRoot.prepend(o)}}_parseSlots(){const t=this._slots={};let n;for(;n=this.firstChild;){const r=n.nodeType===1&&n.getAttribute("slot")||"default";(t[r]||(t[r]=[])).push(n),this.removeChild(n)}}_renderSlots(){const t=(this._teleportTarget||this).querySelectorAll("slot"),n=this._instance.type.__scopeId;for(let r=0;r<t.length;r++){const i=t[r],o=i.getAttribute("name")||"default",s=this._slots[o],a=i.parentNode;if(s)for(const l of s){if(n&&l.nodeType===1){const u=n+"-s",c=document.createTreeWalker(l,1);l.setAttribute(u,"");let f;for(;f=c.nextNode();)f.setAttribute(u,"")}a.insertBefore(l,i)}else for(;i.firstChild;)a.insertBefore(i.firstChild,i);a.removeChild(i)}}_injectChildStyle(t){this._applyStyles(t.styles,t)}_removeChildStyle(t){}}function pu(e){const t=gt(),n=t&&t.ce;return n||null}function Dm(){const e=pu();return e&&e.shadowRoot}function Fm(e="$style"){{const t=gt();if(!t)return Oe;const n=t.type.__cssModules;if(!n)return Oe;const r=n[e];return r||Oe}}const jm=new WeakMap,Bm=new WeakMap,bs=Symbol("_moveCb"),$c=Symbol("_enterCb"),u1=e=>(delete e.props.mode,e),c1=u1({name:"TransitionGroup",props:Pe({},Im,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=gt(),r=qs();let i,o;return co(()=>{if(!i.length)return;const s=e.moveClass||`${e.name||"v"}-move`;if(!h1(i[0].el,n.vnode.el,s))return;i.forEach(f1),i.forEach(d1);const a=i.filter(p1);ml(),a.forEach(l=>{const u=l.el,c=u.style;ln(u,s),c.transform=c.webkitTransform=c.transitionDuration="";const f=u[bs]=p=>{p&&p.target!==u||(!p||/transform$/.test(p.propertyName))&&(u.removeEventListener("transitionend",f),u[bs]=null,Fn(u,s))};u.addEventListener("transitionend",f)})}),()=>{const s=Ae(e),a=$m(s);let l=s.tag||Se;if(i=[],o)for(let u=0;u<o.length;u++){const c=o[u];c.el&&c.el instanceof Element&&(i.push(c),dn(c,yr(c,a,r,n)),jm.set(c,c.el.getBoundingClientRect()))}o=t.default?lo(t.default()):[];for(let u=0;u<o.length;u++){const c=o[u];c.key!=null&&dn(c,yr(c,a,r,n))}return ee(l,null,o)}}}),Vm=c1;function f1(e){const t=e.el;t[bs]&&t[bs](),t[$c]&&t[$c]()}function d1(e){Bm.set(e,e.el.getBoundingClientRect())}function p1(e){const t=jm.get(e),n=Bm.get(e),r=t.left-n.left,i=t.top-n.top;if(r||i){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${r}px,${i}px)`,o.transitionDuration="0s",e}}function h1(e,t,n){const r=e.cloneNode(),i=e[Kr];i&&i.forEach(a=>{a.split(/\s+/).forEach(l=>l&&r.classList.remove(l))}),n.split(/\s+/).forEach(a=>a&&r.classList.add(a)),r.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(r);const{hasTransform:s}=Nm(r);return o.removeChild(r),s}const Jn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return ve(t)?n=>hr(t,n):t};function g1(e){e.target.composing=!0}function Nc(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Jt=Symbol("_assign"),Wi={created(e,{modifiers:{lazy:t,trim:n,number:r}},i){e[Jt]=Jn(i);const o=r||i.props&&i.props.type==="number";En(e,t?"change":"input",s=>{if(s.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=Li(a)),e[Jt](a)}),n&&En(e,"change",()=>{e.value=e.value.trim()}),t||(En(e,"compositionstart",g1),En(e,"compositionend",Nc),En(e,"change",Nc))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:i,number:o}},s){if(e[Jt]=Jn(s),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?Li(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||i&&e.value.trim()===l)||(e.value=l))}},ra={deep:!0,created(e,t,n){e[Jt]=Jn(n),En(e,"change",()=>{const r=e._modelValue,i=qr(e),o=e.checked,s=e[Jt];if(ve(r)){const a=io(r,i),l=a!==-1;if(o&&!l)s(r.concat(i));else if(!o&&l){const u=[...r];u.splice(a,1),s(u)}}else if(er(r)){const a=new Set(r);o?a.add(i):a.delete(i),s(a)}else s(Hm(e,o))})},mounted:Mc,beforeUpdate(e,t,n){e[Jt]=Jn(n),Mc(e,t,n)}};function Mc(e,{value:t,oldValue:n},r){e._modelValue=t;let i;if(ve(t))i=io(t,r.props.value)>-1;else if(er(t))i=t.has(r.props.value);else{if(t===n)return;i=In(t,Hm(e,!0))}e.checked!==i&&(e.checked=i)}const ia={created(e,{value:t},n){e.checked=In(t,n.props.value),e[Jt]=Jn(n),En(e,"change",()=>{e[Jt](qr(e))})},beforeUpdate(e,{value:t,oldValue:n},r){e[Jt]=Jn(r),t!==n&&(e.checked=In(t,r.props.value))}},hu={deep:!0,created(e,{value:t,modifiers:{number:n}},r){const i=er(t);En(e,"change",()=>{const o=Array.prototype.filter.call(e.options,s=>s.selected).map(s=>n?Li(qr(s)):qr(s));e[Jt](e.multiple?i?new Set(o):o:o[0]),e._assigning=!0,on(()=>{e._assigning=!1})}),e[Jt]=Jn(r)},mounted(e,{value:t}){Rc(e,t)},beforeUpdate(e,t,n){e[Jt]=Jn(n)},updated(e,{value:t}){e._assigning||Rc(e,t)}};function Rc(e,t){const n=e.multiple,r=ve(t);if(!(n&&!r&&!er(t))){for(let i=0,o=e.options.length;i<o;i++){const s=e.options[i],a=qr(s);if(n)if(r){const l=typeof a;l==="string"||l==="number"?s.selected=t.some(u=>String(u)===String(a)):s.selected=io(t,a)>-1}else s.selected=t.has(a);else if(In(qr(s),t)){e.selectedIndex!==i&&(e.selectedIndex=i);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function qr(e){return"_value"in e?e._value:e.value}function Hm(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const gu={created(e,t,n){Ro(e,t,n,null,"created")},mounted(e,t,n){Ro(e,t,n,null,"mounted")},beforeUpdate(e,t,n,r){Ro(e,t,n,r,"beforeUpdate")},updated(e,t,n,r){Ro(e,t,n,r,"updated")}};function Gm(e,t){switch(e){case"SELECT":return hu;case"TEXTAREA":return Wi;default:switch(t){case"checkbox":return ra;case"radio":return ia;default:return Wi}}}function Ro(e,t,n,r,i){const s=Gm(e.tagName,n.props&&n.props.type)[i];s&&s(e,t,n,r)}function m1(){Wi.getSSRProps=({value:e})=>({value:e}),ia.getSSRProps=({value:e},t)=>{if(t.props&&In(t.props.value,e))return{checked:!0}},ra.getSSRProps=({value:e},t)=>{if(ve(e)){if(t.props&&io(e,t.props.value)>-1)return{checked:!0}}else if(er(e)){if(t.props&&e.has(t.props.value))return{checked:!0}}else if(e)return{checked:!0}},gu.getSSRProps=(e,t)=>{if(typeof t.type!="string")return;const n=Gm(t.type.toUpperCase(),t.props&&t.props.type);if(n.getSSRProps)return n.getSSRProps(e,t)}}const v1=["ctrl","shift","alt","meta"],y1={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>v1.some(n=>e[`${n}Key`]&&!t.includes(n))},Um=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(i,...o)=>{for(let s=0;s<t.length;s++){const a=y1[t[s]];if(a&&a(i,t))return}return e(i,...o)})},b1={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Wm=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=i=>{if(!("key"in i))return;const o=xt(i.key);if(t.some(s=>s===o||b1[s]===o))return e(i)})},Xm=Pe({patchProp:s1},Ub);let $i,Lc=!1;function Km(){return $i||($i=Zl(Xm))}function qm(){return $i=Lc?$i:eu(Xm),Lc=!0,$i}const mu=(...e)=>{Km().render(...e)},zm=(...e)=>{qm().hydrate(...e)},_s=(...e)=>{const t=Km().createApp(...e),{mount:n}=t;return t.mount=r=>{const i=Qm(r);if(!i)return;const o=t._component;!_e(o)&&!o.render&&!o.template&&(o.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const s=n(i,!1,Jm(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),s},t},vu=(...e)=>{const t=qm().createApp(...e),{mount:n}=t;return t.mount=r=>{const i=Qm(r);if(i)return n(i,!0,Jm(i))},t};function Jm(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Qm(e){return xe(e)?document.querySelector(e):e}let Dc=!1;const Ym=()=>{Dc||(Dc=!0,m1(),zb())},_1=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:Bl,BaseTransitionPropsValidators:zs,Comment:et,DeprecationTypes:Om,EffectScope:Ls,ErrorCodes:Kh,ErrorTypeStrings:Cm,Fragment:Se,KeepAlive:vg,ReactiveEffect:Gr,Static:qn,Suspense:im,Teleport:og,Text:On,TrackOpTypes:Hh,Transition:km,TransitionGroup:Vm,TriggerOpTypes:Gh,VueElement:ho,assertNumber:Xh,callWithAsyncErrorHandling:Ut,callWithErrorHandling:Tr,camelize:He,capitalize:$n,cloneVNode:nn,compatUtils:Tm,computed:Ce,createApp:_s,createBlock:Wr,createCommentVNode:Re,createElementBlock:de,createElementVNode:ae,createHydrationRenderer:eu,createPropsRestProxy:Lg,createRenderer:Zl,createSSRApp:vu,createSlots:xg,createStaticVNode:si,createTextVNode:na,createVNode:ee,customRef:Ws,defineAsyncComponent:mg,defineComponent:Ne,defineCustomElement:du,defineEmits:wg,defineExpose:Tg,defineModel:Ag,defineOptions:Og,defineProps:Sg,defineSSRCustomElement:Lm,defineSlots:Pg,devtools:xm,effect:Sh,effectScope:yh,getCurrentInstance:gt,getCurrentScope:oo,getCurrentWatcher:Uh,getTransitionRawChildren:lo,guardReactiveProps:su,h:ai,handleError:ir,hasInjectionContext:Bg,hydrate:zm,hydrateOnIdle:dg,hydrateOnInteraction:gg,hydrateOnMediaQuery:hg,hydrateOnVisible:pg,initCustomFormatter:ym,initDirectivesForSSR:Ym,inject:zt,isMemoSame:uu,isProxy:so,isReactive:Tn,isReadonly:kn,isRef:rt,isRuntimeOnly:gm,isShallow:jt,isVNode:pn,markRaw:Gs,mergeDefaults:Mg,mergeModels:Rg,mergeProps:lu,nextTick:on,normalizeClass:tr,normalizeProps:Nl,normalizeStyle:Ve,onActivated:Js,onBeforeMount:Hl,onBeforeUnmount:oi,onBeforeUpdate:Zs,onDeactivated:Qs,onErrorCaptured:Xl,onMounted:Ue,onRenderTracked:Wl,onRenderTriggered:Ul,onScopeDispose:Ds,onServerPrefetch:Gl,onUnmounted:Mn,onUpdated:co,onWatcherCleanup:Fl,openBlock:fe,popScopeId:Zh,provide:jr,proxyRefs:Us,pushScopeId:Yh,queuePostFlushCb:Ur,reactive:ke,readonly:ii,ref:ye,registerRuntimeCompiler:hm,render:mu,renderList:qe,renderSlot:ze,resolveComponent:bg,resolveDirective:Cg,resolveDynamicComponent:ql,resolveFilter:wm,resolveTransitionHooks:yr,setBlockTracking:hs,setDevtoolsHook:Em,setTransitionHooks:dn,shallowReactive:Vs,shallowReadonly:Lh,shallowRef:ao,ssrContextKey:nu,ssrUtils:Sm,stop:wh,toDisplayString:ct,toHandlerKey:Xn,toHandlers:Eg,toRaw:Ae,toRef:$t,toRefs:Bh,toValue:jh,transformVNodeArgs:um,triggerRef:Fh,unref:v,useAttrs:$g,useCssModule:Fm,useCssVars:Or,useHost:pu,useId:cg,useModel:tm,useSSRContext:ru,useShadowRoot:Dm,useSlots:kg,useTemplateRef:fg,useTransitionState:qs,vModelCheckbox:ra,vModelDynamic:gu,vModelRadio:ia,vModelSelect:hu,vModelText:Wi,vShow:fu,version:cu,warn:_m,watch:we,watchEffect:iu,watchPostEffect:Zg,watchSyncEffect:ou,withAsyncContext:Dg,withCtx:Ks,withDefaults:Ig,withDirectives:tg,withKeys:Wm,withMemo:bm,withModifiers:Um,withScopeId:eg},Symbol.toStringTag,{value:"Module"}));/**
* vue v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const pP=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:Bl,BaseTransitionPropsValidators:zs,Comment:et,DeprecationTypes:Om,EffectScope:Ls,ErrorCodes:Kh,ErrorTypeStrings:Cm,Fragment:Se,KeepAlive:vg,ReactiveEffect:Gr,Static:qn,Suspense:im,Teleport:og,Text:On,TrackOpTypes:Hh,Transition:km,TransitionGroup:Vm,TriggerOpTypes:Gh,VueElement:ho,assertNumber:Xh,callWithAsyncErrorHandling:Ut,callWithErrorHandling:Tr,camelize:He,capitalize:$n,cloneVNode:nn,compatUtils:Tm,computed:Ce,createApp:_s,createBlock:Wr,createCommentVNode:Re,createElementBlock:de,createElementVNode:ae,createHydrationRenderer:eu,createPropsRestProxy:Lg,createRenderer:Zl,createSSRApp:vu,createSlots:xg,createStaticVNode:si,createTextVNode:na,createVNode:ee,customRef:Ws,defineAsyncComponent:mg,defineComponent:Ne,defineCustomElement:du,defineEmits:wg,defineExpose:Tg,defineModel:Ag,defineOptions:Og,defineProps:Sg,defineSSRCustomElement:Lm,defineSlots:Pg,devtools:xm,effect:Sh,effectScope:yh,getCurrentInstance:gt,getCurrentScope:oo,getCurrentWatcher:Uh,getTransitionRawChildren:lo,guardReactiveProps:su,h:ai,handleError:ir,hasInjectionContext:Bg,hydrate:zm,hydrateOnIdle:dg,hydrateOnInteraction:gg,hydrateOnMediaQuery:hg,hydrateOnVisible:pg,initCustomFormatter:ym,initDirectivesForSSR:Ym,inject:zt,isMemoSame:uu,isProxy:so,isReactive:Tn,isReadonly:kn,isRef:rt,isRuntimeOnly:gm,isShallow:jt,isVNode:pn,markRaw:Gs,mergeDefaults:Mg,mergeModels:Rg,mergeProps:lu,nextTick:on,normalizeClass:tr,normalizeProps:Nl,normalizeStyle:Ve,onActivated:Js,onBeforeMount:Hl,onBeforeUnmount:oi,onBeforeUpdate:Zs,onDeactivated:Qs,onErrorCaptured:Xl,onMounted:Ue,onRenderTracked:Wl,onRenderTriggered:Ul,onScopeDispose:Ds,onServerPrefetch:Gl,onUnmounted:Mn,onUpdated:co,onWatcherCleanup:Fl,openBlock:fe,popScopeId:Zh,provide:jr,proxyRefs:Us,pushScopeId:Yh,queuePostFlushCb:Ur,reactive:ke,readonly:ii,ref:ye,registerRuntimeCompiler:hm,render:mu,renderList:qe,renderSlot:ze,resolveComponent:bg,resolveDirective:Cg,resolveDynamicComponent:ql,resolveFilter:wm,resolveTransitionHooks:yr,setBlockTracking:hs,setDevtoolsHook:Em,setTransitionHooks:dn,shallowReactive:Vs,shallowReadonly:Lh,shallowRef:ao,ssrContextKey:nu,ssrUtils:Sm,stop:wh,toDisplayString:ct,toHandlerKey:Xn,toHandlers:Eg,toRaw:Ae,toRef:$t,toRefs:Bh,toValue:jh,transformVNodeArgs:um,triggerRef:Fh,unref:v,useAttrs:$g,useCssModule:Fm,useCssVars:Or,useHost:pu,useId:cg,useModel:tm,useSSRContext:ru,useShadowRoot:Dm,useSlots:kg,useTemplateRef:fg,useTransitionState:qs,vModelCheckbox:ra,vModelDynamic:gu,vModelRadio:ia,vModelSelect:hu,vModelText:Wi,vShow:fu,version:cu,warn:_m,watch:we,watchEffect:iu,watchPostEffect:Zg,watchSyncEffect:ou,withAsyncContext:Dg,withCtx:Ks,withDefaults:Ig,withDirectives:tg,withKeys:Wm,withMemo:bm,withModifiers:Um,withScopeId:eg},Symbol.toStringTag,{value:"Module"}));/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Rr=typeof document<"u";function Zm(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function C1(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Zm(e.default)}const Fe=Object.assign;function Ga(e,t){const n={};for(const r in t){const i=t[r];n[r]=rn(i)?i.map(e):e(i)}return n}const Ni=()=>{},rn=Array.isArray,e0=/#/g,x1=/&/g,E1=/\//g,S1=/=/g,w1=/\?/g,t0=/\+/g,T1=/%5B/g,O1=/%5D/g,n0=/%5E/g,P1=/%60/g,r0=/%7B/g,A1=/%7C/g,i0=/%7D/g,I1=/%20/g;function yu(e){return encodeURI(""+e).replace(A1,"|").replace(T1,"[").replace(O1,"]")}function k1(e){return yu(e).replace(r0,"{").replace(i0,"}").replace(n0,"^")}function yl(e){return yu(e).replace(t0,"%2B").replace(I1,"+").replace(e0,"%23").replace(x1,"%26").replace(P1,"`").replace(r0,"{").replace(i0,"}").replace(n0,"^")}function $1(e){return yl(e).replace(S1,"%3D")}function N1(e){return yu(e).replace(e0,"%23").replace(w1,"%3F")}function M1(e){return e==null?"":N1(e).replace(E1,"%2F")}function Xi(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const R1=/\/$/,L1=e=>e.replace(R1,"");function Ua(e,t,n="/"){let r,i={},o="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),i=e(o)),a>-1&&(r=r||t.slice(0,a),s=t.slice(a,t.length)),r=B1(r??t,n),{fullPath:r+(o&&"?")+o+s,path:r,query:i,hash:Xi(s)}}function D1(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Fc(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function F1(e,t,n){const r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&zr(t.matched[r],n.matched[i])&&o0(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function zr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function o0(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!j1(e[n],t[n]))return!1;return!0}function j1(e,t){return rn(e)?jc(e,t):rn(t)?jc(t,e):e===t}function jc(e,t){return rn(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function B1(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),i=r[r.length-1];(i===".."||i===".")&&r.push("");let o=n.length-1,s,a;for(s=0;s<r.length;s++)if(a=r[s],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(s).join("/")}const Ln={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ki;(function(e){e.pop="pop",e.push="push"})(Ki||(Ki={}));var Mi;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Mi||(Mi={}));function V1(e){if(!e)if(Rr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),L1(e)}const H1=/^[^#]+#/;function G1(e,t){return e.replace(H1,"#")+t}function U1(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const oa=()=>({left:window.scrollX,top:window.scrollY});function W1(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=U1(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Bc(e,t){return(history.state?history.state.position-t:-1)+e}const bl=new Map;function X1(e,t){bl.set(e,t)}function K1(e){const t=bl.get(e);return bl.delete(e),t}let q1=()=>location.protocol+"//"+location.host;function s0(e,t){const{pathname:n,search:r,hash:i}=t,o=e.indexOf("#");if(o>-1){let a=i.includes(e.slice(o))?e.slice(o).length:1,l=i.slice(a);return l[0]!=="/"&&(l="/"+l),Fc(l,"")}return Fc(n,e)+r+i}function z1(e,t,n,r){let i=[],o=[],s=null;const a=({state:p})=>{const d=s0(e,location),h=n.value,x=t.value;let j=0;if(p){if(n.value=d,t.value=p,s&&s===h){s=null;return}j=x?p.position-x.position:0}else r(d);i.forEach(L=>{L(n.value,h,{delta:j,type:Ki.pop,direction:j?j>0?Mi.forward:Mi.back:Mi.unknown})})};function l(){s=n.value}function u(p){i.push(p);const d=()=>{const h=i.indexOf(p);h>-1&&i.splice(h,1)};return o.push(d),d}function c(){const{history:p}=window;p.state&&p.replaceState(Fe({},p.state,{scroll:oa()}),"")}function f(){for(const p of o)p();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:u,destroy:f}}function Vc(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?oa():null}}function J1(e){const{history:t,location:n}=window,r={value:s0(e,n)},i={value:t.state};i.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,u,c){const f=e.indexOf("#"),p=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:q1()+e+l;try{t[c?"replaceState":"pushState"](u,"",p),i.value=u}catch(d){console.error(d),n[c?"replace":"assign"](p)}}function s(l,u){const c=Fe({},t.state,Vc(i.value.back,l,i.value.forward,!0),u,{position:i.value.position});o(l,c,!0),r.value=l}function a(l,u){const c=Fe({},i.value,t.state,{forward:l,scroll:oa()});o(c.current,c,!0);const f=Fe({},Vc(r.value,l,null),{position:c.position+1},u);o(l,f,!1),r.value=l}return{location:r,state:i,push:a,replace:s}}function hP(e){e=V1(e);const t=J1(e),n=z1(e,t.state,t.location,t.replace);function r(o,s=!0){s||n.pauseListeners(),history.go(o)}const i=Fe({location:"",base:e,go:r,createHref:G1.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function Q1(e){return typeof e=="string"||e&&typeof e=="object"}function a0(e){return typeof e=="string"||typeof e=="symbol"}const l0=Symbol("");var Hc;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Hc||(Hc={}));function Jr(e,t){return Fe(new Error,{type:e,[l0]:!0},t)}function vn(e,t){return e instanceof Error&&l0 in e&&(t==null||!!(e.type&t))}const Gc="[^/]+?",Y1={sensitive:!1,strict:!1,start:!0,end:!0},Z1=/[.+*?^${}()[\]/\\]/g;function e_(e,t){const n=Fe({},Y1,t),r=[];let i=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(i+="/");for(let f=0;f<u.length;f++){const p=u[f];let d=40+(n.sensitive?.25:0);if(p.type===0)f||(i+="/"),i+=p.value.replace(Z1,"\\$&"),d+=40;else if(p.type===1){const{value:h,repeatable:x,optional:j,regexp:L}=p;o.push({name:h,repeatable:x,optional:j});const A=L||Gc;if(A!==Gc){d+=10;try{new RegExp(`(${A})`)}catch(C){throw new Error(`Invalid custom RegExp for param "${h}" (${A}): `+C.message)}}let g=x?`((?:${A})(?:/(?:${A}))*)`:`(${A})`;f||(g=j&&u.length<2?`(?:/${g})`:"/"+g),j&&(g+="?"),i+=g,d+=20,j&&(d+=-8),x&&(d+=-20),A===".*"&&(d+=-50)}c.push(d)}r.push(c)}if(n.strict&&n.end){const u=r.length-1;r[u][r[u].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const s=new RegExp(i,n.sensitive?"":"i");function a(u){const c=u.match(s),f={};if(!c)return null;for(let p=1;p<c.length;p++){const d=c[p]||"",h=o[p-1];f[h.name]=d&&h.repeatable?d.split("/"):d}return f}function l(u){let c="",f=!1;for(const p of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const d of p)if(d.type===0)c+=d.value;else if(d.type===1){const{value:h,repeatable:x,optional:j}=d,L=h in u?u[h]:"";if(rn(L)&&!x)throw new Error(`Provided param "${h}" is an array but it is not repeatable (* or + modifiers)`);const A=rn(L)?L.join("/"):L;if(!A)if(j)p.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${h}"`);c+=A}}return c||"/"}return{re:s,score:r,keys:o,parse:a,stringify:l}}function t_(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function u0(e,t){let n=0;const r=e.score,i=t.score;for(;n<r.length&&n<i.length;){const o=t_(r[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-r.length)===1){if(Uc(r))return 1;if(Uc(i))return-1}return i.length-r.length}function Uc(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const n_={type:0,value:""},r_=/[a-zA-Z0-9_]/;function i_(e){if(!e)return[[]];if(e==="/")return[[n_]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(d){throw new Error(`ERR (${n})/"${u}": ${d}`)}let n=0,r=n;const i=[];let o;function s(){o&&i.push(o),o=[]}let a=0,l,u="",c="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function p(){u+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(u&&f(),s()):l===":"?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:l==="("?n=2:r_.test(l)?p():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:n=3:c+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),s(),i}function o_(e,t,n){const r=e_(i_(e.path),n),i=Fe(r,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function s_(e,t){const n=[],r=new Map;t=qc({strict:!1,end:!0,sensitive:!1},t);function i(f){return r.get(f)}function o(f,p,d){const h=!d,x=Xc(f);x.aliasOf=d&&d.record;const j=qc(t,f),L=[x];if("alias"in f){const C=typeof f.alias=="string"?[f.alias]:f.alias;for(const b of C)L.push(Xc(Fe({},x,{components:d?d.record.components:x.components,path:b,aliasOf:d?d.record:x})))}let A,g;for(const C of L){const{path:b}=C;if(p&&b[0]!=="/"){const R=p.record.path,T=R[R.length-1]==="/"?"":"/";C.path=p.record.path+(b&&T+b)}if(A=o_(C,p,j),d?d.alias.push(A):(g=g||A,g!==A&&g.alias.push(A),h&&f.name&&!Kc(A)&&s(f.name)),c0(A)&&l(A),x.children){const R=x.children;for(let T=0;T<R.length;T++)o(R[T],A,d&&d.children[T])}d=d||A}return g?()=>{s(g)}:Ni}function s(f){if(a0(f)){const p=r.get(f);p&&(r.delete(f),n.splice(n.indexOf(p),1),p.children.forEach(s),p.alias.forEach(s))}else{const p=n.indexOf(f);p>-1&&(n.splice(p,1),f.record.name&&r.delete(f.record.name),f.children.forEach(s),f.alias.forEach(s))}}function a(){return n}function l(f){const p=u_(f,n);n.splice(p,0,f),f.record.name&&!Kc(f)&&r.set(f.record.name,f)}function u(f,p){let d,h={},x,j;if("name"in f&&f.name){if(d=r.get(f.name),!d)throw Jr(1,{location:f});j=d.record.name,h=Fe(Wc(p.params,d.keys.filter(g=>!g.optional).concat(d.parent?d.parent.keys.filter(g=>g.optional):[]).map(g=>g.name)),f.params&&Wc(f.params,d.keys.map(g=>g.name))),x=d.stringify(h)}else if(f.path!=null)x=f.path,d=n.find(g=>g.re.test(x)),d&&(h=d.parse(x),j=d.record.name);else{if(d=p.name?r.get(p.name):n.find(g=>g.re.test(p.path)),!d)throw Jr(1,{location:f,currentLocation:p});j=d.record.name,h=Fe({},p.params,f.params),x=d.stringify(h)}const L=[];let A=d;for(;A;)L.unshift(A.record),A=A.parent;return{name:j,path:x,params:h,matched:L,meta:l_(L)}}e.forEach(f=>o(f));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:u,removeRoute:s,clearRoutes:c,getRoutes:a,getRecordMatcher:i}}function Wc(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Xc(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:a_(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function a_(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Kc(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function l_(e){return e.reduce((t,n)=>Fe(t,n.meta),{})}function qc(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function u_(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;u0(e,t[o])<0?r=o:n=o+1}const i=c_(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function c_(e){let t=e;for(;t=t.parent;)if(c0(t)&&u0(e,t)===0)return t}function c0({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function f_(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<r.length;++i){const o=r[i].replace(t0," "),s=o.indexOf("="),a=Xi(s<0?o:o.slice(0,s)),l=s<0?null:Xi(o.slice(s+1));if(a in t){let u=t[a];rn(u)||(u=t[a]=[u]),u.push(l)}else t[a]=l}return t}function zc(e){let t="";for(let n in e){const r=e[n];if(n=$1(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(rn(r)?r.map(o=>o&&yl(o)):[r&&yl(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function d_(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=rn(r)?r.map(i=>i==null?null:""+i):r==null?r:""+r)}return t}const p_=Symbol(""),Jc=Symbol(""),sa=Symbol(""),f0=Symbol(""),_l=Symbol("");function pi(){let e=[];function t(r){return e.push(r),()=>{const i=e.indexOf(r);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Gn(e,t,n,r,i,o=s=>s()){const s=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((a,l)=>{const u=p=>{p===!1?l(Jr(4,{from:n,to:t})):p instanceof Error?l(p):Q1(p)?l(Jr(2,{from:t,to:p})):(s&&r.enterCallbacks[i]===s&&typeof p=="function"&&s.push(p),a())},c=o(()=>e.call(r&&r.instances[i],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(p=>l(p))})}function Wa(e,t,n,r,i=o=>o()){const o=[];for(const s of e)for(const a in s.components){let l=s.components[a];if(!(t!=="beforeRouteEnter"&&!s.instances[a]))if(Zm(l)){const c=(l.__vccOpts||l)[t];c&&o.push(Gn(c,n,r,s,a,i))}else{let u=l();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${s.path}"`);const f=C1(c)?c.default:c;s.mods[a]=c,s.components[a]=f;const d=(f.__vccOpts||f)[t];return d&&Gn(d,n,r,s,a,i)()}))}}return o}function Qc(e){const t=zt(sa),n=zt(f0),r=Ce(()=>{const l=v(e.to);return t.resolve(l)}),i=Ce(()=>{const{matched:l}=r.value,{length:u}=l,c=l[u-1],f=n.matched;if(!c||!f.length)return-1;const p=f.findIndex(zr.bind(null,c));if(p>-1)return p;const d=Yc(l[u-2]);return u>1&&Yc(c)===d&&f[f.length-1].path!==d?f.findIndex(zr.bind(null,l[u-2])):p}),o=Ce(()=>i.value>-1&&y_(n.params,r.value.params)),s=Ce(()=>i.value>-1&&i.value===n.matched.length-1&&o0(n.params,r.value.params));function a(l={}){if(v_(l)){const u=t[v(e.replace)?"replace":"push"](v(e.to)).catch(Ni);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:r,href:Ce(()=>r.value.href),isActive:o,isExactActive:s,navigate:a}}function h_(e){return e.length===1?e[0]:e}const g_=Ne({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Qc,setup(e,{slots:t}){const n=ke(Qc(e)),{options:r}=zt(sa),i=Ce(()=>({[Zc(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Zc(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&h_(t.default(n));return e.custom?o:ai("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),m_=g_;function v_(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function y_(e,t){for(const n in t){const r=t[n],i=e[n];if(typeof r=="string"){if(r!==i)return!1}else if(!rn(i)||i.length!==r.length||r.some((o,s)=>o!==i[s]))return!1}return!0}function Yc(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Zc=(e,t,n)=>e??t??n,b_=Ne({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=zt(_l),i=Ce(()=>e.route||r.value),o=zt(Jc,0),s=Ce(()=>{let u=v(o);const{matched:c}=i.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),a=Ce(()=>i.value.matched[s.value]);jr(Jc,Ce(()=>s.value+1)),jr(p_,a),jr(_l,i);const l=ye();return we(()=>[l.value,a.value,e.name],([u,c,f],[p,d,h])=>{c&&(c.instances[f]=u,d&&d!==c&&u&&u===p&&(c.leaveGuards.size||(c.leaveGuards=d.leaveGuards),c.updateGuards.size||(c.updateGuards=d.updateGuards))),u&&c&&(!d||!zr(c,d)||!p)&&(c.enterCallbacks[f]||[]).forEach(x=>x(u))},{flush:"post"}),()=>{const u=i.value,c=e.name,f=a.value,p=f&&f.components[c];if(!p)return ef(n.default,{Component:p,route:u});const d=f.props[c],h=d?d===!0?u.params:typeof d=="function"?d(u):d:null,j=ai(p,Fe({},h,t,{onVnodeUnmounted:L=>{L.component.isUnmounted&&(f.instances[c]=null)},ref:l}));return ef(n.default,{Component:j,route:u})||j}}});function ef(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const __=b_;function gP(e){const t=s_(e.routes,e),n=e.parseQuery||f_,r=e.stringifyQuery||zc,i=e.history,o=pi(),s=pi(),a=pi(),l=ao(Ln);let u=Ln;Rr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Ga.bind(null,U=>""+U),f=Ga.bind(null,M1),p=Ga.bind(null,Xi);function d(U,re){let ie,se;return a0(U)?(ie=t.getRecordMatcher(U),se=re):se=U,t.addRoute(se,ie)}function h(U){const re=t.getRecordMatcher(U);re&&t.removeRoute(re)}function x(){return t.getRoutes().map(U=>U.record)}function j(U){return!!t.getRecordMatcher(U)}function L(U,re){if(re=Fe({},re||l.value),typeof U=="string"){const _=Ua(n,U,re.path),N=t.resolve({path:_.path},re),K=i.createHref(_.fullPath);return Fe(_,N,{params:p(N.params),hash:Xi(_.hash),redirectedFrom:void 0,href:K})}let ie;if(U.path!=null)ie=Fe({},U,{path:Ua(n,U.path,re.path).path});else{const _=Fe({},U.params);for(const N in _)_[N]==null&&delete _[N];ie=Fe({},U,{params:f(_)}),re.params=f(re.params)}const se=t.resolve(ie,re),q=U.hash||"";se.params=c(p(se.params));const G=D1(r,Fe({},U,{hash:k1(q),path:se.path})),m=i.createHref(G);return Fe({fullPath:G,hash:q,query:r===zc?d_(U.query):U.query||{}},se,{redirectedFrom:void 0,href:m})}function A(U){return typeof U=="string"?Ua(n,U,l.value.path):Fe({},U)}function g(U,re){if(u!==U)return Jr(8,{from:re,to:U})}function C(U){return T(U)}function b(U){return C(Fe(A(U),{replace:!0}))}function R(U){const re=U.matched[U.matched.length-1];if(re&&re.redirect){const{redirect:ie}=re;let se=typeof ie=="function"?ie(U):ie;return typeof se=="string"&&(se=se.includes("?")||se.includes("#")?se=A(se):{path:se},se.params={}),Fe({query:U.query,hash:U.hash,params:se.path!=null?{}:U.params},se)}}function T(U,re){const ie=u=L(U),se=l.value,q=U.state,G=U.force,m=U.replace===!0,_=R(ie);if(_)return T(Fe(A(_),{state:typeof _=="object"?Fe({},q,_.state):q,force:G,replace:m}),re||ie);const N=ie;N.redirectedFrom=re;let K;return!G&&F1(r,se,ie)&&(K=Jr(16,{to:N,from:se}),X(se,se,!0,!1)),(K?Promise.resolve(K):S(N,se)).catch(w=>vn(w)?vn(w,2)?w:V(w):W(w,N,se)).then(w=>{if(w){if(vn(w,2))return T(Fe({replace:m},A(w.to),{state:typeof w.to=="object"?Fe({},q,w.to.state):q,force:G}),re||N)}else w=I(N,se,!0,m,q);return H(N,se,w),w})}function O(U,re){const ie=g(U,re);return ie?Promise.reject(ie):Promise.resolve()}function y(U){const re=le.values().next().value;return re&&typeof re.runWithContext=="function"?re.runWithContext(U):U()}function S(U,re){let ie;const[se,q,G]=C_(U,re);ie=Wa(se.reverse(),"beforeRouteLeave",U,re);for(const _ of se)_.leaveGuards.forEach(N=>{ie.push(Gn(N,U,re))});const m=O.bind(null,U,re);return ie.push(m),me(ie).then(()=>{ie=[];for(const _ of o.list())ie.push(Gn(_,U,re));return ie.push(m),me(ie)}).then(()=>{ie=Wa(q,"beforeRouteUpdate",U,re);for(const _ of q)_.updateGuards.forEach(N=>{ie.push(Gn(N,U,re))});return ie.push(m),me(ie)}).then(()=>{ie=[];for(const _ of G)if(_.beforeEnter)if(rn(_.beforeEnter))for(const N of _.beforeEnter)ie.push(Gn(N,U,re));else ie.push(Gn(_.beforeEnter,U,re));return ie.push(m),me(ie)}).then(()=>(U.matched.forEach(_=>_.enterCallbacks={}),ie=Wa(G,"beforeRouteEnter",U,re,y),ie.push(m),me(ie))).then(()=>{ie=[];for(const _ of s.list())ie.push(Gn(_,U,re));return ie.push(m),me(ie)}).catch(_=>vn(_,8)?_:Promise.reject(_))}function H(U,re,ie){a.list().forEach(se=>y(()=>se(U,re,ie)))}function I(U,re,ie,se,q){const G=g(U,re);if(G)return G;const m=re===Ln,_=Rr?history.state:{};ie&&(se||m?i.replace(U.fullPath,Fe({scroll:m&&_&&_.scroll},q)):i.push(U.fullPath,q)),l.value=U,X(U,re,ie,m),V()}let M;function z(){M||(M=i.listen((U,re,ie)=>{if(!ce.listening)return;const se=L(U),q=R(se);if(q){T(Fe(q,{replace:!0,force:!0}),se).catch(Ni);return}u=se;const G=l.value;Rr&&X1(Bc(G.fullPath,ie.delta),oa()),S(se,G).catch(m=>vn(m,12)?m:vn(m,2)?(T(Fe(A(m.to),{force:!0}),se).then(_=>{vn(_,20)&&!ie.delta&&ie.type===Ki.pop&&i.go(-1,!1)}).catch(Ni),Promise.reject()):(ie.delta&&i.go(-ie.delta,!1),W(m,se,G))).then(m=>{m=m||I(se,G,!1),m&&(ie.delta&&!vn(m,8)?i.go(-ie.delta,!1):ie.type===Ki.pop&&vn(m,20)&&i.go(-1,!1)),H(se,G,m)}).catch(Ni)}))}let Z=pi(),J=pi(),P;function W(U,re,ie){V(U);const se=J.list();return se.length?se.forEach(q=>q(U,re,ie)):console.error(U),Promise.reject(U)}function D(){return P&&l.value!==Ln?Promise.resolve():new Promise((U,re)=>{Z.add([U,re])})}function V(U){return P||(P=!U,z(),Z.list().forEach(([re,ie])=>U?ie(U):re()),Z.reset()),U}function X(U,re,ie,se){const{scrollBehavior:q}=e;if(!Rr||!q)return Promise.resolve();const G=!ie&&K1(Bc(U.fullPath,0))||(se||!ie)&&history.state&&history.state.scroll||null;return on().then(()=>q(U,re,G)).then(m=>m&&W1(m)).catch(m=>W(m,U,re))}const te=U=>i.go(U);let ne;const le=new Set,ce={currentRoute:l,listening:!0,addRoute:d,removeRoute:h,clearRoutes:t.clearRoutes,hasRoute:j,getRoutes:x,resolve:L,options:e,push:C,replace:b,go:te,back:()=>te(-1),forward:()=>te(1),beforeEach:o.add,beforeResolve:s.add,afterEach:a.add,onError:J.add,isReady:D,install(U){const re=this;U.component("RouterLink",m_),U.component("RouterView",__),U.config.globalProperties.$router=re,Object.defineProperty(U.config.globalProperties,"$route",{enumerable:!0,get:()=>v(l)}),Rr&&!ne&&l.value===Ln&&(ne=!0,C(i.location).catch(q=>{}));const ie={};for(const q in Ln)Object.defineProperty(ie,q,{get:()=>l.value[q],enumerable:!0});U.provide(sa,re),U.provide(f0,Vs(ie)),U.provide(_l,l);const se=U.unmount;le.add(U),U.unmount=function(){le.delete(U),le.size<1&&(u=Ln,M&&M(),M=null,l.value=Ln,ne=!1,P=!1),se()}}};function me(U){return U.reduce((re,ie)=>re.then(()=>y(ie)),Promise.resolve())}return ce}function C_(e,t){const n=[],r=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let s=0;s<o;s++){const a=t.matched[s];a&&(e.matched.find(u=>zr(u,a))?r.push(a):n.push(a));const l=e.matched[s];l&&(t.matched.find(u=>zr(u,l))||i.push(l))}return[n,r,i]}function mP(){return zt(sa)}var x_=Object.defineProperty,E_=Object.defineProperties,S_=Object.getOwnPropertyDescriptors,tf=Object.getOwnPropertySymbols,w_=Object.prototype.hasOwnProperty,T_=Object.prototype.propertyIsEnumerable,nf=(e,t,n)=>t in e?x_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,O_=(e,t)=>{for(var n in t||(t={}))w_.call(t,n)&&nf(e,n,t[n]);if(tf)for(var n of tf(t))T_.call(t,n)&&nf(e,n,t[n]);return e},P_=(e,t)=>E_(e,S_(t));function vP(e,t){var n;const r=ao();return iu(()=>{r.value=e()},P_(O_({},t),{flush:(n=void 0)!=null?n:"sync"})),ii(r)}var rf;const aa=typeof window<"u",A_=e=>typeof e<"u",Cl=e=>typeof e=="function",I_=e=>typeof e=="string",Qr=()=>{},k_=aa&&((rf=window==null?void 0:window.navigator)==null?void 0:rf.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);function Qn(e){return typeof e=="function"?e():v(e)}function d0(e,t){function n(...r){return new Promise((i,o)=>{Promise.resolve(e(()=>t.apply(this,r),{fn:t,thisArg:this,args:r})).then(i).catch(o)})}return n}function $_(e,t={}){let n,r,i=Qr;const o=a=>{clearTimeout(a),i(),i=Qr};return a=>{const l=Qn(e),u=Qn(t.maxWait);return n&&o(n),l<=0||u!==void 0&&u<=0?(r&&(o(r),r=null),Promise.resolve(a())):new Promise((c,f)=>{i=t.rejectOnCancel?f:c,u&&!r&&(r=setTimeout(()=>{n&&o(n),r=null,c(a())},u)),n=setTimeout(()=>{r&&o(r),r=null,c(a())},l)})}}function N_(e,t=!0,n=!0,r=!1){let i=0,o,s=!0,a=Qr,l;const u=()=>{o&&(clearTimeout(o),o=void 0,a(),a=Qr)};return f=>{const p=Qn(e),d=Date.now()-i,h=()=>l=f();return u(),p<=0?(i=Date.now(),h()):(d>p&&(n||!s)?(i=Date.now(),h()):t&&(l=new Promise((x,j)=>{a=r?j:x,o=setTimeout(()=>{i=Date.now(),s=!0,x(h()),u()},Math.max(0,p-d))})),!n&&!o&&(o=setTimeout(()=>s=!0,p)),s=!1,l)}}function M_(e){return e}function R_(e,t){let n,r,i;const o=ye(!0),s=()=>{o.value=!0,i()};we(e,s,{flush:"sync"});const a=Cl(t)?t:t.get,l=Cl(t)?void 0:t.set,u=Ws((c,f)=>(r=c,i=f,{get(){return o.value&&(n=a(),o.value=!1),r(),n},set(p){l==null||l(p)}}));return Object.isExtensible(u)&&(u.trigger=s),u}function la(e){return oo()?(Ds(e),!0):!1}function L_(e,t=200,n={}){return d0($_(t,n),e)}function yP(e,t=200,n={}){const r=ye(e.value),i=L_(()=>{r.value=e.value},t,n);return we(e,()=>i()),r}function bP(e,t=200,n=!1,r=!0,i=!1){return d0(N_(t,n,r,i),e)}function bu(e,t=!0){gt()?Ue(e):t?e():on(e)}function _P(e,t,n={}){const{immediate:r=!0}=n,i=ye(!1);let o=null;function s(){o&&(clearTimeout(o),o=null)}function a(){i.value=!1,s()}function l(...u){s(),i.value=!0,o=setTimeout(()=>{i.value=!1,o=null,e(...u)},Qn(t))}return r&&(i.value=!0,aa&&l()),la(a),{isPending:ii(i),start:l,stop:a}}function fn(e){var t;const n=Qn(e);return(t=n==null?void 0:n.$el)!=null?t:n}const or=aa?window:void 0,D_=aa?window.document:void 0;function Qt(...e){let t,n,r,i;if(I_(e[0])||Array.isArray(e[0])?([n,r,i]=e,t=or):[t,n,r,i]=e,!t)return Qr;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const o=[],s=()=>{o.forEach(c=>c()),o.length=0},a=(c,f,p,d)=>(c.addEventListener(f,p,d),()=>c.removeEventListener(f,p,d)),l=we(()=>[fn(t),Qn(i)],([c,f])=>{s(),c&&o.push(...n.flatMap(p=>r.map(d=>a(c,p,d,f))))},{immediate:!0,flush:"post"}),u=()=>{l(),s()};return la(u),u}let of=!1;function CP(e,t,n={}){const{window:r=or,ignore:i=[],capture:o=!0,detectIframe:s=!1}=n;if(!r)return;k_&&!of&&(of=!0,Array.from(r.document.body.children).forEach(p=>p.addEventListener("click",Qr)));let a=!0;const l=p=>i.some(d=>{if(typeof d=="string")return Array.from(r.document.querySelectorAll(d)).some(h=>h===p.target||p.composedPath().includes(h));{const h=fn(d);return h&&(p.target===h||p.composedPath().includes(h))}}),c=[Qt(r,"click",p=>{const d=fn(e);if(!(!d||d===p.target||p.composedPath().includes(d))){if(p.detail===0&&(a=!l(p)),!a){a=!0;return}t(p)}},{passive:!0,capture:o}),Qt(r,"pointerdown",p=>{const d=fn(e);d&&(a=!p.composedPath().includes(d)&&!l(p))},{passive:!0}),s&&Qt(r,"blur",p=>{var d;const h=fn(e);((d=r.document.activeElement)==null?void 0:d.tagName)==="IFRAME"&&!(h!=null&&h.contains(r.document.activeElement))&&t(p)})].filter(Boolean);return()=>c.forEach(p=>p())}function xP(e={}){var t;const{window:n=or}=e,r=(t=e.document)!=null?t:n==null?void 0:n.document,i=R_(()=>null,()=>r==null?void 0:r.activeElement);return n&&(Qt(n,"blur",o=>{o.relatedTarget===null&&i.trigger()},!0),Qt(n,"focus",i.trigger,!0)),i}function p0(e,t=!1){const n=ye(),r=()=>n.value=!!e();return r(),bu(r,t),n}function F_(e){return JSON.parse(JSON.stringify(e))}const sf=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},af="__vueuse_ssr_handlers__";sf[af]=sf[af]||{};function EP(e,t,{window:n=or,initialValue:r=""}={}){const i=ye(r),o=Ce(()=>{var s;return fn(t)||((s=n==null?void 0:n.document)==null?void 0:s.documentElement)});return we([o,()=>Qn(e)],([s,a])=>{var l;if(s&&n){const u=(l=n.getComputedStyle(s).getPropertyValue(a))==null?void 0:l.trim();i.value=u||r}},{immediate:!0}),we(i,s=>{var a;(a=o.value)!=null&&a.style&&o.value.style.setProperty(Qn(e),s)}),i}function SP({document:e=D_}={}){if(!e)return ye("visible");const t=ye(e.visibilityState);return Qt(e,"visibilitychange",()=>{t.value=e.visibilityState}),t}var lf=Object.getOwnPropertySymbols,j_=Object.prototype.hasOwnProperty,B_=Object.prototype.propertyIsEnumerable,V_=(e,t)=>{var n={};for(var r in e)j_.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&lf)for(var r of lf(e))t.indexOf(r)<0&&B_.call(e,r)&&(n[r]=e[r]);return n};function H_(e,t,n={}){const r=n,{window:i=or}=r,o=V_(r,["window"]);let s;const a=p0(()=>i&&"ResizeObserver"in i),l=()=>{s&&(s.disconnect(),s=void 0)},u=we(()=>fn(e),f=>{l(),a.value&&i&&f&&(s=new ResizeObserver(t),s.observe(f,o))},{immediate:!0,flush:"post"}),c=()=>{l(),u()};return la(c),{isSupported:a,stop:c}}function wP(e,t={}){const{reset:n=!0,windowResize:r=!0,windowScroll:i=!0,immediate:o=!0}=t,s=ye(0),a=ye(0),l=ye(0),u=ye(0),c=ye(0),f=ye(0),p=ye(0),d=ye(0);function h(){const x=fn(e);if(!x){n&&(s.value=0,a.value=0,l.value=0,u.value=0,c.value=0,f.value=0,p.value=0,d.value=0);return}const j=x.getBoundingClientRect();s.value=j.height,a.value=j.bottom,l.value=j.left,u.value=j.right,c.value=j.top,f.value=j.width,p.value=j.x,d.value=j.y}return H_(e,h),we(()=>fn(e),x=>!x&&h()),i&&Qt("scroll",h,{capture:!0,passive:!0}),r&&Qt("resize",h,{passive:!0}),bu(()=>{o&&h()}),{height:s,bottom:a,left:l,right:u,top:c,width:f,x:p,y:d,update:h}}var uf=Object.getOwnPropertySymbols,G_=Object.prototype.hasOwnProperty,U_=Object.prototype.propertyIsEnumerable,W_=(e,t)=>{var n={};for(var r in e)G_.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&uf)for(var r of uf(e))t.indexOf(r)<0&&U_.call(e,r)&&(n[r]=e[r]);return n};function TP(e,t,n={}){const r=n,{window:i=or}=r,o=W_(r,["window"]);let s;const a=p0(()=>i&&"MutationObserver"in i),l=()=>{s&&(s.disconnect(),s=void 0)},u=we(()=>fn(e),f=>{l(),a.value&&i&&f&&(s=new MutationObserver(t),s.observe(f,o))},{immediate:!0}),c=()=>{l(),u()};return la(c),{isSupported:a,stop:c}}var cf;(function(e){e.UP="UP",e.RIGHT="RIGHT",e.DOWN="DOWN",e.LEFT="LEFT",e.NONE="NONE"})(cf||(cf={}));var X_=Object.defineProperty,ff=Object.getOwnPropertySymbols,K_=Object.prototype.hasOwnProperty,q_=Object.prototype.propertyIsEnumerable,df=(e,t,n)=>t in e?X_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,z_=(e,t)=>{for(var n in t||(t={}))K_.call(t,n)&&df(e,n,t[n]);if(ff)for(var n of ff(t))q_.call(t,n)&&df(e,n,t[n]);return e};const J_={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]};z_({linear:M_},J_);function OP(e,t,n,r={}){var i,o,s;const{clone:a=!1,passive:l=!1,eventName:u,deep:c=!1,defaultValue:f}=r,p=gt(),d=n||(p==null?void 0:p.emit)||((i=p==null?void 0:p.$emit)==null?void 0:i.bind(p))||((s=(o=p==null?void 0:p.proxy)==null?void 0:o.$emit)==null?void 0:s.bind(p==null?void 0:p.proxy));let h=u;t||(t="modelValue"),h=u||h||`update:${t.toString()}`;const x=L=>a?Cl(a)?a(L):F_(L):L,j=()=>A_(e[t])?x(e[t]):f;if(l){const L=j(),A=ye(L);return we(()=>e[t],g=>A.value=x(g)),we(A,g=>{(g!==e[t]||c)&&d(h,g)},{deep:c}),A}else return Ce({get(){return j()},set(L){d(h,L)}})}function PP({window:e=or}={}){if(!e)return ye(!1);const t=ye(e.document.hasFocus());return Qt(e,"blur",()=>{t.value=!1}),Qt(e,"focus",()=>{t.value=!0}),t}function AP(e={}){const{window:t=or,initialWidth:n=1/0,initialHeight:r=1/0,listenOrientation:i=!0,includeScrollbar:o=!0}=e,s=ye(n),a=ye(r),l=()=>{t&&(o?(s.value=t.innerWidth,a.value=t.innerHeight):(s.value=t.document.documentElement.clientWidth,a.value=t.document.documentElement.clientHeight))};return l(),bu(l),Qt("resize",l,{passive:!0}),i&&Qt("orientationchange",l,{passive:!0}),{width:s,height:a}}var h0=(e=>(e.transparent="rgba(0,0,0,0)",e.black="#000000",e.silver="#C0C0C0",e.gray="#808080",e.white="#FFFFFF",e.maroon="#800000",e.red="#FF0000",e.purple="#800080",e.fuchsia="#FF00FF",e.green="#008000",e.lime="#00FF00",e.olive="#808000",e.yellow="#FFFF00",e.navy="#000080",e.blue="#0000FF",e.teal="#008080",e.aqua="#00FFFF",e.aliceblue="#f0f8ff",e.antiquewhite="#faebd7",e.aquamarine="#7fffd4",e.azure="#f0ffff",e.beige="#f5f5dc",e.bisque="#ffe4c4",e.blanchedalmond="#ffebcd",e.blueviolet="#8a2be2",e.brown="#a52a2a",e.burlywood="#deb887",e.cadetblue="#5f9ea0",e.chartreuse="#7fff00",e.chocolate="#d2691e",e.coral="#ff7f50",e.cornflowerblue="#6495ed",e.cornsilk="#fff8dc",e.crimson="#dc143c",e.cyan="#00ffff",e.darkblue="#00008b",e.darkcyan="#008b8b",e.darkgoldenrod="#b8860b",e.darkgray="#a9a9a9",e.darkgreen="#006400",e.darkgrey="#a9a9a9",e.darkkhaki="#bdb76b",e.darkmagenta="#8b008b",e.darkolivegreen="#556b2f",e.darkorange="#ff8c00",e.darkorchid="#9932cc",e.darkred="#8b0000",e.darksalmon="#e9967a",e.darkseagreen="#8fbc8f",e.darkslateblue="#483d8b",e.darkslategray="#2f4f4f",e.darkslategrey="#2f4f4f",e.darkturquoise="#00ced1",e.darkviolet="#9400d3",e.deeppink="#ff1493",e.deepskyblue="#00bfff",e.dimgray="#696969",e.dimgrey="#696969",e.dodgerblue="#1e90ff",e.firebrick="#b22222",e.floralwhite="#fffaf0",e.forestgreen="#228b22",e.gainsboro="#dcdcdc",e.ghostwhite="#f8f8ff",e.gold="#ffd700",e.goldenrod="#daa520",e.greenyellow="#adff2f",e.grey="#808080",e.honeydew="#f0fff0",e.hotpink="#ff69b4",e.indianred="#cd5c5c",e.indigo="#4b0082",e.ivory="#fffff0",e.khaki="#f0e68c",e.lavender="#e6e6fa",e.lavenderblush="#fff0f5",e.lawngreen="#7cfc00",e.lemonchiffon="#fffacd",e.lightblue="#add8e6",e.lightcoral="#f08080",e.lightcyan="#e0ffff",e.lightgoldenrodyellow="#fafad2",e.lightgray="#d3d3d3",e.lightgreen="#90ee90",e.lightgrey="#d3d3d3",e.lightpink="#ffb6c1",e.lightsalmon="#ffa07a",e.lightseagreen="#20b2aa",e.lightskyblue="#87cefa",e.lightslategray="#778899",e.lightslategrey="#778899",e.lightsteelblue="#b0c4de",e.lightyellow="#ffffe0",e.limegreen="#32cd32",e.linen="#faf0e6",e.magenta="#ff00ff",e.mediumaquamarine="#66cdaa",e.mediumblue="#0000cd",e.mediumorchid="#ba55d3",e.mediumpurple="#9370db",e.mediumseagreen="#3cb371",e.mediumslateblue="#7b68ee",e.mediumspringgreen="#00fa9a",e.mediumturquoise="#48d1cc",e.mediumvioletred="#c71585",e.midnightblue="#191970",e.mintcream="#f5fffa",e.mistyrose="#ffe4e1",e.moccasin="#ffe4b5",e.navajowhite="#ffdead",e.oldlace="#fdf5e6",e.olivedrab="#6b8e23",e.orange="#ffa500",e.orangered="#ff4500",e.orchid="#da70d6",e.palegoldenrod="#eee8aa",e.palegreen="#98fb98",e.paleturquoise="#afeeee",e.palevioletred="#db7093",e.papayawhip="#ffefd5",e.peachpuff="#ffdab9",e.peru="#cd853f",e.pink="#ffc0cb",e.plum="#dda0dd",e.powderblue="#b0e0e6",e.rosybrown="#bc8f8f",e.royalblue="#4169e1",e.saddlebrown="#8b4513",e.salmon="#fa8072",e.sandybrown="#f4a460",e.seagreen="#2e8b57",e.seashell="#fff5ee",e.sienna="#a0522d",e.skyblue="#87ceeb",e.slateblue="#6a5acd",e.slategray="#708090",e.snow="#fffafa",e.springgreen="#00ff7f",e.steelblue="#4682b4",e.tan="#d2b48c",e.thistle="#d8bfd8",e.tomato="#ff6347",e.turquoise="#40e0d0",e.violet="#ee82ee",e.wheat="#f5deb3",e.whitesmoke="#f5f5f5",e.yellowgreen="#9acd32",e))(h0||{});function ua(e){return typeof e!="string"?!1:(e=e.toLowerCase(),/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e))}function Q_(e){return typeof e!="string"?!1:(e=e.toLowerCase(),/^(rgb\(|RGB\()/.test(e))}function g0(e){return typeof e!="string"?!1:(e=e.toLowerCase(),/^(rgba|RGBA)/.test(e))}function m0(e){return/^(rgb|rgba|RGB|RGBA)/.test(e)}function Y_(e){return h0[e]}function v0(e){if(ua(e)||m0(e))return e;const t=Y_(e);if(!t)throw new Error(`Color: Invalid Input of ${e}`);return t}function Z_(e){e=e.replace("#",""),e.length===3&&(e=Array.from(e).map(n=>n+n).join(""));const t=e.split("");return new Array(3).fill(0).map((n,r)=>parseInt(`0x${t[r*2]}${t[r*2+1]}`))}function eC(e){return e.replace(/rgb\(|rgba\(|\)/g,"").split(",").slice(0,3).map(t=>parseInt(t))}function go(e){const t=v0(e).toLowerCase();return ua(t)?Z_(t):eC(t)}function y0(e){const t=v0(e);return g0(t)?Number(t.toLowerCase().split(",").slice(-1)[0].replace(/[)|\s]/g,"")):1}function _u(e){const t=go(e);return t&&[...t,y0(e)]}function tC(e,t){const n=go(e);return typeof t=="number"?`rgba(${n.join(",")},${t})`:`rgb(${n.join(",")})`}function nC(e){if(ua(e))return e;const t=go(e),n=r=>Number(r).toString(16).padStart(2,"0");return`#${t.map(n).join("")}`}function ca(e){if(!Array.isArray(e))throw new Error(`getColorFromRgbValue: ${e} is not an array`);const{length:t}=e;if(t!==3&&t!==4)throw new Error("getColorFromRgbValue: value length should be 3 or 4");return(t===3?"rgb(":"rgba(")+e.join(",")+")"}function rC(e,t=0){let n=_u(e);return n=n.map((r,i)=>i===3?r:r-Math.ceil(2.55*t)).map(r=>r<0?0:r),ca(n)}function xl(e,t=0){let n=_u(e);return n=n.map((r,i)=>i===3?r:r+Math.ceil(2.55*t)).map(r=>r>255?255:r),ca(n)}function ut(e,t=100){const n=go(e);return ca([...n,t/100])}const iC=Object.freeze(Object.defineProperty({__proto__:null,darken:rC,fade:ut,getColorFromRgbValue:ca,getOpacity:y0,getRgbValue:go,getRgbaValue:_u,isHex:ua,isRgb:Q_,isRgbOrRgba:m0,isRgba:g0,lighten:xl,toHex:nC,toRgb:tC},Symbol.toStringTag,{value:"Module"})),sr=(e,t)=>{const n=e.__vccOpts||e;for(const[r,i]of t)n[r]=i;return n},oC={},sC={viewBox:"0 0 187 38",preserveAspectRatio:"none",class:"dv-button-svg"};function aC(e,t){return fe(),de("svg",sC,t[0]||(t[0]=[ae("g",{style:{transform:"translate(2px, 2px)"}},[ae("g",null,[ae("path",{"data-type":"shape",d:"M0,0 L0,34 L168,34 L183,19 L183,0",class:"dv-button-svg-bg"})]),ae("path",{"data-type":"polyline",d:"M0,34 L168,34 L183,19",class:"dv-button-svg-line"})],-1)]))}const lC=sr(oC,[["render",aC]]),uC={},cC={viewBox:"0 0 167 38",preserveAspectRatio:"none",class:"dv-button-svg"};function fC(e,t){return fe(),de("svg",cC,t[0]||(t[0]=[si('<g style="transform:translate(2px, 2px);"><g><path data-type="shape" d="M0,0 L0,34 L163,34 L163,0" class="dv-button-svg-bg"></path></g><path data-type="polyline" d="M0,0 L164.1,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,0 L163,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M164.1,34 L0,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M1.1,34 L1.1,0" class="dv-button-svg-line"></path></g>',1)]))}const dC=sr(uC,[["render",fC]]),pC={},hC={viewBox:"0 0 167 38",preserveAspectRatio:"none",class:"dv-button-svg"};function gC(e,t){return fe(),de("svg",hC,t[0]||(t[0]=[si('<g style="transform:translate(2px, 2px);"><g><path data-type="shape" d="M1,1 L1,33 L162,33 L162,1" class="dv-button-svg-bg"></path></g><path data-type="polyline" d="M0,0 L0,10" class="dv-button-svg-line"></path><path data-type="polyline" d="M-1.1,0 L10,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M164.1,0 L153,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,0 L163,10" class="dv-button-svg-line"></path><path data-type="polyline" d="M164.1,34 L153,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,34 L163,24" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,34 L0,24" class="dv-button-svg-line"></path><path data-type="polyline" d="M-1.1,34 L10,34" class="dv-button-svg-line"></path></g>',1)]))}const mC=sr(pC,[["render",gC]]),vC={},yC={viewBox:"0 0 187 38",preserveAspectRatio:"none",class:"dv-button-svg"};function bC(e,t){return fe(),de("svg",yC,t[0]||(t[0]=[ae("g",{style:{transform:"translate(2px, 2px)"}},[ae("g",null,[ae("path",{"data-type":"shape",d:"M0,34 L168,34 L183,19 L183,0 L0,0",class:"dv-button-svg-bg"})]),ae("path",{"data-type":"polyline",d:"M0,34 L168,34 L183,19 L183,0",class:"dv-button-svg-line"}),ae("path",{"data-type":"polyline",d:"M184.1,0 L0,0 L0,34.7",class:"dv-button-svg-line"})],-1)]))}const _C=sr(vC,[["render",bC]]),CC={},xC={viewBox:"0 0 187 38",preserveAspectRatio:"none",class:"dv-button-svg"};function EC(e,t){return fe(),de("svg",xC,t[0]||(t[0]=[ae("g",{style:{transform:"translate(2px, 2px)"}},[ae("g",null,[ae("path",{"data-type":"shape",d:"M0,34 L168,34 L183,19 L183,0 L15,0 L0,15",class:"dv-button-svg-bg"})]),ae("path",{"data-type":"polyline",d:"M0,34 L168,34 L183,19 L183,0",class:"dv-button-svg-line"}),ae("path",{"data-type":"polyline",d:"M183,0 L15,0 L0,15 L0,34",class:"dv-button-svg-line"})],-1)]))}const SC=sr(CC,[["render",EC]]),wC={},TC={viewBox:"0 0 167 38",preserveAspectRatio:"none",class:"dv-button-svg"};function OC(e,t){return fe(),de("svg",TC,t[0]||(t[0]=[si('<g style="transform:translate(2px, 2px);"><g><path data-type="shape" d="M0,0 L0,34 L163,34 L163,0" class="dv-button-svg-bg"></path></g><path data-type="polyline" d="M0,0 L81.6,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,0 L81.4,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,34 L81.6,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,34 L81.4,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,1 L10,1" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,1 L153,1" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,33 L10,33" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,33 L153,33" class="dv-button-svg-line"></path></g>',1)]))}const PC=sr(wC,[["render",OC]]),AC={class:"dv-button-wrapper"},IC={class:"dv-button"},kC={class:"dv-button-svg-container"},$C={class:"dv-button-text"},NC=Ne({components:{Border1:lC,Border2:dC,Border3:mC,Border4:_C,Border5:SC,Border6:PC},__name:"index",props:{color:{default:"#2058c7"},fontColor:{default:""},bg:{type:Boolean,default:!0},border:{default:"Border1"},fontSize:{default:14}},setup(e){Or(a=>({"108fc75d":v(s),"45ef2fd4":a.color,fc71f308:v(r),"6ca41ab4":v(n),"1faf6725":v(i),"9aee3820":v(o)}));const t=e,n=Ce(()=>xl(t.color,40)),r=Ce(()=>t.fontColor===""?t.color:t.fontColor),i=Ce(()=>xl(r.value,40)),o=Ce(()=>t.bg?.1:0),s=Ce(()=>`${t.fontSize}px`);return(a,l)=>(fe(),de("div",AC,[ae("button",IC,[ae("div",kC,[(fe(),Wr(ql(a.border)))]),ae("div",$C,[ze(a.$slots,"default")])])]))}}),pf={install(e){e.component("DvButton",NC)}};function MC(e){return oo()?(Ds(e),!0):!1}function Cs(e){return typeof e=="function"?e():v(e)}const RC=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const LC=Object.prototype.toString,DC=e=>LC.call(e)==="[object Object]",El=()=>{};function FC(e,t){function n(...r){return new Promise((i,o)=>{Promise.resolve(e(()=>t.apply(this,r),{fn:t,thisArg:this,args:r})).then(i).catch(o)})}return n}function jC(e,t={}){let n,r,i=El;const o=s=>{clearTimeout(s),i(),i=El};return s=>{const a=Cs(e),l=Cs(t.maxWait);return n&&o(n),a<=0||l!==void 0&&l<=0?(r&&(o(r),r=null),Promise.resolve(s())):new Promise((u,c)=>{i=t.rejectOnCancel?c:u,l&&!r&&(r=setTimeout(()=>{n&&o(n),r=null,u(s())},l)),n=setTimeout(()=>{r&&o(r),r=null,u(s())},a)})}}function BC(e,t=200,n={}){return FC(jC(t,n),e)}function VC(e){var t;const n=Cs(e);return(t=n==null?void 0:n.$el)!=null?t:n}const HC=RC?window:void 0;function GC(...e){let t,n,r,i;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,r,i]=e,t=HC):[t,n,r,i]=e,!t)return El;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const o=[],s=()=>{o.forEach(c=>c()),o.length=0},a=(c,f,p,d)=>(c.addEventListener(f,p,d),()=>c.removeEventListener(f,p,d)),l=we(()=>[VC(t),Cs(i)],([c,f])=>{if(s(),!c)return;const p=DC(f)?{...f}:f;o.push(...n.flatMap(d=>r.map(h=>a(c,d,h,p))))},{immediate:!0,flush:"post"}),u=()=>{l(),s()};return MC(u),u}function qi(e,t){return arguments.length===1?Number.parseInt((Math.random()*e+1).toString(),10):Number.parseInt((Math.random()*(t-e+1)+e).toString(),10)}function UC(e,t){const n=window.MutationObserver,r=new n(t);return r.observe(e,{attributes:!0,attributeFilter:["style"],attributeOldValue:!0}),r}function xs(e,t){const n=Math.abs(e[0]-t[0]),r=Math.abs(e[1]-t[1]);return Math.sqrt(n*n+r*r)}function Ir(e,t,n,r){return[e+Math.cos(r)*n,t+Math.sin(r)*n]}function WC(e){return e.filter(t=>typeof t=="number")}function XC(e){return e=WC(e),e.reduce((t,n)=>t+n,0)}function KC(e,t){const n=Math.abs(e.x-t.x),r=Math.abs(e.y-t.y);return Math.sqrt(n*n+r*r)}function hf(e){const t=Array.from({length:e.length-1}).fill(0).map((n,r)=>[e[r],e[r+1]]).map(n=>KC(n[0],n[1]));return XC(t)}function qC(e){return`${e.x},${e.y}`}function gf(e){return e.map(qC).join(" ")}function sn(e){return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,t=>{const n=Math.random()*16|0;return(t==="x"?n:n&3|8).toString(16)})}function je(e,t){for(const n in t){if(e[n]&&typeof e[n]=="object"){je(e[n],t[n]);continue}if(typeof t[n]=="object"){e[n]=Be(t[n]);continue}e[n]=t[n]}return e}function Be(e,t){if(!e)return e;const n=Array.isArray(e)?[]:{};if(e&&typeof e=="object")for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(e[r]&&typeof e[r]=="object"?n[r]=Be(e[r]):n[r]=e[r]);return n}function De(e,t,n){const r=ye(0),i=ye(0);let o,s=null,a=null;const l=(d=!0)=>new Promise(h=>{on(()=>{a=e.value,r.value=e.value?e.value.clientWidth:0,i.value=e.value?e.value.clientHeight:0,e.value?(!r.value||!i.value)&&console.warn("DataV: Component width or height is 0px, rendering abnormality may occur!"):console.warn("DataV: Failed to get dom node, component rendering may be abnormal!"),typeof t=="function"&&d&&t(),h(!0)})}),u=()=>{o=BC(l,200)},c=()=>{s=UC(a,o),GC(window,"resize",o)},f=()=>{s&&(s.disconnect(),s.takeRecords(),s=null)},p=async()=>{await l(!1),u(),c(),typeof n=="function"&&n()};return Ue(()=>{p()}),Mn(()=>{f()}),Js(p),Qs(f),{width:r,height:i,initWH:l}}const zC=["width","height"],JC=["d","fill"],QC=["fill","x","y"],YC=["xlink:href","width","height","x","y"],ZC=["fill","x","y"],ex={__name:"index",props:{config:{type:Object,default:()=>({})}},setup(e){Or(d=>({"51c9737a":v(s)}));const t=e,n=ye(null),{width:r,height:i}=De(n,l,a),o=ke({defaultConfig:{data:[],img:[],fontSize:12,imgSideLength:30,columnColor:"rgba(0, 194, 255, 0.4)",textColor:"#fff",showValue:!1,sort:!0},mergedConfig:null,column:[]}),s=Ce(()=>`${t.config.fontSize?t.config.fontSize:o.defaultConfig.fontSize}px`);we(()=>t.config,()=>{u()},{deep:!0});function a(){u()}function l(){u()}function u(){c(),f(),p()}function c(){o.mergedConfig=je(Be(o.defaultConfig),t.config||{})}function f(){let{data:d}=o.mergedConfig;const{sort:h}=o.mergedConfig;d=Be(d),h&&d.sort(({value:j},{value:L})=>j>L?-1:j<L?1:0);const x=Math.max(...d.map(j=>j.value));d=d.map(j=>({...j,percent:x===0?0:j.value/x})),o.mergedConfig.data=d}function p(){const{imgSideLength:d,fontSize:h,data:x}=o.mergedConfig,j=x.length,L=r.value/(j+1),A=i.value-d-h-5,g=i.value-h-5;o.column=x.map((C,b)=>{const{percent:R}=C,T=L*(b+1),O=L*b,y=L*(b+2),S=g-A*R,H=A*R*.6+S,I=`
          M${O}, ${g}
          Q${T}, ${H} ${T},${S}
          M${T},${S}
          Q${T}, ${H} ${y},${g}
          L${O}, ${g}
          Z
        `,M=(g+S)/2+h/2;return{...C,d:I,x:T,y:S,textY:M}})}return(d,h)=>(fe(),de("div",{ref_key:"conicalColumnChart",ref:n,class:"dv-conical-column-chart"},[(fe(),de("svg",{width:v(r),height:v(i)},[(fe(!0),de(Se,null,qe(v(o).column,(x,j)=>(fe(),de("g",{key:j},[ae("path",{d:x.d,fill:v(o).mergedConfig.columnColor},null,8,JC),ae("text",{fill:v(o).mergedConfig.textColor,x:x.x,y:v(i)-4},ct(x.name),9,QC),v(o).mergedConfig.img.length?(fe(),de("image",{key:0,"xlink:href":v(o).mergedConfig.img[j%v(o).mergedConfig.img.length],width:v(o).mergedConfig.imgSideLength,height:v(o).mergedConfig.imgSideLength,x:x.x-v(o).mergedConfig.imgSideLength/2,y:x.y-v(o).mergedConfig.imgSideLength},null,8,YC)):Re("",!0),v(o).mergedConfig.showValue?(fe(),de("text",{key:1,fill:v(o).mergedConfig.textColor,x:x.x,y:x.textY},ct(x.value),9,ZC)):Re("",!0)]))),128))],8,zC))],512))}},mf={install(e){e.component("DvConicalColumnChart",ex)}},tx=["id"],nx=["offset","stop-color"],rx=["id","x2"],ix=["offset","stop-color"],ox=["x","y","rx","ry","stroke-width","stroke","width","height"],sx=["stroke-width","stroke-dasharray","stroke","points"],ax=["stroke","fill","x","y"],lx={__name:"index",props:{config:{type:Object,default:()=>({})}},setup(e){const t=e,n=sn(),r=ye(null),i=ke({gradientId1:`percent-pond-gradientId1-${n}`,gradientId2:`percent-pond-gradientId2-${n}`,width:0,height:0,defaultConfig:{value:0,colors:["#3DE7C9","#00BAFF"],borderWidth:3,borderGap:3,lineDash:[5,1],textColor:"#fff",borderRadius:5,localGradient:!1,formatter:"{value}%"},mergedConfig:null}),o=Ce(()=>{if(!i.mergedConfig)return 0;const{borderWidth:j}=i.mergedConfig;return i.width-j}),s=Ce(()=>{if(!i.mergedConfig)return 0;const{borderWidth:j}=i.mergedConfig;return i.height-j}),a=Ce(()=>{const j=i.height/2;if(!i.mergedConfig)return`0, ${j} 0, ${j}`;const{borderWidth:L,borderGap:A,value:g}=i.mergedConfig,C=(i.width-(L+A)*2)/100*g;return`
        ${L+A}, ${j}
        ${L+A+C}, ${j+.001}
      `}),l=Ce(()=>{if(!i.mergedConfig)return 0;const{borderWidth:j,borderGap:L}=i.mergedConfig;return i.height-(j+L)*2}),u=Ce(()=>{if(!i.mergedConfig)return[];const{colors:j}=i.mergedConfig,L=100/(j.length-1);return j.map((A,g)=>[L*g,A])}),c=Ce(()=>i.mergedConfig&&i.mergedConfig.localGradient?i.gradientId1:i.gradientId2),f=Ce(()=>{if(!i.mergedConfig)return"100%";const{value:j}=i.mergedConfig;return`${200-j}%`}),p=Ce(()=>{if(!i.mergedConfig)return"";const{value:j,formatter:L}=i.mergedConfig;return L.replace("{value}",j)});we(()=>t.config,()=>{x()},{deep:!0}),Ue(()=>{d()});async function d(){await h(),t.config&&x()}async function h(){await on();const{clientWidth:j,clientHeight:L}=r.value;i.width=j,i.height=L}function x(){i.mergedConfig=je(Be(i.defaultConfig),t.config||{})}return(j,L)=>(fe(),de("div",{ref_key:"percentPond",ref:r,class:"dv-percent-pond"},[(fe(),de("svg",null,[ae("defs",null,[ae("linearGradient",{id:v(i).gradientId1,x1:"0%",y1:"0%",x2:"100%",y2:"0%"},[(fe(!0),de(Se,null,qe(v(u),A=>(fe(),de("stop",{key:A[0],offset:`${A[0]}%`,"stop-color":A[1]},null,8,nx))),128))],8,tx),ae("linearGradient",{id:v(i).gradientId2,x1:"0%",y1:"0%",x2:v(f),y2:"0%"},[(fe(!0),de(Se,null,qe(v(u),A=>(fe(),de("stop",{key:A[0],offset:`${A[0]}%`,"stop-color":A[1]},null,8,ix))),128))],8,rx)]),ae("rect",{x:v(i).mergedConfig?v(i).mergedConfig.borderWidth/2:"0",y:v(i).mergedConfig?v(i).mergedConfig.borderWidth/2:"0",rx:v(i).mergedConfig?v(i).mergedConfig.borderRadius:"0",ry:v(i).mergedConfig?v(i).mergedConfig.borderRadius:"0",fill:"transparent","stroke-width":v(i).mergedConfig?v(i).mergedConfig.borderWidth:"0",stroke:`url(#${v(i).gradientId1})`,width:v(o)>0?v(o):0,height:v(s)>0?v(s):0},null,8,ox),ae("polyline",{"stroke-width":v(l),"stroke-dasharray":v(i).mergedConfig?v(i).mergedConfig.lineDash.join(","):"0",stroke:`url(#${v(c)})`,points:v(a)},null,8,sx),ae("text",{stroke:v(i).mergedConfig?v(i).mergedConfig.textColor:"#fff",fill:v(i).mergedConfig?v(i).mergedConfig.textColor:"#fff",x:v(i).width/2,y:v(i).height/2},ct(v(p)),9,ax)]))],512))}},vf={install(e){e.component("DvPercentPond",lx)}};function b0(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ux(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}),n}var fa={},_0={exports:{}};(function(e){function t(n){return n&&n.__esModule?n:{default:n}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(_0);var We=_0.exports,yf={},bf={exports:{}},_f={exports:{}},Cf={exports:{}},xf;function wt(){return xf||(xf=1,function(e){function t(n){"@babel/helpers - typeof";return e.exports=t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(Cf)),Cf.exports}var Ef={exports:{}},Sf;function cx(){return Sf||(Sf=1,function(e){var t=wt().default;function n(r,i){if(t(r)!="object"||!r)return r;var o=r[Symbol.toPrimitive];if(o!==void 0){var s=o.call(r,i||"default");if(t(s)!="object")return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(Ef)),Ef.exports}var wf;function fx(){return wf||(wf=1,function(e){var t=wt().default,n=cx();function r(i){var o=n(i,"string");return t(o)=="symbol"?o:String(o)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(_f)),_f.exports}var Tf;function an(){return Tf||(Tf=1,function(e){var t=fx();function n(r,i,o){return i=t(i),i in r?Object.defineProperty(r,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[i]=o,r}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(bf)),bf.exports}var Of={exports:{}},Pf={exports:{}},Af={exports:{}},If;function C0(){return If||(If=1,function(e){function t(n,r){(r==null||r>n.length)&&(r=n.length);for(var i=0,o=new Array(r);i<r;i++)o[i]=n[i];return o}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(Af)),Af.exports}var kf;function dx(){return kf||(kf=1,function(e){var t=C0();function n(r){if(Array.isArray(r))return t(r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(Pf)),Pf.exports}var $f={exports:{}},Nf;function px(){return Nf||(Nf=1,function(e){function t(n){if(typeof Symbol<"u"&&n[Symbol.iterator]!=null||n["@@iterator"]!=null)return Array.from(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}($f)),$f.exports}var Mf={exports:{}},Rf;function x0(){return Rf||(Rf=1,function(e){var t=C0();function n(r,i){if(r){if(typeof r=="string")return t(r,i);var o=Object.prototype.toString.call(r).slice(8,-1);if(o==="Object"&&r.constructor&&(o=r.constructor.name),o==="Map"||o==="Set")return Array.from(r);if(o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return t(r,i)}}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(Mf)),Mf.exports}var Lf={exports:{}},Df;function hx(){return Df||(Df=1,function(e){function t(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(Lf)),Lf.exports}var Ff;function pt(){return Ff||(Ff=1,function(e){var t=dx(),n=px(),r=x0(),i=hx();function o(s){return t(s)||n(s)||r(s)||i()}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports}(Of)),Of.exports}var jf={exports:{}},Bf;function mo(){return Bf||(Bf=1,function(e){function t(n,r){if(!(n instanceof r))throw new TypeError("Cannot call a class as a function")}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(jf)),jf.exports}const vo=ux(iC);var Vf={},Hf={},Gf={exports:{}},Uf={exports:{}},Wf;function gx(){return Wf||(Wf=1,function(e){function t(n){if(Array.isArray(n))return n}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(Uf)),Uf.exports}var Xf={exports:{}},Kf;function mx(){return Kf||(Kf=1,function(e){function t(n,r){var i=n==null?null:typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(i!=null){var o,s,a,l,u=[],c=!0,f=!1;try{if(a=(i=i.call(n)).next,r===0){if(Object(i)!==i)return;c=!1}else for(;!(c=(o=a.call(i)).done)&&(u.push(o.value),u.length!==r);c=!0);}catch(p){f=!0,s=p}finally{try{if(!c&&i.return!=null&&(l=i.return(),Object(l)!==l))return}finally{if(f)throw s}}return u}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(Xf)),Xf.exports}var qf={exports:{}},zf;function vx(){return zf||(zf=1,function(e){function t(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(qf)),qf.exports}var Jf;function Nt(){return Jf||(Jf=1,function(e){var t=gx(),n=mx(),r=x0(),i=vx();function o(s,a){return t(s)||n(s,a)||r(s,a)||i()}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports}(Gf)),Gf.exports}var Qf;function yx(){return Qf||(Qf=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.bezierCurveToPolyline=L,e.getBezierCurveLength=A,e.default=void 0;var n=t(Nt()),r=t(pt()),i=Math.sqrt,o=Math.pow,s=Math.ceil,a=Math.abs,l=50;function u(C){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:5,R=C.length-1,T=C[0],O=C[R][2],y=C.slice(1),S=y.map(function(z,Z){var J=Z===0?T:y[Z-1][2];return c.apply(void 0,[J].concat((0,r.default)(z)))}),H=new Array(R).fill(l),I=h(S,H),M=j(I,S,y,b);return M.segmentPoints.push(O),M}function c(C,b,R,T){return function(O){var y=1-O,S=o(y,3),H=o(y,2),I=o(O,3),M=o(O,2);return[C[0]*S+3*b[0]*O*H+3*R[0]*M*y+T[0]*I,C[1]*S+3*b[1]*O*H+3*R[1]*M*y+T[1]*I]}}function f(C,b){var R=(0,n.default)(C,2),T=R[0],O=R[1],y=(0,n.default)(b,2),S=y[0],H=y[1];return i(o(T-S,2)+o(O-H,2))}function p(C){return C.reduce(function(b,R){return b+R},0)}function d(C){return C.map(function(b,R){return new Array(b.length-1).fill(0).map(function(T,O){return f(b[O],b[O+1])})})}function h(C,b){return C.map(function(R,T){var O=1/b[T];return new Array(b[T]).fill("").map(function(y,S){return R(S*O)})})}function x(C,b){return C.map(function(R){return R.map(function(T){return a(T-b)})}).map(function(R){return p(R)}).reduce(function(R,T){return R+T},0)}function j(C,b,R,T){var O=4,y=1,S=function(){var I=C.reduce(function(te,ne){return te+ne.length},0);C.forEach(function(te,ne){return te.push(R[ne][2])});var M=d(C),z=M.reduce(function(te,ne){return te+ne.length},0),Z=M.map(function(te){return p(te)}),J=p(Z),P=J/z,W=x(M,P);if(W<=T)return"break";I=s(P/T*I*1.1);var D=Z.map(function(te){return s(te/J*I)});C=h(b,D),I=C.reduce(function(te,ne){return te+ne.length},0);var V=JSON.parse(JSON.stringify(C));V.forEach(function(te,ne){return te.push(R[ne][2])}),M=d(V),z=M.reduce(function(te,ne){return te+ne.length},0),Z=M.map(function(te){return p(te)}),J=p(Z),P=J/z;var X=1/I/10;b.forEach(function(te,ne){for(var le=D[ne],ce=new Array(le).fill("").map(function(q,G){return G/D[ne]}),me=0;me<O;me++)for(var U=d([C[ne]])[0],re=U.map(function(q){return q-P}),ie=0,se=0;se<le;se++){if(se===0)return;ie+=re[se-1],ce[se]-=X*ie,ce[se]>1&&(ce[se]=1),ce[se]<0&&(ce[se]=0),C[ne][se]=te(ce[se])}}),O*=4,y++};do{var H=S();if(H==="break")break}while(O<=1025);return C=C.reduce(function(I,M){return I.concat(M)},[]),{segmentPoints:C,cycles:y,rounds:O}}function L(C){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:5;if(!C)return console.error("bezierCurveToPolyline: Missing parameters!"),!1;if(!(C instanceof Array))return console.error("bezierCurveToPolyline: Parameter bezierCurve must be an array!"),!1;if(typeof b!="number")return console.error("bezierCurveToPolyline: Parameter precision must be a number!"),!1;var R=u(C,b),T=R.segmentPoints;return T}function A(C){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:5;if(!C)return console.error("getBezierCurveLength: Missing parameters!"),!1;if(!(C instanceof Array))return console.error("getBezierCurveLength: Parameter bezierCurve must be an array!"),!1;if(typeof b!="number")return console.error("getBezierCurveLength: Parameter precision must be a number!"),!1;var R=u(C,b),T=R.segmentPoints,O=d([T])[0],y=p(O);return y}var g=L;e.default=g}(Hf)),Hf}var Yf={},Zf;function bx(){return Zf||(Zf=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t(Nt()),r=t(pt());function i(u){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:.25,p=arguments.length>3&&arguments[3]!==void 0?arguments[3]:.25;if(!(u instanceof Array))return console.error("polylineToBezierCurve: Parameter polyline must be an array!"),!1;if(u.length<=2)return console.error("polylineToBezierCurve: Converting to a curve requires at least 3 points!"),!1;var d=u[0],h=u.length-1,x=new Array(h).fill(0).map(function(j,L){return[].concat((0,r.default)(o(u,L,c,f,p)),[u[L+1]])});return c&&s(x,d),x.unshift(u[0]),x}function o(u,c){var f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,p=arguments.length>3&&arguments[3]!==void 0?arguments[3]:.25,d=arguments.length>4&&arguments[4]!==void 0?arguments[4]:.25,h=u.length;if(!(h<3||c>=h)){var x=c-1;x<0&&(x=f?h+x:0);var j=c+1;j>=h&&(j=f?j-h:h-1);var L=c+2;L>=h&&(L=f?L-h:h-1);var A=u[x],g=u[c],C=u[j],b=u[L];return[[g[0]+p*(C[0]-A[0]),g[1]+p*(C[1]-A[1])],[C[0]-d*(b[0]-g[0]),C[1]-d*(b[1]-g[1])]]}}function s(u,c){var f=u[0],p=u.slice(-1)[0];return u.push([a(p[1],p[2]),a(f[0],c),c]),u}function a(u,c){var f=(0,n.default)(u,2),p=f[0],d=f[1],h=(0,n.default)(c,2),x=h[0],j=h[1],L=x-p,A=j-d;return[x+L,j+A]}var l=i;e.default=l}(Yf)),Yf}var ed;function Cu(){return ed||(ed=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"bezierCurveToPolyline",{enumerable:!0,get:function(){return n.bezierCurveToPolyline}}),Object.defineProperty(e,"getBezierCurveLength",{enumerable:!0,get:function(){return n.getBezierCurveLength}}),Object.defineProperty(e,"polylineToBezierCurve",{enumerable:!0,get:function(){return r.default}}),e.default=void 0;var n=yx(),r=t(bx()),i={bezierCurveToPolyline:n.bezierCurveToPolyline,getBezierCurveLength:n.getBezierCurveLength,polylineToBezierCurve:r.default};e.default=i}(Vf)),Vf}var td={},nd;function mt(){return nd||(nd=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.deepClone=p,e.eliminateBlur=d,e.checkPointIsInCircle=h,e.getTwoPointDistance=x,e.checkPointIsInPolygon=j,e.checkPointIsInSector=L,e.checkPointIsNearPolyline=g,e.checkPointIsInRect=C,e.getRotatePointPos=b,e.getScalePointPos=R,e.getTranslatePointPos=T,e.getDistanceBetweenPointAndLine=O,e.getCircleRadianPoint=y,e.getRegularPolygonPoints=S,e.default=void 0;var n=t(pt()),r=t(Nt()),i=t(wt()),o=Math.abs,s=Math.sqrt,a=Math.sin,l=Math.cos,u=Math.max,c=Math.min,f=Math.PI;function p(I){var M=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!I)return I;var z=JSON.parse,Z=JSON.stringify;if(!M)return z(Z(I));var J=I instanceof Array?[]:{};if(I&&(0,i.default)(I)==="object")for(var P in I)I.hasOwnProperty(P)&&(I[P]&&(0,i.default)(I[P])==="object"?J[P]=p(I[P],!0):J[P]=I[P]);return J}function d(I){return I.map(function(M){var z=(0,r.default)(M,2),Z=z[0],J=z[1];return[parseInt(Z)+.5,parseInt(J)+.5]})}function h(I,M,z,Z){return x(I,[M,z])<=Z}function x(I,M){var z=(0,r.default)(I,2),Z=z[0],J=z[1],P=(0,r.default)(M,2),W=P[0],D=P[1],V=o(Z-W),X=o(J-D);return s(V*V+X*X)}function j(I,M){for(var z=0,Z=(0,r.default)(I,2),J=Z[0],P=Z[1],W=M.length,D=1,V=M[0];D<=W;D++){var X=M[D%W];if(J>c(V[0],X[0])&&J<=u(V[0],X[0])&&P<=u(V[1],X[1])&&V[0]!==X[0]){var te=(J-V[0])*(X[1]-V[1])/(X[0]-V[0])+V[1];(V[1]===X[1]||P<=te)&&z++}V=X}return z%2===1}function L(I,M,z,Z,J,P,W){if(!I||x(I,[M,z])>Z)return!1;if(!W){var D=p([P,J]),V=(0,r.default)(D,2);J=V[0],P=V[1]}var X=J>P;if(X){var te=[P,J];J=te[0],P=te[1]}var ne=P-J;if(ne>=f*2)return!0;var le=(0,r.default)(I,2),ce=le[0],me=le[1],U=y(M,z,Z,J),re=(0,r.default)(U,2),ie=re[0],se=re[1],q=y(M,z,Z,P),G=(0,r.default)(q,2),m=G[0],_=G[1],N=[ce-M,me-z],K=[ie-M,se-z],w=[m-M,_-z],B=ne>f;if(B){var E=p([w,K]),k=(0,r.default)(E,2);K=k[0],w=k[1]}var $=A(K,N)&&!A(w,N);return B&&($=!$),X&&($=!$),$}function A(I,M){var z=(0,r.default)(I,2),Z=z[0],J=z[1],P=(0,r.default)(M,2),W=P[0],D=P[1];return-J*W+Z*D>0}function g(I,M,z){var Z=z/2,J=M.map(function(D){var V=(0,r.default)(D,2),X=V[0],te=V[1];return[X,te-Z]}),P=M.map(function(D){var V=(0,r.default)(D,2),X=V[0],te=V[1];return[X,te+Z]}),W=[].concat((0,n.default)(J),(0,n.default)(P.reverse()));return j(I,W)}function C(I,M,z,Z,J){var P=(0,r.default)(I,2),W=P[0],D=P[1];return!(W<M||D<z||W>M+Z||D>z+J)}function b(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,M=arguments.length>1?arguments[1]:void 0,z=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[0,0];if(!M)return!1;if(I%360===0)return M;var Z=(0,r.default)(M,2),J=Z[0],P=Z[1],W=(0,r.default)(z,2),D=W[0],V=W[1];return I*=f/180,[(J-D)*l(I)-(P-V)*a(I)+D,(J-D)*a(I)+(P-V)*l(I)+V]}function R(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[1,1],M=arguments.length>1?arguments[1]:void 0,z=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[0,0];if(!M)return!1;if(I===1)return M;var Z=(0,r.default)(M,2),J=Z[0],P=Z[1],W=(0,r.default)(z,2),D=W[0],V=W[1],X=(0,r.default)(I,2),te=X[0],ne=X[1],le=J-D,ce=P-V;return[le*te+D,ce*ne+V]}function T(I,M){if(!I||!M)return!1;var z=(0,r.default)(M,2),Z=z[0],J=z[1],P=(0,r.default)(I,2),W=P[0],D=P[1];return[Z+W,J+D]}function O(I,M,z){if(!I||!M||!z)return!1;var Z=(0,r.default)(I,2),J=Z[0],P=Z[1],W=(0,r.default)(M,2),D=W[0],V=W[1],X=(0,r.default)(z,2),te=X[0],ne=X[1],le=ne-V,ce=D-te,me=V*(te-D)-D*(ne-V),U=o(le*J+ce*P+me),re=s(le*le+ce*ce);return U/re}function y(I,M,z,Z){return[I+l(Z)*z,M+a(Z)*z]}function S(I,M,z,Z){var J=arguments.length>4&&arguments[4]!==void 0?arguments[4]:f*-.5,P=f*2/Z,W=new Array(Z).fill("").map(function(D,V){return V*P+J});return W.map(function(D){return y(I,M,z,D)})}var H={deepClone:p,eliminateBlur:d,checkPointIsInCircle:h,checkPointIsInPolygon:j,checkPointIsInSector:L,checkPointIsNearPolyline:g,getTwoPointDistance:x,getRotatePointPos:b,getScalePointPos:R,getTranslatePointPos:T,getCircleRadianPoint:y,getRegularPolygonPoints:S,getDistanceBetweenPointAndLine:O};e.default=H}(td)),td}var rd={},id={},od;function _x(){return od||(od=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.drawPolylinePath=r,e.drawBezierCurvePath=i,e.default=void 0;var n=t(pt());function r(s,a){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!s||a.length<2)return!1;l&&s.beginPath(),a.forEach(function(c,f){return c&&(f===0?s.moveTo.apply(s,(0,n.default)(c)):s.lineTo.apply(s,(0,n.default)(c)))}),u&&s.closePath()}function i(s,a){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,c=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(!s||!a)return!1;u&&s.beginPath(),l&&s.moveTo.apply(s,(0,n.default)(l)),a.forEach(function(f){return f&&s.bezierCurveTo.apply(s,(0,n.default)(f[0]).concat((0,n.default)(f[1]),(0,n.default)(f[2])))}),c&&s.closePath()}var o={drawPolylinePath:r,drawBezierCurvePath:i};e.default=o}(id)),id}var sd;function xu(){return sd||(sd=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.extendNewGraph=R,e.default=e.text=e.bezierCurve=e.smoothline=e.polyline=e.regPolygon=e.sector=e.arc=e.ring=e.rect=e.ellipse=e.circle=void 0;var n=t(pt()),r=t(Nt()),i=t(Cu()),o=mt(),s=_x(),a=i.default.polylineToBezierCurve,l=i.default.bezierCurveToPolyline,u={shape:{rx:0,ry:0,r:0},validator:function(T){var O=T.shape,y=O.rx,S=O.ry,H=O.r;return typeof y!="number"||typeof S!="number"||typeof H!="number"?(console.error("Circle shape configuration is abnormal!"),!1):!0},draw:function(T,O){var y=T.ctx,S=O.shape;y.beginPath();var H=S.rx,I=S.ry,M=S.r;y.arc(H,I,M>0?M:.01,0,Math.PI*2),y.fill(),y.stroke(),y.closePath()},hoverCheck:function(T,O){var y=O.shape,S=y.rx,H=y.ry,I=y.r;return(0,o.checkPointIsInCircle)(T,S,H,I)},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.rx,I=y.ry;S.graphCenter=[H,I]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape;this.attr("shape",{rx:H.rx+y,ry:H.ry+S})}};e.circle=u;var c={shape:{rx:0,ry:0,hr:0,vr:0},validator:function(T){var O=T.shape,y=O.rx,S=O.ry,H=O.hr,I=O.vr;return typeof y!="number"||typeof S!="number"||typeof H!="number"||typeof I!="number"?(console.error("Ellipse shape configuration is abnormal!"),!1):!0},draw:function(T,O){var y=T.ctx,S=O.shape;y.beginPath();var H=S.rx,I=S.ry,M=S.hr,z=S.vr;y.ellipse(H,I,M>0?M:.01,z>0?z:.01,0,0,Math.PI*2),y.fill(),y.stroke(),y.closePath()},hoverCheck:function(T,O){var y=O.shape,S=y.rx,H=y.ry,I=y.hr,M=y.vr,z=Math.max(I,M),Z=Math.min(I,M),J=Math.sqrt(z*z-Z*Z),P=[S-J,H],W=[S+J,H],D=(0,o.getTwoPointDistance)(T,P)+(0,o.getTwoPointDistance)(T,W);return D<=2*z},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.rx,I=y.ry;S.graphCenter=[H,I]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape;this.attr("shape",{rx:H.rx+y,ry:H.ry+S})}};e.ellipse=c;var f={shape:{x:0,y:0,w:0,h:0},validator:function(T){var O=T.shape,y=O.x,S=O.y,H=O.w,I=O.h;return typeof y!="number"||typeof S!="number"||typeof H!="number"||typeof I!="number"?(console.error("Rect shape configuration is abnormal!"),!1):!0},draw:function(T,O){var y=T.ctx,S=O.shape;y.beginPath();var H=S.x,I=S.y,M=S.w,z=S.h;y.rect(H,I,M,z),y.fill(),y.stroke(),y.closePath()},hoverCheck:function(T,O){var y=O.shape,S=y.x,H=y.y,I=y.w,M=y.h;return(0,o.checkPointIsInRect)(T,S,H,I,M)},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.x,I=y.y,M=y.w,z=y.h;S.graphCenter=[H+M/2,I+z/2]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape;this.attr("shape",{x:H.x+y,y:H.y+S})}};e.rect=f;var p={shape:{rx:0,ry:0,r:0},validator:function(T){var O=T.shape,y=O.rx,S=O.ry,H=O.r;return typeof y!="number"||typeof S!="number"||typeof H!="number"?(console.error("Ring shape configuration is abnormal!"),!1):!0},draw:function(T,O){var y=T.ctx,S=O.shape;y.beginPath();var H=S.rx,I=S.ry,M=S.r;y.arc(H,I,M>0?M:.01,0,Math.PI*2),y.stroke(),y.closePath()},hoverCheck:function(T,O){var y=O.shape,S=O.style,H=y.rx,I=y.ry,M=y.r,z=S.lineWidth,Z=z/2,J=M-Z,P=M+Z,W=(0,o.getTwoPointDistance)(T,[H,I]);return W>=J&&W<=P},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.rx,I=y.ry;S.graphCenter=[H,I]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape;this.attr("shape",{rx:H.rx+y,ry:H.ry+S})}};e.ring=p;var d={shape:{rx:0,ry:0,r:0,startAngle:0,endAngle:0,clockWise:!0},validator:function(T){var O=T.shape,y=["rx","ry","r","startAngle","endAngle"];return y.find(function(S){return typeof O[S]!="number"})?(console.error("Arc shape configuration is abnormal!"),!1):!0},draw:function(T,O){var y=T.ctx,S=O.shape;y.beginPath();var H=S.rx,I=S.ry,M=S.r,z=S.startAngle,Z=S.endAngle,J=S.clockWise;y.arc(H,I,M>0?M:.001,z,Z,!J),y.stroke(),y.closePath()},hoverCheck:function(T,O){var y=O.shape,S=O.style,H=y.rx,I=y.ry,M=y.r,z=y.startAngle,Z=y.endAngle,J=y.clockWise,P=S.lineWidth,W=P/2,D=M-W,V=M+W;return!(0,o.checkPointIsInSector)(T,H,I,D,z,Z,J)&&(0,o.checkPointIsInSector)(T,H,I,V,z,Z,J)},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.rx,I=y.ry;S.graphCenter=[H,I]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape;this.attr("shape",{rx:H.rx+y,ry:H.ry+S})}};e.arc=d;var h={shape:{rx:0,ry:0,r:0,startAngle:0,endAngle:0,clockWise:!0},validator:function(T){var O=T.shape,y=["rx","ry","r","startAngle","endAngle"];return y.find(function(S){return typeof O[S]!="number"})?(console.error("Sector shape configuration is abnormal!"),!1):!0},draw:function(T,O){var y=T.ctx,S=O.shape;y.beginPath();var H=S.rx,I=S.ry,M=S.r,z=S.startAngle,Z=S.endAngle,J=S.clockWise;y.arc(H,I,M>0?M:.01,z,Z,!J),y.lineTo(H,I),y.closePath(),y.stroke(),y.fill()},hoverCheck:function(T,O){var y=O.shape,S=y.rx,H=y.ry,I=y.r,M=y.startAngle,z=y.endAngle,Z=y.clockWise;return(0,o.checkPointIsInSector)(T,S,H,I,M,z,Z)},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.rx,I=y.ry;S.graphCenter=[H,I]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape,I=H.rx,M=H.ry;this.attr("shape",{rx:I+y,ry:M+S})}};e.sector=h;var x={shape:{rx:0,ry:0,r:0,side:0},validator:function(T){var O=T.shape,y=O.side,S=["rx","ry","r","side"];return S.find(function(H){return typeof O[H]!="number"})?(console.error("RegPolygon shape configuration is abnormal!"),!1):y<3?(console.error("RegPolygon at least trigon!"),!1):!0},draw:function(T,O){var y=T.ctx,S=O.shape,H=O.cache;y.beginPath();var I=S.rx,M=S.ry,z=S.r,Z=S.side;if(!H.points||H.rx!==I||H.ry!==M||H.r!==z||H.side!==Z){var J=(0,o.getRegularPolygonPoints)(I,M,z,Z);Object.assign(H,{points:J,rx:I,ry:M,r:z,side:Z})}var P=H.points;(0,s.drawPolylinePath)(y,P),y.closePath(),y.stroke(),y.fill()},hoverCheck:function(T,O){var y=O.cache,S=y.points;return(0,o.checkPointIsInPolygon)(T,S)},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.rx,I=y.ry;S.graphCenter=[H,I]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape,I=O.cache,M=H.rx,z=H.ry;I.rx+=y,I.ry+=S,this.attr("shape",{rx:M+y,ry:z+S}),I.points=I.points.map(function(Z){var J=(0,r.default)(Z,2),P=J[0],W=J[1];return[P+y,W+S]})}};e.regPolygon=x;var j={shape:{points:[],close:!1},validator:function(T){var O=T.shape,y=O.points;return y instanceof Array?!0:(console.error("Polyline points should be an array!"),!1)},draw:function(T,O){var y=T.ctx,S=O.shape,H=O.style.lineWidth;y.beginPath();var I=S.points,M=S.close;H===1&&(I=(0,o.eliminateBlur)(I)),(0,s.drawPolylinePath)(y,I),M&&(y.closePath(),y.fill()),y.stroke()},hoverCheck:function(T,O){var y=O.shape,S=O.style,H=y.points,I=y.close,M=S.lineWidth;return I?(0,o.checkPointIsInPolygon)(T,H):(0,o.checkPointIsNearPolyline)(T,H,M)},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.points;S.graphCenter=H[0]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape,I=H.points,M=I.map(function(z){var Z=(0,r.default)(z,2),J=Z[0],P=Z[1];return[J+y,P+S]});this.attr("shape",{points:M})}};e.polyline=j;var L={shape:{points:[],close:!1},validator:function(T){var O=T.shape,y=O.points;return y instanceof Array?!0:(console.error("Smoothline points should be an array!"),!1)},draw:function(T,O){var y=T.ctx,S=O.shape,H=O.cache,I=S.points,M=S.close;if(!H.points||H.points.toString()!==I.toString()){var z=a(I,M),Z=l(z);Object.assign(H,{points:(0,o.deepClone)(I,!0),bezierCurve:z,hoverPoints:Z})}var J=H.bezierCurve;y.beginPath(),(0,s.drawBezierCurvePath)(y,J.slice(1),J[0]),M&&(y.closePath(),y.fill()),y.stroke()},hoverCheck:function(T,O){var y=O.cache,S=O.shape,H=O.style,I=y.hoverPoints,M=S.close,z=H.lineWidth;return M?(0,o.checkPointIsInPolygon)(T,I):(0,o.checkPointIsNearPolyline)(T,I,z)},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.points;S.graphCenter=H[0]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape,I=O.cache,M=H.points,z=M.map(function(D){var V=(0,r.default)(D,2),X=V[0],te=V[1];return[X+y,te+S]});I.points=z;var Z=(0,r.default)(I.bezierCurve[0],2),J=Z[0],P=Z[1],W=I.bezierCurve.slice(1);I.bezierCurve=[[J+y,P+S]].concat((0,n.default)(W.map(function(D){return D.map(function(V){var X=(0,r.default)(V,2),te=X[0],ne=X[1];return[te+y,ne+S]})}))),I.hoverPoints=I.hoverPoints.map(function(D){var V=(0,r.default)(D,2),X=V[0],te=V[1];return[X+y,te+S]}),this.attr("shape",{points:z})}};e.smoothline=L;var A={shape:{points:[],close:!1},validator:function(T){var O=T.shape,y=O.points;return y instanceof Array?!0:(console.error("BezierCurve points should be an array!"),!1)},draw:function(T,O){var y=T.ctx,S=O.shape,H=O.cache,I=S.points,M=S.close;if(!H.points||H.points.toString()!==I.toString()){var z=l(I,20);Object.assign(H,{points:(0,o.deepClone)(I,!0),hoverPoints:z})}y.beginPath(),(0,s.drawBezierCurvePath)(y,I.slice(1),I[0]),M&&(y.closePath(),y.fill()),y.stroke()},hoverCheck:function(T,O){var y=O.cache,S=O.shape,H=O.style,I=y.hoverPoints,M=S.close,z=H.lineWidth;return M?(0,o.checkPointIsInPolygon)(T,I):(0,o.checkPointIsNearPolyline)(T,I,z)},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.points;S.graphCenter=H[0]},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape,I=O.cache,M=H.points,z=(0,r.default)(M[0],2),Z=z[0],J=z[1],P=M.slice(1),W=[[Z+y,J+S]].concat((0,n.default)(P.map(function(D){return D.map(function(V){var X=(0,r.default)(V,2),te=X[0],ne=X[1];return[te+y,ne+S]})})));I.points=W,I.hoverPoints=I.hoverPoints.map(function(D){var V=(0,r.default)(D,2),X=V[0],te=V[1];return[X+y,te+S]}),this.attr("shape",{points:W})}};e.bezierCurve=A;var g={shape:{content:"",position:[],maxWidth:void 0,rowGap:0},validator:function(T){var O=T.shape,y=O.content,S=O.position,H=O.rowGap;return typeof y!="string"?(console.error("Text content should be a string!"),!1):S instanceof Array?typeof H!="number"?(console.error("Text rowGap should be a number!"),!1):!0:(console.error("Text position should be an array!"),!1)},draw:function(T,O){var y=T.ctx,S=O.shape,H=S.content,I=S.position,M=S.maxWidth,z=S.rowGap,Z=y.textBaseline,J=y.font,P=parseInt(J.replace(/\D/g,"")),W=I,D=(0,r.default)(W,2),V=D[0],X=D[1];H=H.split(`
`);var te=H.length,ne=P+z,le=te*ne-z,ce=0;Z==="middle"&&(ce=le/2,X+=P/2),Z==="bottom"&&(ce=le,X+=P),I=new Array(te).fill(0).map(function(me,U){return[V,X+U*ne-ce]}),y.beginPath(),H.forEach(function(me,U){y.fillText.apply(y,[me].concat((0,n.default)(I[U]),[M])),y.strokeText.apply(y,[me].concat((0,n.default)(I[U]),[M]))}),y.closePath()},hoverCheck:function(T,O){return O.shape,O.style,!1},setGraphCenter:function(T,O){var y=O.shape,S=O.style,H=y.position;S.graphCenter=(0,n.default)(H)},move:function(T,O){var y=T.movementX,S=T.movementY,H=O.shape,I=(0,r.default)(H.position,2),M=I[0],z=I[1];this.attr("shape",{position:[M+y,z+S]})}};e.text=g;var C=new Map([["circle",u],["ellipse",c],["rect",f],["ring",p],["arc",d],["sector",h],["regPolygon",x],["polyline",j],["smoothline",L],["bezierCurve",A],["text",g]]),b=C;e.default=b;function R(T,O){if(!T||!O){console.error("ExtendNewGraph Missing Parameters!");return}if(!O.shape){console.error("Required attribute of shape to extendNewGraph!");return}if(!O.validator){console.error("Required function of validator to extendNewGraph!");return}if(!O.draw){console.error("Required function of draw to extendNewGraph!");return}C.set(T,O)}}(rd)),rd}var ad={},ld={exports:{}},ud;function Cx(){return ud||(ud=1,function(e){var t=wt().default;function n(){e.exports=n=function(){return i},e.exports.__esModule=!0,e.exports.default=e.exports;var r,i={},o=Object.prototype,s=o.hasOwnProperty,a=Object.defineProperty||function(D,V,X){D[V]=X.value},l=typeof Symbol=="function"?Symbol:{},u=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",f=l.toStringTag||"@@toStringTag";function p(D,V,X){return Object.defineProperty(D,V,{value:X,enumerable:!0,configurable:!0,writable:!0}),D[V]}try{p({},"")}catch{p=function(D,V,X){return D[V]=X}}function d(D,V,X,te){var ne=V&&V.prototype instanceof C?V:C,le=Object.create(ne.prototype),ce=new P(te||[]);return a(le,"_invoke",{value:M(D,X,ce)}),le}function h(D,V,X){try{return{type:"normal",arg:D.call(V,X)}}catch(te){return{type:"throw",arg:te}}}i.wrap=d;var x="suspendedStart",j="suspendedYield",L="executing",A="completed",g={};function C(){}function b(){}function R(){}var T={};p(T,u,function(){return this});var O=Object.getPrototypeOf,y=O&&O(O(W([])));y&&y!==o&&s.call(y,u)&&(T=y);var S=R.prototype=C.prototype=Object.create(T);function H(D){["next","throw","return"].forEach(function(V){p(D,V,function(X){return this._invoke(V,X)})})}function I(D,V){function X(ne,le,ce,me){var U=h(D[ne],D,le);if(U.type!=="throw"){var re=U.arg,ie=re.value;return ie&&t(ie)=="object"&&s.call(ie,"__await")?V.resolve(ie.__await).then(function(se){X("next",se,ce,me)},function(se){X("throw",se,ce,me)}):V.resolve(ie).then(function(se){re.value=se,ce(re)},function(se){return X("throw",se,ce,me)})}me(U.arg)}var te;a(this,"_invoke",{value:function(ne,le){function ce(){return new V(function(me,U){X(ne,le,me,U)})}return te=te?te.then(ce,ce):ce()}})}function M(D,V,X){var te=x;return function(ne,le){if(te===L)throw new Error("Generator is already running");if(te===A){if(ne==="throw")throw le;return{value:r,done:!0}}for(X.method=ne,X.arg=le;;){var ce=X.delegate;if(ce){var me=z(ce,X);if(me){if(me===g)continue;return me}}if(X.method==="next")X.sent=X._sent=X.arg;else if(X.method==="throw"){if(te===x)throw te=A,X.arg;X.dispatchException(X.arg)}else X.method==="return"&&X.abrupt("return",X.arg);te=L;var U=h(D,V,X);if(U.type==="normal"){if(te=X.done?A:j,U.arg===g)continue;return{value:U.arg,done:X.done}}U.type==="throw"&&(te=A,X.method="throw",X.arg=U.arg)}}}function z(D,V){var X=V.method,te=D.iterator[X];if(te===r)return V.delegate=null,X==="throw"&&D.iterator.return&&(V.method="return",V.arg=r,z(D,V),V.method==="throw")||X!=="return"&&(V.method="throw",V.arg=new TypeError("The iterator does not provide a '"+X+"' method")),g;var ne=h(te,D.iterator,V.arg);if(ne.type==="throw")return V.method="throw",V.arg=ne.arg,V.delegate=null,g;var le=ne.arg;return le?le.done?(V[D.resultName]=le.value,V.next=D.nextLoc,V.method!=="return"&&(V.method="next",V.arg=r),V.delegate=null,g):le:(V.method="throw",V.arg=new TypeError("iterator result is not an object"),V.delegate=null,g)}function Z(D){var V={tryLoc:D[0]};1 in D&&(V.catchLoc=D[1]),2 in D&&(V.finallyLoc=D[2],V.afterLoc=D[3]),this.tryEntries.push(V)}function J(D){var V=D.completion||{};V.type="normal",delete V.arg,D.completion=V}function P(D){this.tryEntries=[{tryLoc:"root"}],D.forEach(Z,this),this.reset(!0)}function W(D){if(D||D===""){var V=D[u];if(V)return V.call(D);if(typeof D.next=="function")return D;if(!isNaN(D.length)){var X=-1,te=function ne(){for(;++X<D.length;)if(s.call(D,X))return ne.value=D[X],ne.done=!1,ne;return ne.value=r,ne.done=!0,ne};return te.next=te}}throw new TypeError(t(D)+" is not iterable")}return b.prototype=R,a(S,"constructor",{value:R,configurable:!0}),a(R,"constructor",{value:b,configurable:!0}),b.displayName=p(R,f,"GeneratorFunction"),i.isGeneratorFunction=function(D){var V=typeof D=="function"&&D.constructor;return!!V&&(V===b||(V.displayName||V.name)==="GeneratorFunction")},i.mark=function(D){return Object.setPrototypeOf?Object.setPrototypeOf(D,R):(D.__proto__=R,p(D,f,"GeneratorFunction")),D.prototype=Object.create(S),D},i.awrap=function(D){return{__await:D}},H(I.prototype),p(I.prototype,c,function(){return this}),i.AsyncIterator=I,i.async=function(D,V,X,te,ne){ne===void 0&&(ne=Promise);var le=new I(d(D,V,X,te),ne);return i.isGeneratorFunction(V)?le:le.next().then(function(ce){return ce.done?ce.value:le.next()})},H(S),p(S,f,"Generator"),p(S,u,function(){return this}),p(S,"toString",function(){return"[object Generator]"}),i.keys=function(D){var V=Object(D),X=[];for(var te in V)X.push(te);return X.reverse(),function ne(){for(;X.length;){var le=X.pop();if(le in V)return ne.value=le,ne.done=!1,ne}return ne.done=!0,ne}},i.values=W,P.prototype={constructor:P,reset:function(D){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(J),!D)for(var V in this)V.charAt(0)==="t"&&s.call(this,V)&&!isNaN(+V.slice(1))&&(this[V]=r)},stop:function(){this.done=!0;var D=this.tryEntries[0].completion;if(D.type==="throw")throw D.arg;return this.rval},dispatchException:function(D){if(this.done)throw D;var V=this;function X(U,re){return le.type="throw",le.arg=D,V.next=U,re&&(V.method="next",V.arg=r),!!re}for(var te=this.tryEntries.length-1;te>=0;--te){var ne=this.tryEntries[te],le=ne.completion;if(ne.tryLoc==="root")return X("end");if(ne.tryLoc<=this.prev){var ce=s.call(ne,"catchLoc"),me=s.call(ne,"finallyLoc");if(ce&&me){if(this.prev<ne.catchLoc)return X(ne.catchLoc,!0);if(this.prev<ne.finallyLoc)return X(ne.finallyLoc)}else if(ce){if(this.prev<ne.catchLoc)return X(ne.catchLoc,!0)}else{if(!me)throw new Error("try statement without catch or finally");if(this.prev<ne.finallyLoc)return X(ne.finallyLoc)}}}},abrupt:function(D,V){for(var X=this.tryEntries.length-1;X>=0;--X){var te=this.tryEntries[X];if(te.tryLoc<=this.prev&&s.call(te,"finallyLoc")&&this.prev<te.finallyLoc){var ne=te;break}}ne&&(D==="break"||D==="continue")&&ne.tryLoc<=V&&V<=ne.finallyLoc&&(ne=null);var le=ne?ne.completion:{};return le.type=D,le.arg=V,ne?(this.method="next",this.next=ne.finallyLoc,g):this.complete(le)},complete:function(D,V){if(D.type==="throw")throw D.arg;return D.type==="break"||D.type==="continue"?this.next=D.arg:D.type==="return"?(this.rval=this.arg=D.arg,this.method="return",this.next="end"):D.type==="normal"&&V&&(this.next=V),g},finish:function(D){for(var V=this.tryEntries.length-1;V>=0;--V){var X=this.tryEntries[V];if(X.finallyLoc===D)return this.complete(X.completion,X.afterLoc),J(X),g}},catch:function(D){for(var V=this.tryEntries.length-1;V>=0;--V){var X=this.tryEntries[V];if(X.tryLoc===D){var te=X.completion;if(te.type==="throw"){var ne=te.arg;J(X)}return ne}}throw new Error("illegal catch attempt")},delegateYield:function(D,V,X){return this.delegate={iterator:W(D),resultName:V,nextLoc:X},this.method==="next"&&(this.arg=r),g}},i}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(ld)),ld.exports}var Xa,cd;function xx(){if(cd)return Xa;cd=1;var e=Cx()();Xa=e;try{regeneratorRuntime=e}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}return Xa}var fd={exports:{}},dd;function Ex(){return dd||(dd=1,function(e){function t(r,i,o,s,a,l,u){try{var c=r[l](u),f=c.value}catch(p){o(p);return}c.done?i(f):Promise.resolve(f).then(s,a)}function n(r){return function(){var i=this,o=arguments;return new Promise(function(s,a){var l=r.apply(i,o);function u(f){t(l,s,a,u,c,"next",f)}function c(f){t(l,s,a,u,c,"throw",f)}u(void 0)})}}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(fd)),fd.exports}var pd={},hd;function Sx(){return hd||(hd=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t(pt()),r=t(mo()),i=vo,o=mt(),s=function d(h){(0,r.default)(this,d),this.colorProcessor(h);var x={fill:[0,0,0,1],stroke:[0,0,0,0],opacity:1,lineCap:null,lineJoin:null,lineDash:null,lineDashOffset:null,shadowBlur:0,shadowColor:[0,0,0,0],shadowOffsetX:0,shadowOffsetY:0,lineWidth:0,graphCenter:null,scale:null,rotate:null,translate:null,hoverCursor:"pointer",fontStyle:"normal",fontVarient:"normal",fontWeight:"normal",fontSize:10,fontFamily:"Arial",textAlign:"center",textBaseline:"middle",gradientColor:null,gradientType:"linear",gradientParams:null,gradientWith:"stroke",gradientStops:"auto",colors:null};Object.assign(this,x,h)};e.default=s,s.prototype.colorProcessor=function(d){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,x=h?i.getColorFromRgbValue:i.getRgbaValue,j=["fill","stroke","shadowColor"],L=Object.keys(d),A=L.filter(function(R){return j.find(function(T){return T===R})});A.forEach(function(R){return d[R]=x(d[R])});var g=d.gradientColor,C=d.colors;if(g&&(d.gradientColor=g.map(function(R){return x(R)})),C){var b=Object.keys(C);b.forEach(function(R){return C[R]=x(C[R])})}},s.prototype.initStyle=function(d){a(d,this),u(d,this),c(d,this)};function a(d,h){d.save();var x=h.graphCenter,j=h.rotate,L=h.scale,A=h.translate;x instanceof Array&&(d.translate.apply(d,(0,n.default)(x)),j&&d.rotate(j*Math.PI/180),L instanceof Array&&d.scale.apply(d,(0,n.default)(L)),A&&d.translate.apply(d,(0,n.default)(A)),d.translate(-x[0],-x[1]))}var l=["lineCap","lineJoin","lineDashOffset","shadowOffsetX","shadowOffsetY","lineWidth","textAlign","textBaseline"];function u(d,h){var x=h.fill,j=h.stroke,L=h.shadowColor,A=h.opacity;l.forEach(function(S){(S||typeof S=="number")&&(d[S]=h[S])}),x=(0,n.default)(x),j=(0,n.default)(j),L=(0,n.default)(L),x[3]*=A,j[3]*=A,L[3]*=A,d.fillStyle=(0,i.getColorFromRgbValue)(x),d.strokeStyle=(0,i.getColorFromRgbValue)(j),d.shadowColor=(0,i.getColorFromRgbValue)(L);var g=h.lineDash,C=h.shadowBlur;g&&(g=g.map(function(S){return S>=0?S:0}),d.setLineDash(g)),typeof C=="number"&&(d.shadowBlur=C>0?C:.001);var b=h.fontStyle,R=h.fontVarient,T=h.fontWeight,O=h.fontSize,y=h.fontFamily;d.font=b+" "+R+" "+T+" "+O+"px "+y}function c(d,h){if(f(h)){var x=h.gradientColor,j=h.gradientParams,L=h.gradientType,A=h.gradientWith,g=h.gradientStops,C=h.opacity;x=x.map(function(R){var T=R[3]*C,O=(0,n.default)(R);return O[3]=T,O}),x=x.map(function(R){return(0,i.getColorFromRgbValue)(R)}),g==="auto"&&(g=p(x));var b=d["create".concat(L.slice(0,1).toUpperCase()+L.slice(1),"Gradient")].apply(d,(0,n.default)(j));g.forEach(function(R,T){return b.addColorStop(R,x[T])}),d["".concat(A,"Style")]=b}}function f(d){var h=d.gradientColor,x=d.gradientParams,j=d.gradientType,L=d.gradientWith,A=d.gradientStops;if(!h||!x)return!1;if(h.length===1)return console.warn("The gradient needs to provide at least two colors"),!1;if(j!=="linear"&&j!=="radial")return console.warn("GradientType only supports linear or radial, current value is "+j),!1;var g=x.length;return j==="linear"&&g!==4||j==="radial"&&g!==6?(console.warn("The expected length of gradientParams is "+(j==="linear"?"4":"6")),!1):L!=="fill"&&L!=="stroke"?(console.warn("GradientWith only supports fill or stroke, current value is "+L),!1):A!=="auto"&&!(A instanceof Array)?(console.warn("gradientStops only supports 'auto' or Number Array ([0, .5, 1]), current value is "+A),!1):!0}function p(d){var h=1/(d.length-1);return d.map(function(x,j){return h*j})}s.prototype.restoreTransform=function(d){d.restore()},s.prototype.update=function(d){this.colorProcessor(d),Object.assign(this,d)},s.prototype.getStyle=function(){var d=(0,o.deepClone)(this,!0);return this.colorProcessor(d,!0),d}}(pd)),pd}var gd={},md={},vd;function wx(){return vd||(vd=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.easeInOutBounce=e.easeOutBounce=e.easeInBounce=e.easeInOutElastic=e.easeOutElastic=e.easeInElastic=e.easeInOutBack=e.easeOutBack=e.easeInBack=e.easeInOutQuint=e.easeOutQuint=e.easeInQuint=e.easeInOutQuart=e.easeOutQuart=e.easeInQuart=e.easeInOutCubic=e.easeOutCubic=e.easeInCubic=e.easeInOutQuad=e.easeOutQuad=e.easeInQuad=e.easeInOutSine=e.easeOutSine=e.easeInSine=e.linear=void 0;var t=[[[0,1],"",[.33,.67]],[[1,0],[.67,.33]]];e.linear=t;var n=[[[0,1]],[[.538,.564],[.169,.912],[.88,.196]],[[1,0]]];e.easeInSine=n;var r=[[[0,1]],[[.444,.448],[.169,.736],[.718,.16]],[[1,0]]];e.easeOutSine=r;var i=[[[0,1]],[[.5,.5],[.2,1],[.8,0]],[[1,0]]];e.easeInOutSine=i;var o=[[[0,1]],[[.55,.584],[.231,.904],[.868,.264]],[[1,0]]];e.easeInQuad=o;var s=[[[0,1]],[[.413,.428],[.065,.816],[.76,.04]],[[1,0]]];e.easeOutQuad=s;var a=[[[0,1]],[[.5,.5],[.3,.9],[.7,.1]],[[1,0]]];e.easeInOutQuad=a;var l=[[[0,1]],[[.679,.688],[.366,.992],[.992,.384]],[[1,0]]];e.easeInCubic=l;var u=[[[0,1]],[[.321,.312],[.008,.616],[.634,.008]],[[1,0]]];e.easeOutCubic=u;var c=[[[0,1]],[[.5,.5],[.3,1],[.7,0]],[[1,0]]];e.easeInOutCubic=c;var f=[[[0,1]],[[.812,.74],[.611,.988],[1.013,.492]],[[1,0]]];e.easeInQuart=f;var p=[[[0,1]],[[.152,.244],[.001,.448],[.285,-.02]],[[1,0]]];e.easeOutQuart=p;var d=[[[0,1]],[[.5,.5],[.4,1],[.6,0]],[[1,0]]];e.easeInOutQuart=d;var h=[[[0,1]],[[.857,.856],[.714,1],[1,.712]],[[1,0]]];e.easeInQuint=h;var x=[[[0,1]],[[.108,.2],[.001,.4],[.214,-.012]],[[1,0]]];e.easeOutQuint=x;var j=[[[0,1]],[[.5,.5],[.5,1],[.5,0]],[[1,0]]];e.easeInOutQuint=j;var L=[[[0,1]],[[.667,.896],[.38,1.184],[.955,.616]],[[1,0]]];e.easeInBack=L;var A=[[[0,1]],[[.335,.028],[.061,.22],[.631,-.18]],[[1,0]]];e.easeOutBack=A;var g=[[[0,1]],[[.5,.5],[.4,1.4],[.6,-.4]],[[1,0]]];e.easeInOutBack=g;var C=[[[0,1]],[[.474,.964],[.382,.988],[.557,.952]],[[.619,1.076],[.565,1.088],[.669,1.08]],[[.77,.916],[.712,.924],[.847,.904]],[[.911,1.304],[.872,1.316],[.961,1.34]],[[1,0]]];e.easeInElastic=C;var b=[[[0,1]],[[.073,-.32],[.034,-.328],[.104,-.344]],[[.191,.092],[.11,.06],[.256,.08]],[[.31,-.076],[.26,-.068],[.357,-.076]],[[.432,.032],[.362,.028],[.683,-.004]],[[1,0]]];e.easeOutElastic=b;var R=[[[0,1]],[[.21,.94],[.167,.884],[.252,.98]],[[.299,1.104],[.256,1.092],[.347,1.108]],[[.5,.496],[.451,.672],[.548,.324]],[[.696,-.108],[.652,-.112],[.741,-.124]],[[.805,.064],[.756,.012],[.866,.096]],[[1,0]]];e.easeInOutElastic=R;var T=[[[0,1]],[[.148,1],[.075,.868],[.193,.848]],[[.326,1],[.276,.836],[.405,.712]],[[.6,1],[.511,.708],[.671,.348]],[[1,0]]];e.easeInBounce=T;var O=[[[0,1]],[[.357,.004],[.27,.592],[.376,.252]],[[.604,-.004],[.548,.312],[.669,.184]],[[.82,0],[.749,.184],[.905,.132]],[[1,0]]];e.easeOutBounce=O;var y=[[[0,1]],[[.102,1],[.05,.864],[.117,.86]],[[.216,.996],[.208,.844],[.227,.808]],[[.347,.996],[.343,.8],[.48,.292]],[[.635,.004],[.511,.676],[.656,.208]],[[.787,0],[.76,.2],[.795,.144]],[[.905,-.004],[.899,.164],[.944,.144]],[[1,0]]];e.easeInOutBounce=y;var S=new Map([["linear",t],["easeInSine",n],["easeOutSine",r],["easeInOutSine",i],["easeInQuad",o],["easeOutQuad",s],["easeInOutQuad",a],["easeInCubic",l],["easeOutCubic",u],["easeInOutCubic",c],["easeInQuart",f],["easeOutQuart",p],["easeInOutQuart",d],["easeInQuint",h],["easeOutQuint",x],["easeInOutQuint",j],["easeInBack",L],["easeOutBack",A],["easeInOutBack",g],["easeInElastic",C],["easeOutElastic",b],["easeInOutElastic",R],["easeInBounce",T],["easeOutBounce",O],["easeInOutBounce",y]]);e.default=S}(md)),md}var yd;function Tx(){return yd||(yd=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.transition=s,e.injectNewCurve=g,e.default=void 0;var n=t(Nt()),r=t(wt()),i=t(wx()),o="linear";function s(b){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,T=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,O=arguments.length>3&&arguments[3]!==void 0?arguments[3]:30,y=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(!a.apply(void 0,arguments))return!1;try{var S=l(b),H=u(S,O);return!y||typeof T=="number"?h(R,T,H):A(R,T,H)}catch{return console.warn("Transition parameter may be abnormal!"),[T]}}function a(b){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,T=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,O=arguments.length>3&&arguments[3]!==void 0?arguments[3]:30;if(!b||R===!1||T===!1||!O)return console.error("transition: Missing Parameters!"),!1;if((0,r.default)(R)!==(0,r.default)(T))return console.error("transition: Inconsistent Status Types!"),!1;var y=(0,r.default)(T);return y==="string"||y==="boolean"||!b.length?(console.error("transition: Unsupported Data Type of State!"),!1):(!i.default.has(b)&&!(b instanceof Array)&&console.warn("transition: Transition curve not found, default curve will be used!"),!0)}function l(b){var R="";return i.default.has(b)?R=i.default.get(b):b instanceof Array?R=b:R=i.default.get(o),R}function u(b,R){var T=1/(R-1),O=new Array(R).fill(0).map(function(S,H){return H*T}),y=O.map(function(S){return c(b,S)});return y}function c(b,R){var T=f(b,R),O=p(T,R);return d(T,O)}function f(b,R){var T=b.length-1,O="",y="";b.findIndex(function(z,Z){if(Z!==T){O=z,y=b[Z+1];var J=O[0][0],P=y[0][0];return R>=J&&R<P}});var S=O[0],H=O[2]||O[0],I=y[1]||y[0],M=y[0];return[S,H,I,M]}function p(b,R){var T=b[0][0],O=b[3][0],y=O-T,S=R-T;return S/y}function d(b,R){var T=(0,n.default)(b,4),O=(0,n.default)(T[0],2),y=O[1],S=(0,n.default)(T[1],2),H=S[1],I=(0,n.default)(T[2],2),M=I[1],z=(0,n.default)(T[3],2),Z=z[1],J=Math.pow,P=1-R,W=y*J(P,3),D=3*H*R*J(P,2),V=3*M*J(R,2)*P,X=Z*J(R,3);return 1-(W+D+V+X)}function h(b,R,T){var O="object";return typeof b=="number"&&(O="number"),b instanceof Array&&(O="array"),O==="number"?x(b,R,T):O==="array"?j(b,R,T):O==="object"?L(b,R,T):T.map(function(y){return R})}function x(b,R,T){var O=R-b;return T.map(function(y){return b+O*y})}function j(b,R,T){var O=R.map(function(y,S){return typeof y!="number"?!1:y-b[S]});return T.map(function(y){return O.map(function(S,H){return S===!1?R[H]:b[H]+S*y})})}function L(b,R,T){var O=Object.keys(R),y=O.map(function(I){return b[I]}),S=O.map(function(I){return R[I]}),H=j(y,S,T);return H.map(function(I){var M={};return I.forEach(function(z,Z){return M[O[Z]]=z}),M})}function A(b,R,T){var O=h(b,R,T),y=function(I){var M=b[I],z=R[I];if((0,r.default)(z)!=="object")return"continue";var Z=A(M,z,T);O.forEach(function(J,P){return J[I]=Z[P]})};for(var S in R)var H=y(S);return O}function g(b,R){if(!b||!R){console.error("InjectNewCurve Missing Parameters!");return}i.default.set(b,R)}var C=s;e.default=C}(gd)),gd}var bd;function Ox(){return bd||(bd=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t(xx()),r=t(Ex()),i=t(wt()),o=t(pt()),s=t(mo()),a=t(Sx()),l=t(Tx()),u=mt(),c=function p(d,h){(0,s.default)(this,p),h=(0,u.deepClone)(h,!0);var x={visible:!0,drag:!1,hover:!1,index:1,animationDelay:0,animationFrame:30,animationCurve:"linear",animationPause:!1,hoverRect:null,mouseEnter:null,mouseOuter:null,click:null},j={status:"static",animationRoot:[],animationKeys:[],animationFrameState:[],cache:{}};h.shape||(h.shape={}),h.style||(h.style={});var L=Object.assign({},d.shape,h.shape);Object.assign(x,h,j),Object.assign(this,d,x),this.shape=L,this.style=new a.default(h.style),this.addedProcessor()};e.default=c,c.prototype.addedProcessor=function(){typeof this.setGraphCenter=="function"&&this.setGraphCenter(null,this),typeof this.added=="function"&&this.added(this)},c.prototype.drawProcessor=function(p,d){var h=p.ctx;d.style.initStyle(h),typeof this.beforeDraw=="function"&&this.beforeDraw(this,p),d.draw(p,d),typeof this.drawed=="function"&&this.drawed(this,p),d.style.restoreTransform(h)},c.prototype.hoverCheckProcessor=function(p,d){var h=d.hoverRect,x=d.style,j=d.hoverCheck,L=x.graphCenter,A=x.rotate,g=x.scale,C=x.translate;return L&&(A&&(p=(0,u.getRotatePointPos)(-A,p,L)),g&&(p=(0,u.getScalePointPos)(g.map(function(b){return 1/b}),p,L)),C&&(p=(0,u.getTranslatePointPos)(C.map(function(b){return b*-1}),p))),h?u.checkPointIsInRect.apply(void 0,[p].concat((0,o.default)(h))):j(p,this)},c.prototype.moveProcessor=function(p){this.move(p,this),typeof this.beforeMove=="function"&&this.beforeMove(p,this),typeof this.setGraphCenter=="function"&&this.setGraphCenter(p,this),typeof this.moved=="function"&&this.moved(p,this)},c.prototype.attr=function(p){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0;if(!p||d===void 0)return!1;var h=(0,i.default)(this[p])==="object";h&&(d=(0,u.deepClone)(d,!0));var x=this.render;p==="style"?this.style.update(d):h?Object.assign(this[p],d):this[p]=d,p==="index"&&x.sortGraphsByIndex(),x.drawAllGraph()},c.prototype.animation=function(){var p=(0,r.default)(n.default.mark(function d(h,x){var j,L,A,g,C,b,R,T,O,y=arguments;return n.default.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:if(j=y.length>2&&y[2]!==void 0?y[2]:!1,!(h!=="shape"&&h!=="style")){S.next=4;break}return console.error("Only supported shape and style animation!"),S.abrupt("return");case 4:if(x=(0,u.deepClone)(x,!0),h==="style"&&this.style.colorProcessor(x),L=this[h],A=Object.keys(x),g={},A.forEach(function(H){return g[H]=L[H]}),C=this.animationFrame,b=this.animationCurve,R=this.animationDelay,T=(0,l.default)(b,g,x,C,!0),this.animationRoot.push(L),this.animationKeys.push(A),this.animationFrameState.push(T),!j){S.next=17;break}return S.abrupt("return");case 17:if(!(R>0)){S.next=20;break}return S.next=20,f(R);case 20:return O=this.render,S.abrupt("return",new Promise(function(){var H=(0,r.default)(n.default.mark(function I(M){return n.default.wrap(function(z){for(;;)switch(z.prev=z.next){case 0:return z.next=2,O.launchAnimation();case 2:M();case 3:case"end":return z.stop()}},I)}));return function(I){return H.apply(this,arguments)}}()));case 22:case"end":return S.stop()}},d,this)}));return function(d,h){return p.apply(this,arguments)}}(),c.prototype.turnNextAnimationFrame=function(p){var d=this.animationDelay,h=this.animationRoot,x=this.animationKeys,j=this.animationFrameState,L=this.animationPause;L||Date.now()-p<d||(h.forEach(function(A,g){x[g].forEach(function(C){A[C]=j[g][0][C]})}),j.forEach(function(A,g){A.shift();var C=A.length===0;C&&(h[g]=null),C&&(x[g]=null)}),this.animationFrameState=j.filter(function(A){return A.length}),this.animationRoot=h.filter(function(A){return A}),this.animationKeys=x.filter(function(A){return A}))},c.prototype.animationEnd=function(){var p=this.animationFrameState,d=this.animationKeys,h=this.animationRoot,x=this.render;return h.forEach(function(j,L){var A=d[L],g=p[L].pop();A.forEach(function(C){return j[C]=g[C]})}),this.animationFrameState=[],this.animationKeys=[],this.animationRoot=[],x.drawAllGraph()},c.prototype.pauseAnimation=function(){this.attr("animationPause",!0)},c.prototype.playAnimation=function(){var p=this.render;return this.attr("animationPause",!1),new Promise(function(){var d=(0,r.default)(n.default.mark(function h(x){return n.default.wrap(function(j){for(;;)switch(j.prev=j.next){case 0:return j.next=2,p.launchAnimation();case 2:x();case 3:case"end":return j.stop()}},h)}));return function(h){return d.apply(this,arguments)}}())},c.prototype.delProcessor=function(p){var d=this,h=p.graphs,x=h.findIndex(function(j){return j===d});x!==-1&&(typeof this.beforeDelete=="function"&&this.beforeDelete(this),h.splice(x,1,null),typeof this.deleted=="function"&&this.deleted(this))};function f(p){return new Promise(function(d){setTimeout(d,p)})}}(ad)),ad}var _d;function Px(){return _d||(_d=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t(an()),r=t(pt()),i=t(mo()),o=t(vo),s=t(Cu()),a=mt(),l=t(xu()),u=t(Ox());function c(A,g){var C=Object.keys(A);if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(A);g&&(b=b.filter(function(R){return Object.getOwnPropertyDescriptor(A,R).enumerable})),C.push.apply(C,b)}return C}function f(A){for(var g=1;g<arguments.length;g++){var C=arguments[g]!=null?arguments[g]:{};g%2?c(C,!0).forEach(function(b){(0,n.default)(A,b,C[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(C)):c(C).forEach(function(b){Object.defineProperty(A,b,Object.getOwnPropertyDescriptor(C,b))})}return A}var p=function A(g){if((0,i.default)(this,A),!g){console.error("CRender Missing parameters!");return}var C=g.getContext("2d"),b=g.clientWidth,R=g.clientHeight,T=[b,R];g.setAttribute("width",b),g.setAttribute("height",R),this.ctx=C,this.area=T,this.animationStatus=!1,this.graphs=[],this.color=o.default,this.bezierCurve=s.default,g.addEventListener("mousedown",x.bind(this)),g.addEventListener("mousemove",j.bind(this)),g.addEventListener("mouseup",L.bind(this))};e.default=p,p.prototype.clearArea=function(){var A,g=this.area;(A=this.ctx).clearRect.apply(A,[0,0].concat((0,r.default)(g)))},p.prototype.add=function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},g=A.name;if(!g){console.error("add Missing parameters!");return}var C=l.default.get(g);if(!C){console.warn("No corresponding graph configuration found!");return}var b=new u.default(C,A);if(b.validator(b))return b.render=this,this.graphs.push(b),this.sortGraphsByIndex(),this.drawAllGraph(),b},p.prototype.sortGraphsByIndex=function(){var A=this.graphs;A.sort(function(g,C){if(g.index>C.index)return 1;if(g.index===C.index)return 0;if(g.index<C.index)return-1})},p.prototype.delGraph=function(A){typeof A.delProcessor=="function"&&(A.delProcessor(this),this.graphs=this.graphs.filter(function(g){return g}),this.drawAllGraph())},p.prototype.delAllGraph=function(){var A=this;this.graphs.forEach(function(g){return g.delProcessor(A)}),this.graphs=this.graphs.filter(function(g){return g}),this.drawAllGraph()},p.prototype.drawAllGraph=function(){var A=this;this.clearArea(),this.graphs.filter(function(g){return g&&g.visible}).forEach(function(g){return g.drawProcessor(A,g)})},p.prototype.launchAnimation=function(){var A=this,g=this.animationStatus;if(!g)return this.animationStatus=!0,new Promise(function(C){d.call(A,function(){A.animationStatus=!1,C()},Date.now())})};function d(A,g){var C=this.graphs;if(!h(C)){A();return}C.forEach(function(b){return b.turnNextAnimationFrame(g)}),this.drawAllGraph(),requestAnimationFrame(d.bind(this,A,g))}function h(A){return A.find(function(g){return!g.animationPause&&g.animationFrameState.length})}function x(A){var g=this.graphs,C=g.find(function(b){return b.status==="hover"});C&&(C.status="active")}function j(A){var g=A.offsetX,C=A.offsetY,b=[g,C],R=this.graphs,T=R.find(function(M){return M.status==="active"||M.status==="drag"});if(T){if(!T.drag)return;if(typeof T.move!="function"){console.error("No move method is provided, cannot be dragged!");return}T.moveProcessor(A),T.status="drag";return}var O=R.find(function(M){return M.status==="hover"}),y=R.filter(function(M){return M.hover&&(typeof M.hoverCheck=="function"||M.hoverRect)}),S=y.find(function(M){return M.hoverCheckProcessor(b,M)});S?document.body.style.cursor=S.style.hoverCursor:document.body.style.cursor="default";var H=!1,I=!1;if(O&&(H=typeof O.mouseOuter=="function"),S&&(I=typeof S.mouseEnter=="function"),!(!S&&!O)){if(!S&&O){H&&O.mouseOuter(A,O),O.status="static";return}if(!(S&&S===O)){if(S&&!O){I&&S.mouseEnter(A,S),S.status="hover";return}S&&O&&S!==O&&(H&&O.mouseOuter(A,O),O.status="static",I&&S.mouseEnter(A,S),S.status="hover")}}}function L(A){var g=this.graphs,C=g.find(function(R){return R.status==="active"}),b=g.find(function(R){return R.status==="drag"});C&&typeof C.click=="function"&&C.click(A,C),g.forEach(function(R){return R&&(R.status="static")}),C&&(C.status="hover"),b&&(b.status="hover")}p.prototype.clone=function(A){var g=A.style.getStyle(),C=f({},A,{style:g});return delete C.render,C=(0,a.deepClone)(C,!0),this.add(C)}}(yf)),yf}(function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"CRender",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"extendNewGraph",{enumerable:!0,get:function(){return r.extendNewGraph}}),e.default=void 0;var n=t(Px()),r=xu(),i=n.default;e.default=i})(fa);const E0=b0(fa),Ax={class:"dv-water-pond-level"},Ix={key:0},kx=["id"],$x=["offset","stop-color"],Nx=["stroke","fill","x","y"],Mx=["cx","cy","rx","ry","stroke"],Rx=["rx","ry","width","height","stroke"],Lx={__name:"index",props:{config:Object,default:()=>({})},setup(e){const t=e,n=sn(),r=ye(null),i=ke({gradientId:`water-level-pond-${n}`,defaultConfig:{data:[],shape:"rect",waveNum:3,waveHeight:40,waveOpacity:.4,colors:["#3DE7C9","#00BAFF"],formatter:"{value}%"},mergedConfig:{},renderer:null,svgBorderGradient:[],details:"",waves:[],animation:!1}),o=Ce(()=>{const{shape:g}=i.mergedConfig;return g==="round"?"50%":g==="rect"?"0":g==="roundRect"?"10px":"0"}),s=Ce(()=>{const{shape:g}=i.mergedConfig;return g||"rect"});we(()=>t.config,()=>{i.renderer.delAllGraph(),i.waves=[],setTimeout(u,0)},{deep:!0}),Ue(()=>{a()}),oi(()=>{i.renderer.delAllGraph(),i.waves=[]});function a(){l(),t.config&&u()}function l(){i.renderer=new E0(r.value)}function u(){c(),f(),p(),d(),A()}function c(){i.mergedConfig=je(Be(i.defaultConfig),t.config)}function f(){const{colors:g}=i.mergedConfig,C=100/(g.length-1);i.svgBorderGradient=g.map((b,R)=>[C*R,b])}function p(){const{data:g,formatter:C}=i.mergedConfig;if(!g.length){i.details="";return}const b=Math.max(...g);i.details=C.replace("{value}",b)}function d(){const g=h(),C=j();i.waves=g.map(b=>i.renderer.add({name:"smoothline",animationFrame:300,shape:b,style:C,drawed:L}))}function h(){const{waveNum:g,waveHeight:C,data:b}=i.mergedConfig,[R,T]=i.renderer.area,O=g*4+4,y=R/g/2;return b.map(S=>{let H=Array.from({length:O}).fill(0).map((I,M)=>{const z=R-y*M,Z=(1-S/100)*T,J=M%2===0?Z:Z-C;return[z,J]});return H=H.map(I=>x(I,[y*2,0])),{points:H}})}function x([g,C],[b,R]){return[g+b,C+R]}function j(){const g=i.renderer.area[1];return{gradientColor:i.mergedConfig.colors,gradientType:"linear",gradientParams:[0,0,0,g],gradientWith:"fill",opacity:i.mergedConfig.waveOpacity,translate:[0,0]}}function L({shape:{points:g}},{ctx:C,area:b}){const R=g[0],T=g.slice(-1)[0],O=b[1];C.lineTo(T[0],O),C.lineTo(R[0],O),C.closePath(),C.fill()}async function A(g=1){if(i.animation)return;i.animation=!0;const C=i.renderer.area[0];i.waves.forEach(b=>{b.attr("style",{translate:[0,0]}),b.animation("style",{translate:[C,0]},!0)}),await i.renderer.launchAnimation(),i.animation=!1,i.renderer.graphs.length&&A(g+1)}return(g,C)=>(fe(),de("div",Ax,[v(i).renderer?(fe(),de("svg",Ix,[ae("defs",null,[ae("linearGradient",{id:v(i).gradientId,x1:"0%",y1:"0%",x2:"0%",y2:"100%"},[(fe(!0),de(Se,null,qe(v(i).svgBorderGradient,b=>(fe(),de("stop",{key:b[0],offset:b[0],"stop-color":b[1]},null,8,$x))),128))],8,kx)]),v(i).renderer?(fe(),de("text",{key:0,stroke:`url(#${v(i).gradientId})`,fill:`url(#${v(i).gradientId})`,x:v(i).renderer.area[0]/2+8,y:v(i).renderer.area[1]/2+8},ct(v(i).details),9,Nx)):Re("",!0),!v(s)||v(s)==="round"?(fe(),de("ellipse",{key:1,cx:v(i).renderer.area[0]/2+8,cy:v(i).renderer.area[1]/2+8,rx:v(i).renderer.area[0]/2+5,ry:v(i).renderer.area[1]/2+5,stroke:`url(#${v(i).gradientId})`},null,8,Mx)):(fe(),de("rect",{key:2,x:"2",y:"2",rx:v(s)==="roundRect"?10:0,ry:v(s)==="roundRect"?10:0,width:v(i).renderer.area[0]+12,height:v(i).renderer.area[1]+12,stroke:`url(#${v(i).gradientId})`},null,8,Rx))])):Re("",!0),ae("canvas",{ref_key:"waterPondLevel",ref:r,style:Ve(`border-radius: ${v(o)};`)},null,4)]))}},Cd={install(e){e.component("DvWaterLevelPond",Lx)}},Dx={},Fx={class:"dv-loading"},jx={class:"loading-tip"};function Bx(e,t){return fe(),de("div",Fx,[t[0]||(t[0]=si('<svg width="50px" height="50px"><circle cx="25" cy="25" r="20" fill="transparent" stroke-width="3" stroke-dasharray="31.415, 31.415" stroke="#02bcfe" stroke-linecap="round"><animateTransform attributeName="transform" type="rotate" values="0, 25 25;360, 25 25" dur="1.5s" repeatCount="indefinite"></animateTransform><animate attributeName="stroke" values="#02bcfe;#3be6cb;#02bcfe" dur="3s" repeatCount="indefinite"></animate></circle><circle cx="25" cy="25" r="10" fill="transparent" stroke-width="3" stroke-dasharray="15.7, 15.7" stroke="#3be6cb" stroke-linecap="round"><animateTransform attributeName="transform" type="rotate" values="360, 25 25;0, 25 25" dur="1.5s" repeatCount="indefinite"></animateTransform><animate attributeName="stroke" values="#3be6cb;#02bcfe;#3be6cb" dur="3s" repeatCount="indefinite"></animate></circle></svg>',1)),ae("div",jx,[ze(e.$slots,"default")])])}const Vx=sr(Dx,[["render",Bx]]),xd={install(e){e.component("DvLoading",Vx)}},Hx=["width","height"],Gx=["id"],Ux=["id"],Wx=["id","cx","cy"],Xx=["values","dur"],Kx=["dur"],qx=["id"],zx=["xlink:href","fill"],Jx=["xlink:href","fill","mask"],Qx=["xlink:href","width","height","x","y"],Yx=["fill","x","y"],Zx=["id","d"],eE=["xlink:href","stroke-width","stroke"],tE=["id"],nE=["r","fill"],rE=["dur","path"],iE=["xlink:href","stroke-width","stroke","mask"],oE=["from","to","dur"],sE={__name:"index",props:{config:{type:Object,default:()=>({})},dev:{type:Boolean,default:!1}},setup(e){const t=e,n=sn(),r=ye(null),{width:i,height:o}=De(r,u,l),s=ke({unique:Math.random(),flylineGradientId:`flyline-gradient-id-${n}`,haloGradientId:`halo-gradient-id-${n}`,defaultConfig:{points:[],lines:[],halo:{show:!1,duration:[20,30],color:"#fb7293",radius:120},text:{show:!1,offset:[0,15],color:"#ffdb5c",fontSize:12},icon:{show:!1,src:"",width:15,height:15},line:{width:1,color:"#ffde93",orbitColor:"rgba(103, 224, 227, .2)",duration:[20,30],radius:100},bgImgSrc:"",k:-.5,curvature:5,relative:!0},flylines:[],flylineLengths:[],flylinePoints:[],mergedConfig:null});let a;Ue(()=>{a=gt()}),we(()=>t.config,()=>{c()},{deep:!0});function l(){c()}function u(){c()}async function c(){f(),p(),d(),await L()}function f(){const g=je(Be(s.defaultConfig),t.config||{}),{points:C,lines:b,halo:R,text:T,icon:O,line:y}=g;g.points=C.map(S=>(S.halo=je(Be(R),S.halo||{}),S.text=je(Be(T),S.text||{}),S.icon=je(Be(O),S.icon||{}),S)),g.lines=b.map(S=>je(Be(y),S)),s.mergedConfig=g}function p(){const{relative:g,points:C}=s.mergedConfig;s.flylinePoints=C.map((b,R)=>{const{coordinate:[T,O],halo:y,icon:S,text:H}=b;g&&(b.coordinate=[T*i.value,O*o.value]),b.halo.time=qi(...y.duration)/10;const{width:I,height:M}=S;b.icon.x=b.coordinate[0]-I/2,b.icon.y=b.coordinate[1]-M/2;const[z,Z]=H.offset;return b.text.x=b.coordinate[0]+z,b.text.y=b.coordinate[1]+Z,b.key=`${b.coordinate.toString()}${R}`,b})}function d(){const{points:g,lines:C}=s.mergedConfig;s.flylines=C.map(b=>{const{source:R,target:T,duration:O}=b,y=g.find(({name:Z})=>Z===R).coordinate,S=g.find(({name:Z})=>Z===T).coordinate,H=h(y,S).map(Z=>Z.map(J=>Number.parseFloat(J.toFixed(10)))),I=`M${H[0].toString()} Q${H[1].toString()} ${H[2].toString()}`,M=`path${H.toString()}`,z=qi(...O)/10;return{...b,path:H,key:M,d:I,time:z}})}function h(g,C){const b=x(g,C);return[g,b,C]}function x([g,C],[b,R]){const{curvature:T,k:O}=s.mergedConfig,[y,S]=[(g+b)/2,(C+R)/2],H=xs([g,C],[b,R])/T,I=H/2;let[M,z]=[y,S];do M+=I,z=j(O,[y,S],M)[1];while(xs([y,S],[M,z])<H);return[M,z]}function j(g,[C,b],R){const T=b-g*C+g*R;return[R,T]}async function L(){await on(),s.flylineLengths=s.flylines.map(({key:g})=>a.proxy.$refs[g][0].getTotalLength())}function A({offsetX:g,offsetY:C}){if(!t.dev)return;const b=(g/i.value).toFixed(2),R=(C/o.value).toFixed(2);console.warn(`dv-flyline-chart-enhanced DEV: 
 Click Position is [${g}, ${C}] 
 Relative Position is [${b}, ${R}]`)}return(g,C)=>(fe(),de("div",{ref_key:"flylineChartEnhanced",ref:r,class:"dv-flyline-chart-enhanced",style:Ve(`background-image: url(${v(s).mergedConfig?v(s).mergedConfig.bgImgSrc:""})`),onClick:A},[v(s).flylines.length?(fe(),de("svg",{key:0,width:v(i),height:v(o)},[ae("defs",null,[ae("radialGradient",{id:v(s).flylineGradientId,cx:"50%",cy:"50%",r:"50%"},C[0]||(C[0]=[ae("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"1"},null,-1),ae("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"0"},null,-1)]),8,Gx),ae("radialGradient",{id:v(s).haloGradientId,cx:"50%",cy:"50%",r:"50%"},C[1]||(C[1]=[ae("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"0"},null,-1),ae("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"1"},null,-1)]),8,Ux)]),(fe(!0),de(Se,null,qe(v(s).flylinePoints,b=>(fe(),de("g",{key:b.key+Math.random()},[ae("defs",null,[b.halo.show?(fe(),de("circle",{key:0,id:`halo${v(s).unique}${b.key}`,cx:b.coordinate[0],cy:b.coordinate[1]},[ae("animate",{attributeName:"r",values:`1;${b.halo.radius}`,dur:`${b.halo.time}s`,repeatCount:"indefinite"},null,8,Xx),ae("animate",{attributeName:"opacity",values:"1;0",dur:`${b.halo.time}s`,repeatCount:"indefinite"},null,8,Kx)],8,Wx)):Re("",!0)]),ae("mask",{id:`mask${v(s).unique}${b.key}`},[b.halo.show?(fe(),de("use",{key:0,"xlink:href":`#halo${v(s).unique}${b.key}`,fill:`url(#${v(s).haloGradientId})`},null,8,zx)):Re("",!0)],8,qx),b.halo.show?(fe(),de("use",{key:0,"xlink:href":`#halo${v(s).unique}${b.key}`,fill:b.halo.color,mask:`url(#mask${v(s).unique}${b.key})`},null,8,Jx)):Re("",!0),b.icon.show?(fe(),de("image",{key:1,"xlink:href":b.icon.src,width:b.icon.width,height:b.icon.height,x:b.icon.x,y:b.icon.y},null,8,Qx)):Re("",!0),b.text.show?(fe(),de("text",{key:2,style:Ve(`fontSize:${b.text.fontSize}px;color:${b.text.color}`),fill:b.text.color,x:b.text.x,y:b.text.y},ct(b.name),13,Yx)):Re("",!0)]))),128)),(fe(!0),de(Se,null,qe(v(s).flylines,(b,R)=>(fe(),de("g",{key:b.key+Math.random()},[ae("defs",null,[ae("path",{id:b.key,ref_for:!0,ref:b.key,d:b.d,fill:"transparent"},null,8,Zx)]),ae("use",{"xlink:href":`#${b.key}`,"stroke-width":b.width,stroke:b.orbitColor},null,8,eE),ae("mask",{id:`mask${v(s).unique}${b.key}`},[ae("circle",{cx:"0",cy:"0",r:b.radius,fill:`url(#${v(s).flylineGradientId})`},[ae("animateMotion",{dur:b.time,path:b.d,rotate:"auto",repeatCount:"indefinite"},null,8,rE)],8,nE)],8,tE),v(s).flylineLengths[R]?(fe(),de("use",{key:0,"xlink:href":`#${b.key}`,"stroke-width":b.width,stroke:b.color,mask:`url(#mask${v(s).unique}${b.key})`},[ae("animate",{attributeName:"stroke-dasharray",from:`0, ${v(s).flylineLengths[R]}`,to:`${v(s).flylineLengths[R]}, 0`,dur:b.time,repeatCount:"indefinite"},null,8,oE)],8,iE)):Re("",!0)]))),128))],8,Hx)):Re("",!0)],4))}},Ed={install(e){e.component("DvFlylineChartEnhanced",sE)}},aE=["width","height"],lE=["id"],uE=["id"],cE=["id","cx","cy"],fE=["values","dur"],dE=["dur"],pE=["xlink:href","width","height","x","y"],hE=["id"],gE=["xlink:href","fill"],mE=["xlink:href","fill","mask"],vE=["id","d"],yE=["xlink:href","stroke-width","stroke"],bE=["xlink:href","stroke-width","stroke","mask"],_E=["from","to","dur"],CE=["id"],xE=["r","fill"],EE=["dur","path"],SE=["xlink:href","width","height","x","y"],wE=["fill","x","y"],TE={__name:"index",props:{config:{type:Object,default:()=>({})},dev:{type:Boolean,default:!1}},setup(e){const t=e,n=sn(),r=ye(null),{width:i,height:o}=De(r,u,l),s=ke({unique:Math.random(),maskId:`flyline-mask-id-${n}`,maskCircleId:`mask-circle-id-${n}`,gradientId:`gradient-id-${n}`,gradient2Id:`gradient2-id-${n}`,defaultConfig:{centerPoint:[0,0],points:[],lineWidth:1,orbitColor:"rgba(103, 224, 227, .2)",flylineColor:"#ffde93",k:-.5,curvature:5,flylineRadius:100,duration:[20,30],relative:!0,bgImgUrl:"",text:{offset:[0,15],color:"#ffdb5c",fontSize:12},halo:{show:!0,duration:30,color:"#fb7293",radius:120},centerPointImg:{width:40,height:40,url:""},pointsImg:{width:15,height:15,url:""}},mergedConfig:null,paths:[],lengths:[],times:[],texts:[]});let a;Ue(()=>{a=gt()}),we(()=>t.config,()=>{c()},{deep:!0});function l(){c()}function u(){c()}async function c(){f(),p(),await j(),L(),A()}function f(){const C=je(Be(s.defaultConfig),t.config||{}),{points:b}=C;C.points=b.map(R=>Array.isArray(R)?{position:R,text:""}:R),s.mergedConfig=C}function p(){let{centerPoint:C,points:b}=s.mergedConfig;const{relative:R}=s.mergedConfig;b=b.map(({position:T})=>T),R&&(C=[i.value*C[0],o.value*C[1]],b=b.map(([T,O])=>[i.value*T,o.value*O])),s.paths=b.map(T=>d(C,T))}function d(C,b){const R=h(C,b);return[b,R,C]}function h([C,b],[R,T]){const{curvature:O,k:y}=s.mergedConfig,[S,H]=[(C+R)/2,(b+T)/2],I=xs([C,b],[R,T])/O,M=I/2;let[z,Z]=[S,H];do z+=M,Z=x(y,[S,H],z)[1];while(xs([S,H],[z,Z])<I);return[z,Z]}function x(C,[b,R],T){const O=R-C*b+C*T;return[T,O]}async function j(){await on(),s.lengths=s.paths.map((C,b)=>a.proxy.$refs[`path${b}`][0].getTotalLength())}function L(){const{duration:C,points:b}=s.mergedConfig;s.times=b.map(()=>qi(...C)/10)}function A(){const{points:C}=s.mergedConfig;s.texts=C.map(({text:b})=>b)}function g({offsetX:C,offsetY:b}){if(!t.dev)return;const R=(C/i.value).toFixed(2),T=(b/o.value).toFixed(2);console.warn(`dv-flyline-chart DEV: 
 Click Position is [${C}, ${b}] 
 Relative Position is [${R}, ${T}]`)}return(C,b)=>(fe(),de("div",{ref_key:"flylineChart",ref:r,class:"dv-flyline-chart",style:Ve(`background-image: url(${v(s).mergedConfig?v(s).mergedConfig.bgImgUrl:""})`),onClick:g},[v(s).mergedConfig?(fe(),de("svg",{key:0,width:v(i),height:v(o)},[ae("defs",null,[ae("radialGradient",{id:v(s).gradientId,cx:"50%",cy:"50%",r:"50%"},b[0]||(b[0]=[ae("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"1"},null,-1),ae("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"0"},null,-1)]),8,lE),ae("radialGradient",{id:v(s).gradient2Id,cx:"50%",cy:"50%",r:"50%"},b[1]||(b[1]=[ae("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"0"},null,-1),ae("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"1"},null,-1)]),8,uE),v(s).paths[0]?(fe(),de("circle",{key:0,id:`circle${v(s).paths[0].toString()}`,cx:v(s).paths[0][2][0],cy:v(s).paths[0][2][1]},[ae("animate",{attributeName:"r",values:`1;${v(s).mergedConfig.halo.radius}`,dur:`${v(s).mergedConfig.halo.duration/10}s`,repeatCount:"indefinite"},null,8,fE),ae("animate",{attributeName:"opacity",values:"1;0",dur:`${v(s).mergedConfig.halo.duration/10}s`,repeatCount:"indefinite"},null,8,dE)],8,cE)):Re("",!0)]),v(s).paths[0]?(fe(),de("image",{key:0,"xlink:href":v(s).mergedConfig.centerPointImg.url,width:v(s).mergedConfig.centerPointImg.width,height:v(s).mergedConfig.centerPointImg.height,x:v(s).paths[0][2][0]-v(s).mergedConfig.centerPointImg.width/2,y:v(s).paths[0][2][1]-v(s).mergedConfig.centerPointImg.height/2},null,8,pE)):Re("",!0),ae("mask",{id:`maskhalo${v(s).paths[0].toString()}`},[v(s).paths[0]?(fe(),de("use",{key:0,"xlink:href":`#circle${v(s).paths[0].toString()}`,fill:`url(#${v(s).gradient2Id})`},null,8,gE)):Re("",!0)],8,hE),v(s).paths[0]&&v(s).mergedConfig.halo.show?(fe(),de("use",{key:1,"xlink:href":`#circle${v(s).paths[0].toString()}`,fill:v(s).mergedConfig.halo.color,mask:`url(#maskhalo${v(s).paths[0].toString()})`},null,8,mE)):Re("",!0),(fe(!0),de(Se,null,qe(v(s).paths,(R,T)=>(fe(),de("g",{key:T},[ae("defs",null,[ae("path",{id:`path${R.toString()}`,ref_for:!0,ref:`path${T}`,d:`M${R[0].toString()} Q${R[1].toString()} ${R[2].toString()}`,fill:"transparent"},null,8,vE)]),ae("use",{"xlink:href":`#path${R.toString()}`,"stroke-width":v(s).mergedConfig.lineWidth,stroke:v(s).mergedConfig.orbitColor},null,8,yE),v(s).lengths[T]?(fe(),de("use",{key:0,"xlink:href":`#path${R.toString()}`,"stroke-width":v(s).mergedConfig.lineWidth,stroke:v(s).mergedConfig.flylineColor,mask:`url(#mask${v(s).unique}${R.toString()})`},[ae("animate",{attributeName:"stroke-dasharray",from:`0, ${v(s).lengths[T]}`,to:`${v(s).lengths[T]}, 0`,dur:v(s).times[T]||0,repeatCount:"indefinite"},null,8,_E)],8,bE)):Re("",!0),ae("mask",{id:`mask${v(s).unique}${R.toString()}`},[ae("circle",{cx:"0",cy:"0",r:v(s).mergedConfig.flylineRadius,fill:`url(#${v(s).gradientId})`},[ae("animateMotion",{dur:v(s).times[T]||0,path:`M${R[0].toString()} Q${R[1].toString()} ${R[2].toString()}`,rotate:"auto",repeatCount:"indefinite"},null,8,EE)],8,xE)],8,CE),ae("image",{"xlink:href":v(s).mergedConfig.pointsImg.url,width:v(s).mergedConfig.pointsImg.width,height:v(s).mergedConfig.pointsImg.height,x:R[0][0]-v(s).mergedConfig.pointsImg.width/2,y:R[0][1]-v(s).mergedConfig.pointsImg.height/2},null,8,SE),ae("text",{style:Ve(`fontSize:${v(s).mergedConfig.text.fontSize}px;`),fill:v(s).mergedConfig.text.color,x:R[0][0]+v(s).mergedConfig.text.offset[0],y:R[0][1]+v(s).mergedConfig.text.offset[1]},ct(v(s).texts[T]),13,wE)]))),128))],8,aE)):Re("",!0)],4))}},Sd={install(e){e.component("DvFlylineChart",TE)}},OE={class:"ranking-info"},PE={class:"rank"},AE=["innerHTML"],IE={class:"ranking-value"},kE={class:"ranking-column"},$E={__name:"index",props:{config:{type:Object,default:()=>({})}},setup(e){Or(A=>({fdc8fe50:v(s),"442085aa":v(o),ca44414a:v(l),"31d0b588":v(a)}));const t=e,n=ye(null),{height:r}=De(n,c,u),i=ke({defaultConfig:{data:[],rowNum:5,waitTime:2e3,carousel:"single",unit:"",sort:!0,valueFormatter:null,textColor:"#fff",color:"#1370fb",fontSize:13},mergedConfig:null,rowsData:[],rows:[],heights:[],avgHeight:0,animationIndex:0,animationHandler:"",updater:0});we(()=>t.config,()=>{L(),f()},{deep:!0});const o=Ce(()=>t.config.textColor?t.config.textColor:i.defaultConfig.textColor),s=Ce(()=>t.config.color?t.config.color:i.defaultConfig.color),a=Ce(()=>ut(s.value,50)),l=Ce(()=>`${t.config.fontSize?t.config.fontSize:i.defaultConfig.fontSize}px`);Mn(()=>{L()});function u(){f()}function c(){i.mergedConfig&&h(!0)}function f(){p(),d(),h(),j(!0)}function p(){i.mergedConfig=je(Be(i.defaultConfig),t.config||{})}function d(){let{data:A}=i.mergedConfig;const{rowNum:g,sort:C}=i.mergedConfig;C&&A.sort(({value:S},{value:H})=>S>H?-1:S<H?1:0);const b=A.map(({value:S})=>S),R=Math.min(...b)||0,T=Math.abs(R),O=(Math.max(...b)||0)+T;A=A.map((S,H)=>({...S,ranking:H+1,percent:(S.value+T)/O*100}));const y=A.length;y>g&&y<2*g&&(A=[...A,...A]),A=A.map((S,H)=>({...S,scroll:H})),i.rowsData=A,i.rows=A}function h(A=!1){const{rowNum:g,data:C}=i.mergedConfig,b=r.value/g;i.avgHeight=b,A||(i.heights=Array.from({length:C.length}).fill(b))}const x=Ce(()=>i.mergedConfig.carousel==="single");async function j(A=!1){const{waitTime:g,rowNum:C}=i.mergedConfig,b=i.rowsData.length;if(C>=b)return;const{updater:R}=i;if(A&&(await new Promise(S=>setTimeout(S,g)),R!==i.updater))return;const T=x.value?1:C,O=i.rowsData.slice(i.animationIndex);if(O.push(...i.rowsData.slice(0,i.animationIndex)),i.rows=O.slice(0,x.value?C+1:C*2),i.heights=Array.from({length:b}).fill(i.avgHeight),await new Promise(S=>setTimeout(S,300)),R!==i.updater)return;i.heights.fill(0,0,T),i.animationIndex+=T;const y=i.animationIndex-b;y>=0&&(i.animationIndex=y),i.animationHandler=setTimeout(j,g-300)}function L(){i.updater=(i.updater+1)%999999,i.animationHandler&&clearTimeout(i.animationHandler)}return(A,g)=>(fe(),de("div",{ref_key:"scrollRankingBoard",ref:n,class:"dv-scroll-ranking-board"},[(fe(!0),de(Se,null,qe(v(i).rows,(C,b)=>(fe(),de("div",{key:C.toString()+C.scroll,class:"row-item",style:Ve(`height: ${v(i).heights[b]}px;`)},[ae("div",OE,[ae("div",PE," No."+ct(C.ranking),1),ae("div",{class:"info-name",innerHTML:C.name},null,8,AE),ae("div",IE,ct(v(i).mergedConfig.valueFormatter?v(i).mergedConfig.valueFormatter(C):C.value+v(i).mergedConfig.unit),1)]),ae("div",kE,[ae("div",{class:"inside-column",style:Ve(`width: ${C.percent}%;`)},g[0]||(g[0]=[ae("div",{class:"shine"},null,-1)]),4)])],4))),128))],512))}},NE=sr($E,[["__scopeId","data-v-26f5f75f"]]),wd={install(e){e.component("DvScrollRankingBoard",NE)}},ME=["align","innerHTML"],RE=["align","onClick","onMouseenter","innerHTML"],LE={__name:"index",props:{config:{type:Object,default:()=>({})}},emits:["mouseover","click","getFirstRow"],setup(e,{expose:t,emit:n}){const r=e,i=n,o=ye(null),{width:s,height:a}=De(o,p,f),l=ke({defaultConfig:{header:[],data:[],rowNum:5,headerBGC:"#00BAFF",oddRowBGC:"#003B51",evenRowBGC:"#0A2732",waitTime:2e3,headerHeight:35,columnWidth:[],align:[],index:!1,indexHeader:"#",carousel:"single",hoverPause:!0},mergedConfig:null,header:[],rowsData:[],rows:[],widths:[],heights:[],avgHeight:0,aligns:[],animationIndex:0,animationHandler:"",updater:0,needCalc:!1});we(()=>r.config,()=>{b(),d()},{deep:!0}),Mn(()=>{b()}),t({updateRows:R});function u(T,O,y,S){const{ceils:H,rowIndex:I}=y;i("click",{row:H,ceil:S,rowIndex:I,columnIndex:O})}function c(T,O,y,S,H){if(T){const{ceils:I,rowIndex:M}=S;i("mouseover",{row:I,ceil:H,rowIndex:M,columnIndex:y})}l.mergedConfig.hoverPause&&(T?b():C(!0))}function f(){d()}function p(){l.mergedConfig&&(L(),A())}function d(){h(),x(),j(),L(),A(),g(),C(!0)}function h(){l.mergedConfig=je(Be(l.defaultConfig),r.config||{})}function x(){let{header:T}=l.mergedConfig;const{index:O,indexHeader:y}=l.mergedConfig;if(!T.length){T=[];return}T=[...T],O&&T.unshift(y),l.header=T}function j(){let{data:T}=l.mergedConfig;const{index:O,headerBGC:y,rowNum:S}=l.mergedConfig;O&&(T=T.map((I,M)=>{I=[...I];const z=`<span class="index" style="background-color: ${y};">${M+1}</span>`;return I.unshift(z),I})),T=T.map((I,M)=>({ceils:I,rowIndex:M}));const H=T.length;H>S&&H<2*S&&(T=[...T,...T]),T=T.map((I,M)=>({...I,scroll:M})),l.rowsData=T,l.rows=T}function L(){const{columnWidth:T,header:O}=l.mergedConfig,y=T.reduce((M,z)=>M+z,0);let S=0;l.rowsData[0]?S=l.rowsData[0].ceils.length:O.length&&(S=O.length);const H=(s.value-y)/(S-T.length),I=Array.from({length:S}).fill(H);l.widths=je(I,T)}function A(T=!1){const{headerHeight:O,rowNum:y,data:S}=l.mergedConfig;let H=a.value;l.header.length&&(H-=O);const I=H/y;l.avgHeight=I,T||(l.heights=Array.from({length:S.length}).fill(I))}function g(){const T=l.header.length,O=Array.from({length:T}).fill("left"),{align:y}=l.mergedConfig;l.aligns=je(O,y)}async function C(T=!1){l.needCalc&&(j(),A(),l.needCalc=!1);const{waitTime:O,carousel:y,rowNum:S}=l.mergedConfig,{updater:H}=l,I=l.rowsData.length;if(S>=I||(T&&await new Promise(J=>setTimeout(J,O)),H!==l.updater))return;const M=y==="single"?1:S,z=l.rowsData.slice(l.animationIndex);if(z.push(...l.rowsData.slice(0,l.animationIndex)),l.rows=z.slice(0,y==="page"?S*2:S+1),l.heights=Array.from({length:I}).fill(l.avgHeight),await new Promise(J=>setTimeout(J,300)),H!==l.updater)return;l.heights.splice(0,M,...Array.from({length:M}).fill(0)),l.animationIndex+=M;const Z=l.animationIndex-I;Z>=0&&(l.animationIndex=Z),l.animationHandler=setTimeout(C,O-300),i("getFirstRow",z[1])}function b(){l.updater=(l.updater+1)%999999,l.animationHandler&&clearTimeout(l.animationHandler)}function R(T,O){l.mergedConfig={...l.mergedConfig,data:[...T]},l.needCalc=!0,typeof O=="number"&&(l.animationIndex=O),l.animationHandler||C(!0)}return(T,O)=>(fe(),de("div",{ref_key:"scrollBoard",ref:o,class:"dv-scroll-board"},[v(l).header.length&&v(l).mergedConfig?(fe(),de("div",{key:0,class:"header",style:Ve(`background-color: ${v(l).mergedConfig.headerBGC};`)},[(fe(!0),de(Se,null,qe(v(l).header,(y,S)=>(fe(),de("div",{key:`${y}${S}`,class:"header-item",style:Ve(`
          height: ${v(l).mergedConfig.headerHeight}px;
          line-height: ${v(l).mergedConfig.headerHeight}px;
          width: ${v(l).widths[S]}px;
        `),align:v(l).aligns[S],innerHTML:y},null,12,ME))),128))],4)):Re("",!0),v(l).mergedConfig?(fe(),de("div",{key:1,class:"rows",style:Ve(`height: ${v(a)-(v(l).header.length?v(l).mergedConfig.headerHeight:0)}px;`)},[(fe(!0),de(Se,null,qe(v(l).rows,(y,S)=>(fe(),de("div",{key:`${y.toString()}${y.scroll}`,class:"row-item",style:Ve(`
          height: ${v(l).heights[S]}px;
          line-height: ${v(l).heights[S]}px;
          background-color: ${v(l).mergedConfig[y.rowIndex%2===0?"evenRowBGC":"oddRowBGC"]};
        `)},[(fe(!0),de(Se,null,qe(y.ceils,(H,I)=>(fe(),de("div",{key:`${H}${S}${I}`,class:"ceil",style:Ve(`width: ${v(l).widths[I]}px;`),align:v(l).aligns[I],onClick:M=>u(S,I,y,H),onMouseenter:M=>c(!0,S,I,y,H),onMouseleave:O[0]||(O[0]=M=>c(!1)),innerHTML:H},null,44,RE))),128))],4))),128))],4)):Re("",!0)],512))}},Td={install(e){e.component("DvScrollBoard",LE)}};var S0={},Od={},Vt={},Pd;function Wt(){if(Pd)return Vt;Pd=1;var e=We;Object.defineProperty(Vt,"__esModule",{value:!0}),Vt.filterNonNumber=i,Vt.deepMerge=o,Vt.mulAdd=s,Vt.mergeSameStackData=a,Vt.getTwoPointDistance=l,Vt.getLinearGradientColor=u,Vt.getPolylineLength=c,Vt.getPointToLineDistance=f,Vt.initNeedSeries=p,Vt.radianToAngle=d;var t=e(pt()),n=e(wt()),r=mt();function i(h){return h.filter(function(x){return typeof x=="number"})}function o(h,x){for(var j in x){if(h[j]&&(0,n.default)(h[j])==="object"){o(h[j],x[j]);continue}if((0,n.default)(x[j])==="object"){h[j]=(0,r.deepClone)(x[j],!0);continue}h[j]=x[j]}return h}function s(h){return h=i(h),h.reduce(function(x,j){return x+j},0)}function a(h,x){var j=h.stack;if(!j)return(0,t.default)(h.data);var L=x.filter(function(b){var R=b.stack;return R===j}),A=L.findIndex(function(b){var R=b.data;return R===h.data}),g=L.splice(0,A+1).map(function(b){var R=b.data;return R}),C=g[0].length;return new Array(C).fill(0).map(function(b,R){return s(g.map(function(T){return T[R]}))})}function l(h,x){var j=Math.abs(h[0]-x[0]),L=Math.abs(h[1]-x[1]);return Math.sqrt(j*j+L*L)}function u(h,x,j,L){if(!(!h||!x||!j||!L.length)){var A=L;typeof A=="string"&&(A=[L,L]);var g=h.createLinearGradient.apply(h,(0,t.default)(x).concat((0,t.default)(j))),C=1/(A.length-1);return A.forEach(function(b,R){return g.addColorStop(C*R,b)}),g}}function c(h){var x=new Array(h.length-1).fill(0).map(function(L,A){return[h[A],h[A+1]]}),j=x.map(function(L){return l.apply(void 0,(0,t.default)(L))});return s(j)}function f(h,x,j){var L=l(h,x),A=l(h,j),g=l(x,j);return .5*Math.sqrt((L+A+g)*(L+A-g)*(L+g-A)*(A+g-L))/g}function p(h,x,j){return h=h.filter(function(L){var A=L.type;return A===j}),h=h.map(function(L){return o((0,r.deepClone)(x,!0),L)}),h.filter(function(L){var A=L.show;return A})}function d(h){return h/Math.PI*180}return Vt}var w0=We,DE=w0(an()),Ad=w0(pt()),da=fa,FE=xu(),zi=mt(),jE=vo,BE=Wt();function Id(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function kd(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Id(Object(n),!0).forEach(function(r){(0,DE.default)(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Id(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var VE={shape:{rx:0,ry:0,ir:0,or:0,startAngle:0,endAngle:0,clockWise:!0},validator:function(e){var t=e.shape,n=["rx","ry","ir","or","startAngle","endAngle"];return n.find(function(r){return typeof t[r]!="number"})?(console.error("Pie shape configuration is abnormal!"),!1):!0},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,o=r.ry,s=r.ir,a=r.or,l=r.startAngle,u=r.endAngle,c=r.clockWise;i=parseInt(i)+.5,o=parseInt(o)+.5,n.arc(i,o,s>0?s:0,l,u,!c);var f=(0,zi.getCircleRadianPoint)(i,o,a,u).map(function(d){return parseInt(d)+.5}),p=(0,zi.getCircleRadianPoint)(i,o,s,l).map(function(d){return parseInt(d)+.5});n.lineTo.apply(n,(0,Ad.default)(f)),n.arc(i,o,a>0?a:0,u,l,c),n.lineTo.apply(n,(0,Ad.default)(p)),n.closePath(),n.stroke(),n.fill()}},HE={shape:{rx:0,ry:0,r:0,startAngle:0,endAngle:0,gradientStartAngle:null,gradientEndAngle:null},validator:function(e){var t=e.shape,n=["rx","ry","r","startAngle","endAngle"];return n.find(function(r){return typeof t[r]!="number"})?(console.error("AgArc shape configuration is abnormal!"),!1):!0},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.style,o=i.gradient;o=o.map(function(R){return(0,jE.getColorFromRgbValue)(R)}),o.length===1&&(o=[o[0],o[0]]);var s=o.length-1,a=r.gradientStartAngle,l=r.gradientEndAngle,u=r.startAngle,c=r.endAngle,f=r.r,p=r.rx,d=r.ry;a===null&&(a=u),l===null&&(l=c);var h=(l-a)/s;h===Math.PI*2&&(h=Math.PI*2-.001);for(var x=0;x<s;x++){n.beginPath();var j=(0,zi.getCircleRadianPoint)(p,d,f,u+h*x),L=(0,zi.getCircleRadianPoint)(p,d,f,u+h*(x+1)),A=(0,BE.getLinearGradientColor)(n,j,L,[o[x],o[x+1]]),g=u+h*x,C=u+h*(x+1),b=!1;if(C>c&&(C=c,b=!0),n.arc(p,d,f,g,C),n.strokeStyle=A,n.stroke(),b)break}}},GE={shape:{number:[],content:"",position:[0,0],toFixed:0,rowGap:0,formatter:null},validator:function(e){var t=e.shape,n=t.number,r=t.content,i=t.position;return!(n instanceof Array)||typeof r!="string"||!(i instanceof Array)?(console.error("NumberText shape configuration is abnormal!"),!1):!0},draw:function(e,t){var n=e.ctx,r=t.shape,i=r.number,o=r.content,s=r.toFixed,a=r.rowGap,l=r.formatter,u=o.split("{nt}"),c="";u.forEach(function(f,p){var d=i[p];typeof d!="number"&&(d=""),typeof d=="number"&&(d=d.toFixed(s),typeof l=="function"&&(d=l(d))),c+=f+(d||"")}),FE.text.draw({ctx:n},{shape:kd(kd({},r),{},{content:c,rowGap:a})})}},UE={shape:{x:0,y:0,w:0,h:0},validator:function(e){var t=e.shape,n=t.x,r=t.y,i=t.w,o=t.h;return typeof n!="number"||typeof r!="number"||typeof i!="number"||typeof o!="number"?(console.error("lineIcon shape configuration is abnormal!"),!1):!0},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.x,o=r.y,s=r.w,a=r.h,l=a/2;n.strokeStyle=n.fillStyle,n.moveTo(i,o+l),n.lineTo(i+s,o+l),n.lineWidth=1,n.stroke(),n.beginPath();var u=l-5*2;u<=0&&(u=3),n.arc(i+s/2,o+l,u,0,Math.PI*2),n.lineWidth=5,n.stroke(),n.fillStyle="#fff",n.fill()},hoverCheck:function(e,t){var n=t.shape,r=n.x,i=n.y,o=n.w,s=n.h;return(0,zi.checkPointIsInRect)(e,r,i,o,s)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.x,o=n.y,s=n.w,a=n.h;r.graphCenter=[i+s/2,o+a/2]}};(0,da.extendNewGraph)("pie",VE);(0,da.extendNewGraph)("agArc",HE);(0,da.extendNewGraph)("numberText",GE);(0,da.extendNewGraph)("lineIcon",UE);var $d={},Lo={},Nd={},hi={},Md;function WE(){if(Md)return hi;Md=1,Object.defineProperty(hi,"__esModule",{value:!0}),hi.colorConfig=void 0;var e=["#37a2da","#32c5e9","#67e0e3","#9fe6b8","#ffdb5c","#ff9f7f","#fb7293","#e062ae","#e690d1","#e7bcf3","#9d96f5","#8378ea","#96bfff"];return hi.colorConfig=e,hi}var gi={},Rd;function XE(){if(Rd)return gi;Rd=1,Object.defineProperty(gi,"__esModule",{value:!0}),gi.gridConfig=void 0;var e={left:"10%",right:"10%",top:60,bottom:60,style:{fill:"rgba(0, 0, 0, 0)"},rLevel:-30,animationCurve:"easeOutCubic",animationFrame:30};return gi.gridConfig=e,gi}var ur={},Ld;function KE(){if(Ld)return ur;Ld=1,Object.defineProperty(ur,"__esModule",{value:!0}),ur.yAxisConfig=ur.xAxisConfig=void 0;var e={name:"",show:!0,position:"bottom",nameGap:15,nameLocation:"end",nameTextStyle:{fill:"#333",fontSize:10},min:"20%",max:"20%",interval:null,minInterval:null,maxInterval:null,boundaryGap:null,splitNumber:5,axisLine:{show:!0,style:{stroke:"#333",lineWidth:1}},axisTick:{show:!0,style:{stroke:"#333",lineWidth:1}},axisLabel:{show:!0,formatter:null,style:{fill:"#333",fontSize:10,rotate:0}},splitLine:{show:!1,style:{stroke:"#d4d4d4",lineWidth:1}},rLevel:-20,animationCurve:"easeOutCubic",animationFrame:50};ur.xAxisConfig=e;var t={name:"",show:!0,position:"left",nameGap:15,nameLocation:"end",nameTextStyle:{fill:"#333",fontSize:10},min:"20%",max:"20%",interval:null,minInterval:null,maxInterval:null,boundaryGap:null,splitNumber:5,axisLine:{show:!0,style:{stroke:"#333",lineWidth:1}},axisTick:{show:!0,style:{stroke:"#333",lineWidth:1}},axisLabel:{show:!0,formatter:null,style:{fill:"#333",fontSize:10,rotate:0}},splitLine:{show:!0,style:{stroke:"#d4d4d4",lineWidth:1}},rLevel:-20,animationCurve:"easeOutCubic",animationFrame:50};return ur.yAxisConfig=t,ur}var mi={},Dd;function qE(){if(Dd)return mi;Dd=1,Object.defineProperty(mi,"__esModule",{value:!0}),mi.titleConfig=void 0;var e={show:!0,text:"",offset:[0,-20],style:{fill:"#333",fontSize:17,fontWeight:"bold",textAlign:"center",textBaseline:"bottom"},rLevel:20,animationCurve:"easeOutCubic",animationFrame:50};return mi.titleConfig=e,mi}var vi={},Fd;function zE(){if(Fd)return vi;Fd=1,Object.defineProperty(vi,"__esModule",{value:!0}),vi.lineConfig=void 0;var e={show:!0,name:"",stack:"",smooth:!1,xAxisIndex:0,yAxisIndex:0,data:[],lineStyle:{lineWidth:1},linePoint:{show:!0,radius:2,style:{fill:"#fff",lineWidth:1}},lineArea:{show:!1,gradient:[],style:{opacity:.5}},label:{show:!1,position:"top",offset:[0,-10],formatter:null,style:{fontSize:10}},rLevel:10,animationCurve:"easeOutCubic",animationFrame:50};return vi.lineConfig=e,vi}var yi={},jd;function JE(){if(jd)return yi;jd=1,Object.defineProperty(yi,"__esModule",{value:!0}),yi.barConfig=void 0;var e={show:!0,name:"",stack:"",shapeType:"normal",echelonOffset:10,barWidth:"auto",barGap:"30%",barCategoryGap:"20%",xAxisIndex:0,yAxisIndex:0,data:[],backgroundBar:{show:!1,width:"auto",style:{fill:"rgba(200, 200, 200, .4)"}},label:{show:!1,position:"top",offset:[0,-10],formatter:null,style:{fontSize:10}},gradient:{color:[],local:!0},barStyle:{},independentColor:!1,independentColors:[],rLevel:0,animationCurve:"easeOutCubic",animationFrame:50};return yi.barConfig=e,yi}var bi={},Bd;function T0(){if(Bd)return bi;Bd=1,Object.defineProperty(bi,"__esModule",{value:!0}),bi.pieConfig=void 0;var e={show:!0,name:"",radius:"50%",center:["50%","50%"],startAngle:-Math.PI/2,roseType:!1,roseSort:!0,roseIncrement:"auto",data:[],insideLabel:{show:!1,formatter:"{percent}%",style:{fontSize:10,fill:"#fff",textAlign:"center",textBaseline:"middle"}},outsideLabel:{show:!0,formatter:"{name}",style:{fontSize:11},labelLineBendGap:"20%",labelLineEndLength:50,labelLineStyle:{lineWidth:1}},pieStyle:{},percentToFixed:0,rLevel:10,animationDelayGap:60,animationCurve:"easeOutCubic",startAnimationCurve:"easeOutBack",animationFrame:50};return bi.pieConfig=e,bi}var _i={},Vd;function QE(){if(Vd)return _i;Vd=1,Object.defineProperty(_i,"__esModule",{value:!0}),_i.radarAxisConfig=void 0;var e={show:!0,center:["50%","50%"],radius:"65%",startAngle:-Math.PI/2,splitNum:5,polygon:!1,axisLabel:{show:!0,labelGap:15,color:[],style:{fill:"#333"}},axisLine:{show:!0,color:[],style:{stroke:"#999",lineWidth:1}},splitLine:{show:!0,color:[],style:{stroke:"#d4d4d4",lineWidth:1}},splitArea:{show:!1,color:["#f5f5f5","#e6e6e6"],style:{}},rLevel:-10,animationCurve:"easeOutCubic",animationFrane:50};return _i.radarAxisConfig=e,_i}var Ci={},Hd;function YE(){if(Hd)return Ci;Hd=1,Object.defineProperty(Ci,"__esModule",{value:!0}),Ci.radarConfig=void 0;var e={show:!0,name:"",data:[],radarStyle:{lineWidth:1},point:{show:!0,radius:2,style:{fill:"#fff"}},label:{show:!0,offset:[0,0],labelGap:5,formatter:null,style:{fontSize:10}},rLevel:10,animationCurve:"easeOutCubic",animationFrane:50};return Ci.radarConfig=e,Ci}var xi={},Gd;function O0(){if(Gd)return xi;Gd=1,Object.defineProperty(xi,"__esModule",{value:!0}),xi.gaugeConfig=void 0;var e={show:!0,name:"",radius:"60%",center:["50%","50%"],startAngle:-(Math.PI/4)*5,endAngle:Math.PI/4,min:0,max:100,splitNum:5,arcLineWidth:15,data:[],dataItemStyle:{},axisTick:{show:!0,tickLength:6,style:{stroke:"#999",lineWidth:1}},axisLabel:{show:!0,data:[],formatter:null,labelGap:5,style:{}},pointer:{show:!0,valueIndex:0,style:{scale:[1,1],fill:"#fb7293"}},details:{show:!1,formatter:null,offset:[0,0],valueToFixed:0,position:"center",style:{fontSize:20,fontWeight:"bold",textAlign:"center",textBaseline:"middle"}},backgroundArc:{show:!0,style:{stroke:"#e0e0e0"}},rLevel:10,animationCurve:"easeOutCubic",animationFrame:50};return xi.gaugeConfig=e,xi}var Ei={},Ud;function ZE(){if(Ud)return Ei;Ud=1,Object.defineProperty(Ei,"__esModule",{value:!0}),Ei.legendConfig=void 0;var e={show:!0,orient:"horizontal",left:"auto",right:"auto",top:"auto",bottom:"auto",itemGap:10,iconWidth:25,iconHeight:10,selectAble:!0,data:[],textStyle:{fontFamily:"Arial",fontSize:13,fill:"#000"},iconStyle:{},textUnselectedStyle:{fontFamily:"Arial",fontSize:13,fill:"#999"},iconUnselectedStyle:{fill:"#999"},rLevel:20,animationCurve:"easeOutCubic",animationFrame:50};return Ei.legendConfig=e,Ei}var Wd;function hn(){return Wd||(Wd=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.changeDefaultConfig=h,Object.defineProperty(e,"colorConfig",{enumerable:!0,get:function(){return t.colorConfig}}),Object.defineProperty(e,"gridConfig",{enumerable:!0,get:function(){return n.gridConfig}}),Object.defineProperty(e,"xAxisConfig",{enumerable:!0,get:function(){return r.xAxisConfig}}),Object.defineProperty(e,"yAxisConfig",{enumerable:!0,get:function(){return r.yAxisConfig}}),Object.defineProperty(e,"titleConfig",{enumerable:!0,get:function(){return i.titleConfig}}),Object.defineProperty(e,"lineConfig",{enumerable:!0,get:function(){return o.lineConfig}}),Object.defineProperty(e,"barConfig",{enumerable:!0,get:function(){return s.barConfig}}),Object.defineProperty(e,"pieConfig",{enumerable:!0,get:function(){return a.pieConfig}}),Object.defineProperty(e,"radarAxisConfig",{enumerable:!0,get:function(){return l.radarAxisConfig}}),Object.defineProperty(e,"radarConfig",{enumerable:!0,get:function(){return u.radarConfig}}),Object.defineProperty(e,"gaugeConfig",{enumerable:!0,get:function(){return c.gaugeConfig}}),Object.defineProperty(e,"legendConfig",{enumerable:!0,get:function(){return f.legendConfig}}),e.keys=void 0;var t=WE(),n=XE(),r=KE(),i=qE(),o=zE(),s=JE(),a=T0(),l=QE(),u=YE(),c=O0(),f=ZE(),p=Wt(),d={colorConfig:t.colorConfig,gridConfig:n.gridConfig,xAxisConfig:r.xAxisConfig,yAxisConfig:r.yAxisConfig,titleConfig:i.titleConfig,lineConfig:o.lineConfig,barConfig:s.barConfig,pieConfig:a.pieConfig,radarAxisConfig:l.radarAxisConfig,radarConfig:u.radarConfig,gaugeConfig:c.gaugeConfig,legendConfig:f.legendConfig};function h(j,L){if(!d["".concat(j,"Config")]){console.warn("Change default config Error - Invalid key!");return}(0,p.deepMerge)(d["".concat(j,"Config")],L)}var x=["color","title","legend","xAxis","yAxis","grid","radarAxis","line","bar","pie","radar","gauge"];e.keys=x}(Nd)),Nd}var Xd;function eS(){if(Xd)return Lo;Xd=1,Object.defineProperty(Lo,"__esModule",{value:!0}),Lo.mergeColor=r;var e=hn(),t=mt(),n=Wt();function r(i){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=(0,t.deepClone)(e.colorConfig,!0),a=o.color,l=o.series;if(l||(l=[]),a||(a=[]),o.color=a=(0,n.deepMerge)(s,a),!!l.length){var u=a.length;l.forEach(function(d,h){d.color||(d.color=a[h%u])});var c=l.filter(function(d){var h=d.type;return h==="pie"});c.forEach(function(d){return d.data.forEach(function(h,x){return h.color=a[x%u]})});var f=l.filter(function(d){var h=d.type;return h==="gauge"});f.forEach(function(d){return d.data.forEach(function(h,x){return h.color=a[x%u]})});var p=l.filter(function(d){var h=d.type,x=d.independentColor;return h==="bar"&&x});p.forEach(function(d){d.independentColors||(d.independentColors=a)})}}return Lo}var Do={},kr={},Kd;function gn(){if(Kd)return kr;Kd=1;var e=We;Object.defineProperty(kr,"__esModule",{value:!0}),kr.doUpdate=c,kr.Updater=void 0;var t=e(pt()),n=e(wt()),r=e(mo()),i=function f(p,d){(0,r.default)(this,f);var h=p.chart,x=p.key,j=p.getGraphConfig;if(typeof j!="function"){console.warn("Updater need function getGraphConfig!");return}h[x]||(this.graphs=h[x]=[]),Object.assign(this,p),this.update(d)};kr.Updater=i,i.prototype.update=function(f){var p=this,d=this.graphs,h=this.beforeUpdate;if(o(this,f),!!f.length){var x=(0,n.default)(h);f.forEach(function(j,L){x==="function"&&h(d,j,L,p);var A=d[L];A?s(A,j,L,p):l(d,j,L,p)})}};function o(f,p){var d=f.graphs,h=f.chart.render,x=d.length,j=p.length;if(x>j){var L=d.splice(j);L.forEach(function(A){return A.forEach(function(g){return h.delGraph(g)})})}}function s(f,p,d,h){var x=h.getGraphConfig,j=h.chart.render,L=h.beforeChange,A=x(p,h);a(f,A,j),f.forEach(function(g,C){var b=A[C];typeof L=="function"&&L(g,b),u(g,b)})}function a(f,p,d){var h=f.length,x=p.length;if(x>h){var j=f.slice(-1)[0],L=x-h,A=new Array(L).fill(0).map(function(C){return d.clone(j)});f.push.apply(f,(0,t.default)(A))}else if(x<h){var g=f.splice(x);g.forEach(function(C){return d.delGraph(C)})}}function l(f,p,d,h){var x=h.getGraphConfig,j=h.getStartGraphConfig,L=h.chart,A=L.render,g=null;typeof j=="function"&&(g=j(p,h));var C=x(p,h);if(C.length){g?(f[d]=g.map(function(R){return A.add(R)}),f[d].forEach(function(R,T){var O=C[T];u(R,O)})):f[d]=C.map(function(R){return A.add(R)});var b=h.afterAddGraph;typeof b=="function"&&b(f[d])}}function u(f,p){var d=Object.keys(p);d.forEach(function(h){h==="shape"||h==="style"?f.animation(h,p[h],!0):f[h]=p[h]})}function c(){var f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},p=f.chart,d=f.series,h=f.key,x=f.getGraphConfig,j=f.getStartGraphConfig,L=f.beforeChange,A=f.beforeUpdate,g=f.afterAddGraph;p[h]?p[h].update(d):p[h]=new i({chart:p,key:h,getGraphConfig:x,getStartGraphConfig:j,beforeChange:L,beforeUpdate:A,afterAddGraph:g},d)}return kr}var qd;function tS(){if(qd)return Do;qd=1;var e=We;Object.defineProperty(Do,"__esModule",{value:!0}),Do.title=s;var t=e(Nt()),n=gn(),r=mt(),i=hn(),o=Wt();function s(c){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},p=[];f.title&&(p[0]=(0,o.deepMerge)((0,r.deepClone)(i.titleConfig,!0),f.title)),(0,n.doUpdate)({chart:c,series:p,key:"title",getGraphConfig:a})}function a(c,f){var p=i.titleConfig.animationCurve,d=i.titleConfig.animationFrame,h=i.titleConfig.rLevel,x=l(c,f),j=u(c);return[{name:"text",index:h,visible:c.show,animationCurve:p,animationFrame:d,shape:x,style:j}]}function l(c,f){var p=c.offset,d=c.text,h=f.chart.gridArea,x=h.x,j=h.y,L=h.w,A=(0,t.default)(p,2),g=A[0],C=A[1];return{content:d,position:[x+L/2+g,j+C]}}function u(c){var f=c.style;return f}return Do}var Fo={},zd;function nS(){if(zd)return Fo;zd=1;var e=We;Object.defineProperty(Fo,"__esModule",{value:!0}),Fo.grid=u;var t=e(Nt()),n=e(an()),r=gn(),i=mt(),o=hn(),s=Wt();function a(h,x){var j=Object.keys(h);if(Object.getOwnPropertySymbols){var L=Object.getOwnPropertySymbols(h);x&&(L=L.filter(function(A){return Object.getOwnPropertyDescriptor(h,A).enumerable})),j.push.apply(j,L)}return j}function l(h){for(var x=1;x<arguments.length;x++){var j=arguments[x]!=null?arguments[x]:{};x%2?a(Object(j),!0).forEach(function(L){(0,n.default)(h,L,j[L])}):Object.getOwnPropertyDescriptors?Object.defineProperties(h,Object.getOwnPropertyDescriptors(j)):a(Object(j)).forEach(function(L){Object.defineProperty(h,L,Object.getOwnPropertyDescriptor(j,L))})}return h}function u(h){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},j=x.grid;j=(0,s.deepMerge)((0,i.deepClone)(o.gridConfig,!0),j||{}),(0,r.doUpdate)({chart:h,series:[j],key:"grid",getGraphConfig:c})}function c(h,x){var j=h.animationCurve,L=h.animationFrame,A=h.rLevel,g=f(h,x),C=d(h);return x.chart.gridArea=l({},g),[{name:"rect",index:A,animationCurve:j,animationFrame:L,shape:g,style:C}]}function f(h,x){var j=(0,t.default)(x.chart.render.area,2),L=j[0],A=j[1],g=p(h.left,L),C=p(h.right,L),b=p(h.top,A),R=p(h.bottom,A),T=L-g-C,O=A-b-R;return{x:g,y:b,w:T,h:O}}function p(h,x){return typeof h=="number"?h:typeof h!="string"?0:x*parseInt(h)/100}function d(h){var x=h.style;return x}return Fo}var jo={},Jd;function rS(){if(Jd)return jo;Jd=1;var e=We;Object.defineProperty(jo,"__esModule",{value:!0}),jo.axis=h;var t=e(wt()),n=e(Nt()),r=e(an()),i=e(pt()),o=gn(),s=hn(),a=Wt(),l=mt();function u(E,k){var $=Object.keys(E);if(Object.getOwnPropertySymbols){var F=Object.getOwnPropertySymbols(E);k&&(F=F.filter(function(Q){return Object.getOwnPropertyDescriptor(E,Q).enumerable})),$.push.apply($,F)}return $}function c(E){for(var k=1;k<arguments.length;k++){var $=arguments[k]!=null?arguments[k]:{};k%2?u(Object($),!0).forEach(function(F){(0,r.default)(E,F,$[F])}):Object.getOwnPropertyDescriptors?Object.defineProperties(E,Object.getOwnPropertyDescriptors($)):u(Object($)).forEach(function(F){Object.defineProperty(E,F,Object.getOwnPropertyDescriptor($,F))})}return E}var f={xAxisConfig:s.xAxisConfig,yAxisConfig:s.yAxisConfig},p=Math.abs,d=Math.pow;function h(E){var k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},$=k.xAxis,F=k.yAxis,Q=k.series,Y=[];$&&F&&Q&&(Y=x($,F),Y=j(Y),Y=Y.filter(function(oe){var ue=oe.show;return ue}),Y=L(Y),Y=A(Y,Q),Y=Z(Y),Y=J(Y,E),Y=P(Y),Y=D(Y),Y=V(Y,E)),(0,o.doUpdate)({chart:E,series:Y,key:"axisLine",getGraphConfig:X}),(0,o.doUpdate)({chart:E,series:Y,key:"axisTick",getGraphConfig:le}),(0,o.doUpdate)({chart:E,series:Y,key:"axisLabel",getGraphConfig:U}),(0,o.doUpdate)({chart:E,series:Y,key:"axisName",getGraphConfig:G}),(0,o.doUpdate)({chart:E,series:Y,key:"splitLine",getGraphConfig:K}),E.axisData=Y}function x(E,k){var $=[],F=[];if(E instanceof Array){var Q;(Q=$).push.apply(Q,(0,i.default)(E))}else $.push(E);if(k instanceof Array){var Y;(Y=F).push.apply(Y,(0,i.default)(k))}else F.push(k);return $.splice(2),F.splice(2),$=$.map(function(oe,ue){return c(c({},oe),{},{index:ue,axis:"x"})}),F=F.map(function(oe,ue){return c(c({},oe),{},{index:ue,axis:"y"})}),[].concat((0,i.default)($),(0,i.default)(F))}function j(E){var k=E.filter(function(F){var Q=F.axis;return Q==="x"}),$=E.filter(function(F){var Q=F.axis;return Q==="y"});return k=k.map(function(F){return(0,a.deepMerge)((0,l.deepClone)(s.xAxisConfig),F)}),$=$.map(function(F){return(0,a.deepMerge)((0,l.deepClone)(s.yAxisConfig),F)}),[].concat((0,i.default)(k),(0,i.default)($))}function L(E){var k=E.filter(function(F){var Q=F.data;return Q==="value"}),$=E.filter(function(F){var Q=F.data;return Q!=="value"});return k.forEach(function(F){typeof F.boundaryGap!="boolean"&&(F.boundaryGap=!1)}),$.forEach(function(F){typeof F.boundaryGap!="boolean"&&(F.boundaryGap=!0)}),[].concat((0,i.default)(k),(0,i.default)($))}function A(E,k){var $=E.filter(function(Q){var Y=Q.data;return Y==="value"}),F=E.filter(function(Q){var Y=Q.data;return Y instanceof Array});return $=g($,k),F=M(F),[].concat((0,i.default)($),(0,i.default)(F))}function g(E,k){return E.map(function($){var F=C($,k),Q=T($,F),Y=(0,n.default)(Q,2),oe=Y[0],ue=Y[1],pe=z(oe,ue,$),he=$.axisLabel.formatter,ge=[];return oe<0&&ue>0?ge=S(oe,ue,pe):ge=H(oe,ue,pe),ge=ge.map(function(be){return parseFloat(be.toFixed(2))}),c(c({},$),{},{maxValue:ge.slice(-1)[0],minValue:ge[0],label:I(ge,he)})})}function C(E,k){if(k=k.filter(function(oe){var ue=oe.show,pe=oe.type;return!(ue===!1||pe==="pie")}),k.length===0)return[0,0];var $=E.index,F=E.axis;k=R(k);var Q=F+"Axis",Y=k.filter(function(oe){return oe[Q]===$});return Y.length||(Y=k),b(Y)}function b(E){if(E){var k=Math.min.apply(Math,(0,i.default)(E.map(function(F){var Q=F.data;return Math.min.apply(Math,(0,i.default)((0,a.filterNonNumber)(Q)))}))),$=Math.max.apply(Math,(0,i.default)(E.map(function(F){var Q=F.data;return Math.max.apply(Math,(0,i.default)((0,a.filterNonNumber)(Q)))})));return[k,$]}}function R(E){var k=(0,l.deepClone)(E,!0);return E.forEach(function($,F){var Q=(0,a.mergeSameStackData)($,E);k[F].data=Q}),k}function T(E,k){var $=E.min,F=E.max,Q=E.axis,Y=(0,n.default)(k,2),oe=Y[0],ue=Y[1],pe=(0,t.default)($),he=(0,t.default)(F);if(y($)||($=f[Q+"AxisConfig"].min,pe="string"),y(F)||(F=f[Q+"AxisConfig"].max,he="string"),pe==="string"){$=parseInt(oe-p(oe*parseFloat($)/100));var ge=O($);$=parseFloat(($/ge-.1).toFixed(1))*ge}if(he==="string"){F=parseInt(ue+p(ue*parseFloat(F)/100));var be=O(F);F=parseFloat((F/be+.1).toFixed(1))*be}return[$,F]}function O(E){var k=p(E).toString(),$=k.length,F=k.replace(/0*$/g,"").indexOf("0"),Q=$-1;return F!==-1&&(Q-=F),d(10,Q)}function y(E){var k=(0,t.default)(E),$=k==="string"&&/^\d+%$/.test(E),F=k==="number";return $||F}function S(E,k,$){var F=[],Q=[],Y=0,oe=0;do F.push(Y-=$);while(Y>E);do Q.push(oe+=$);while(oe<k);return[].concat((0,i.default)(F.reverse()),[0],(0,i.default)(Q))}function H(E,k,$){var F=[E],Q=E;do F.push(Q+=$);while(Q<k);return F}function I(E,k){return k&&(typeof k=="string"&&(E=E.map(function($){return k.replace("{value}",$)})),typeof k=="function"&&(E=E.map(function($,F){return k({value:$,index:F})}))),E}function M(E){return E.map(function(k){var $=k.data,F=k.axisLabel.formatter;return c(c({},k),{},{label:I($,F)})})}function z(E,k,$){var F=$.interval,Q=$.minInterval,Y=$.maxInterval,oe=$.splitNumber,ue=$.axis,pe=f[ue+"AxisConfig"];if(typeof F!="number"&&(F=pe.interval),typeof Q!="number"&&(Q=pe.minInterval),typeof Y!="number"&&(Y=pe.maxInterval),typeof oe!="number"&&(oe=pe.splitNumber),typeof F=="number")return F;var he=parseInt((k-E)/(oe-1));return he.toString().length>1&&(he=parseInt(he.toString().replace(/\d$/,"0"))),he===0&&(he=1),typeof Q=="number"&&he<Q?Q:typeof Y=="number"&&he>Y?Y:he}function Z(E){var k=E.filter(function(F){var Q=F.axis;return Q==="x"}),$=E.filter(function(F){var Q=F.axis;return Q==="y"});return k[0]&&!k[0].position&&(k[0].position=s.xAxisConfig.position),k[1]&&!k[1].position&&(k[1].position=k[0].position==="bottom"?"top":"bottom"),$[0]&&!$[0].position&&($[0].position=s.yAxisConfig.position),$[1]&&!$[1].position&&($[1].position=$[0].position==="left"?"right":"left"),[].concat((0,i.default)(k),(0,i.default)($))}function J(E,k){var $=k.gridArea,F=$.x,Q=$.y,Y=$.w,oe=$.h;return E=E.map(function(ue){var pe=ue.position,he=[];return pe==="left"?he=[[F,Q],[F,Q+oe]].reverse():pe==="right"?he=[[F+Y,Q],[F+Y,Q+oe]].reverse():pe==="top"?he=[[F,Q],[F+Y,Q]]:pe==="bottom"&&(he=[[F,Q+oe],[F+Y,Q+oe]]),c(c({},ue),{},{linePosition:he})}),E}function P(E,k){return E.map(function($){var F=$.axis,Q=$.linePosition,Y=$.position,oe=$.label,ue=$.boundaryGap;typeof ue!="boolean"&&(ue=f[F+"AxisConfig"].boundaryGap);var pe=oe.length,he=(0,n.default)(Q,2),ge=(0,n.default)(he[0],2),be=ge[0],Te=ge[1],tt=(0,n.default)(he[1],2),Ye=tt[0],Mt=tt[1],Eo=F==="x"?Ye-be:Mt-Te,lt=Eo/(ue?pe:pe-1),Bt=new Array(pe).fill(0).map(function(fP,wo){return F==="x"?[be+lt*(ue?wo+.5:wo),Te]:[be,Te+lt*(ue?wo+.5:wo)]}),So=W(F,ue,Y,Bt,lt);return c(c({},$),{},{tickPosition:Bt,tickLinePosition:So,tickGap:lt})})}function W(E,k,$,F,Q){var Y=E==="x"?1:0,oe=5;E==="x"&&$==="top"&&(oe=-5),E==="y"&&$==="left"&&(oe=-5);var ue=F.map(function(pe){var he=(0,l.deepClone)(pe);return he[Y]+=oe,[(0,l.deepClone)(pe),he]});return k&&(Y=E==="x"?0:1,oe=Q/2,ue.forEach(function(pe){var he=(0,n.default)(pe,2),ge=he[0],be=he[1];ge[Y]+=oe,be[Y]+=oe})),ue}function D(E,k){return E.map(function($){var F=$.nameGap,Q=$.nameLocation,Y=$.position,oe=$.linePosition,ue=(0,n.default)(oe,2),pe=ue[0],he=ue[1],ge=(0,i.default)(pe);Q==="end"&&(ge=(0,i.default)(he)),Q==="center"&&(ge[0]=(pe[0]+he[0])/2,ge[1]=(pe[1]+he[1])/2);var be=0;Y==="top"&&Q==="center"&&(be=1),Y==="bottom"&&Q==="center"&&(be=1),Y==="left"&&Q!=="center"&&(be=1),Y==="right"&&Q!=="center"&&(be=1);var Te=F;return Y==="top"&&Q!=="end"&&(Te*=-1),Y==="left"&&Q!=="start"&&(Te*=-1),Y==="bottom"&&Q==="start"&&(Te*=-1),Y==="right"&&Q==="end"&&(Te*=-1),ge[be]+=Te,c(c({},$),{},{namePosition:ge})})}function V(E,k){var $=k.gridArea,F=$.w,Q=$.h;return E.map(function(Y){var oe=Y.tickLinePosition,ue=Y.position,pe=Y.boundaryGap,he=0,ge=F;(ue==="top"||ue==="bottom")&&(he=1),(ue==="top"||ue==="bottom")&&(ge=Q),(ue==="right"||ue==="bottom")&&(ge*=-1);var be=oe.map(function(Te){var tt=(0,n.default)(Te,1),Ye=tt[0],Mt=(0,i.default)(Ye);return Mt[he]+=ge,[(0,i.default)(Ye),Mt]});return pe||be.shift(),c(c({},Y),{},{splitLinePosition:be})})}function X(E){var k=E.animationCurve,$=E.animationFrame,F=E.rLevel;return[{name:"polyline",index:F,visible:E.axisLine.show,animationCurve:k,animationFrame:$,shape:te(E),style:ne(E)}]}function te(E){var k=E.linePosition;return{points:k}}function ne(E){return E.axisLine.style}function le(E){var k=E.animationCurve,$=E.animationFrame,F=E.rLevel,Q=ce(E),Y=me(E);return Q.map(function(oe){return{name:"polyline",index:F,visible:E.axisTick.show,animationCurve:k,animationFrame:$,shape:oe,style:Y}})}function ce(E){var k=E.tickLinePosition;return k.map(function($){return{points:$}})}function me(E){return E.axisTick.style}function U(E){var k=E.animationCurve,$=E.animationFrame,F=E.rLevel,Q=re(E),Y=se(E,Q);return Q.map(function(oe,ue){return{name:"text",index:F,visible:E.axisLabel.show,animationCurve:k,animationFrame:$,shape:oe,style:Y[ue],setGraphCenter:function(){}}})}function re(E){var k=E.label,$=E.tickPosition,F=E.position;return $.map(function(Q,Y){return{position:ie(Q,F),content:k[Y].toString()}})}function ie(E,k){var $=0,F=10;return(k==="top"||k==="bottom")&&($=1),(k==="top"||k==="left")&&(F=-10),E=(0,l.deepClone)(E),E[$]+=F,E}function se(E,k){var $=E.position,F=E.axisLabel.style,Q=q($);F=(0,a.deepMerge)(Q,F);var Y=k.map(function(oe){var ue=oe.position;return c(c({},F),{},{graphCenter:ue})});return Y}function q(E){if(E==="left")return{textAlign:"right",textBaseline:"middle"};if(E==="right")return{textAlign:"left",textBaseline:"middle"};if(E==="top")return{textAlign:"center",textBaseline:"bottom"};if(E==="bottom")return{textAlign:"center",textBaseline:"top"}}function G(E){var k=E.animationCurve,$=E.animationFrame,F=E.rLevel;return[{name:"text",index:F,animationCurve:k,animationFrame:$,shape:m(E),style:_(E)}]}function m(E){var k=E.name,$=E.namePosition;return{content:k,position:$}}function _(E){var k=E.nameLocation,$=E.position,F=E.nameTextStyle,Q=N($,k);return(0,a.deepMerge)(Q,F)}function N(E,k){if(E==="top"&&k==="start"||E==="bottom"&&k==="start"||E==="left"&&k==="center")return{textAlign:"right",textBaseline:"middle"};if(E==="top"&&k==="end"||E==="bottom"&&k==="end"||E==="right"&&k==="center")return{textAlign:"left",textBaseline:"middle"};if(E==="top"&&k==="center"||E==="left"&&k==="end"||E==="right"&&k==="end")return{textAlign:"center",textBaseline:"bottom"};if(E==="bottom"&&k==="center"||E==="left"&&k==="start"||E==="right"&&k==="start")return{textAlign:"center",textBaseline:"top"}}function K(E){var k=E.animationCurve,$=E.animationFrame,F=E.rLevel,Q=w(E),Y=B(E);return Q.map(function(oe){return{name:"polyline",index:F,visible:E.splitLine.show,animationCurve:k,animationFrame:$,shape:oe,style:Y}})}function w(E){var k=E.splitLinePosition;return k.map(function($){return{points:$}})}function B(E){return E.splitLine.style}return jo}var Bo={},Qd;function iS(){if(Qd)return Bo;Qd=1;var e=We;Object.defineProperty(Bo,"__esModule",{value:!0}),Bo.line=d;var t=e(wt()),n=e(Nt()),r=e(pt()),i=e(an()),o=gn(),s=hn(),a=e(Cu()),l=Wt();function u(U,re){var ie=Object.keys(U);if(Object.getOwnPropertySymbols){var se=Object.getOwnPropertySymbols(U);re&&(se=se.filter(function(q){return Object.getOwnPropertyDescriptor(U,q).enumerable})),ie.push.apply(ie,se)}return ie}function c(U){for(var re=1;re<arguments.length;re++){var ie=arguments[re]!=null?arguments[re]:{};re%2?u(Object(ie),!0).forEach(function(se){(0,i.default)(U,se,ie[se])}):Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(ie)):u(Object(ie)).forEach(function(se){Object.defineProperty(U,se,Object.getOwnPropertyDescriptor(ie,se))})}return U}var f=a.default.polylineToBezierCurve,p=a.default.getBezierCurveLength;function d(U){var re=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},ie=re.xAxis,se=re.yAxis,q=re.series,G=[];ie&&se&&q&&(G=(0,l.initNeedSeries)(q,s.lineConfig,"line"),G=h(G,U)),(0,o.doUpdate)({chart:U,series:G,key:"lineArea",getGraphConfig:g,getStartGraphConfig:O,beforeUpdate:y,beforeChange:S}),(0,o.doUpdate)({chart:U,series:G,key:"line",getGraphConfig:H,getStartGraphConfig:Z,beforeUpdate:y,beforeChange:S}),(0,o.doUpdate)({chart:U,series:G,key:"linePoint",getGraphConfig:J,getStartGraphConfig:D}),(0,o.doUpdate)({chart:U,series:G,key:"lineLabel",getGraphConfig:V})}function h(U,re){var ie=re.axisData;return U.map(function(se){var q=(0,l.mergeSameStackData)(se,U);q=x(se,q);var G=j(se,ie),m=L(q,G),_=A(G);return c(c({},se),{},{linePosition:m.filter(function(N){return N}),lineFillBottomPos:_})})}function x(U,re){var ie=U.data;return re.map(function(se,q){return typeof ie[q]=="number"?se:null})}function j(U,re){var ie=U.xAxisIndex,se=U.yAxisIndex,q=re.find(function(m){var _=m.axis,N=m.index;return _==="x"&&N===ie}),G=re.find(function(m){var _=m.axis,N=m.index;return _==="y"&&N===se});return[q,G]}function L(U,re){var ie=re.findIndex(function(Y){var oe=Y.data;return oe==="value"}),se=re[ie],q=re[1-ie],G=se.linePosition,m=se.axis,_=q.tickPosition,N=_.length,K=m==="x"?0:1,w=G[0][K],B=G[1][K],E=B-w,k=se.maxValue,$=se.minValue,F=k-$,Q=new Array(N).fill(0).map(function(Y,oe){var ue=U[oe];if(typeof ue!="number")return null;var pe=(ue-$)/F;return F===0&&(pe=0),pe*E+w});return Q.map(function(Y,oe){if(oe>=N||typeof Y!="number")return null;var ue=[Y,_[oe][1-K]];return K===0||ue.reverse(),ue})}function A(U){var re=U.find(function(B){var E=B.data;return E==="value"}),ie=re.axis,se=re.linePosition,q=re.minValue,G=re.maxValue,m=ie==="x"?0:1,_=se[0][m];if(q<0&&G>0){var N=G-q,K=Math.abs(se[0][m]-se[1][m]),w=Math.abs(q)/N*K;ie==="y"&&(w*=-1),_+=w}return{changeIndex:m,changeValue:_}}function g(U){var re=U.animationCurve,ie=U.animationFrame,se=U.lineFillBottomPos,q=U.rLevel;return[{name:I(U),index:q,animationCurve:re,animationFrame:ie,visible:U.lineArea.show,lineFillBottomPos:se,shape:C(U),style:b(U),drawed:T}]}function C(U){var re=U.linePosition;return{points:re}}function b(U){var re=U.lineArea,ie=U.color,se=re.gradient,q=re.style,G=[q.fill||ie],m=(0,l.deepMerge)(G,se);m.length===1&&m.push(m[0]);var _=R(U);return q=c(c({},q),{},{stroke:"rgba(0, 0, 0, 0)"}),(0,l.deepMerge)({gradientColor:m,gradientParams:_,gradientType:"linear",gradientWith:"fill"},q)}function R(U){var re=U.lineFillBottomPos,ie=U.linePosition,se=re.changeIndex,q=re.changeValue,G=ie.map(function(K){return K[se]}),m=Math.max.apply(Math,(0,r.default)(G)),_=Math.min.apply(Math,(0,r.default)(G)),N=m;return se===1&&(N=_),se===1?[0,N,0,q]:[N,0,q,0]}function T(U,re){var ie=U.lineFillBottomPos,se=U.shape,q=re.ctx,G=se.points,m=ie.changeIndex,_=ie.changeValue,N=(0,r.default)(G[G.length-1]),K=(0,r.default)(G[0]);N[m]=_,K[m]=_,q.lineTo.apply(q,(0,r.default)(N)),q.lineTo.apply(q,(0,r.default)(K)),q.closePath(),q.fill()}function O(U){var re=g(U)[0],ie=c({},re.style);return ie.opacity=0,re.style=ie,[re]}function y(U,re,ie,se){var q=U[ie];if(q){var G=I(re),m=se.chart.render,_=q[0].name,N=G!==_;N&&(q.forEach(function(K){return m.delGraph(K)}),U[ie]=null)}}function S(U,re){var ie=re.shape.points,se=U.shape.points,q=se.length,G=ie.length;if(G>q){var m=se.slice(-1)[0],_=new Array(G-q).fill(0).map(function(N){return(0,r.default)(m)});se.push.apply(se,(0,r.default)(_))}else G<q&&se.splice(G)}function H(U){var re=U.animationCurve,ie=U.animationFrame,se=U.rLevel;return[{name:I(U),index:se+1,animationCurve:re,animationFrame:ie,shape:C(U),style:M(U)}]}function I(U){var re=U.smooth;return re?"smoothline":"polyline"}function M(U){var re=U.lineStyle,ie=U.color,se=U.smooth,q=U.linePosition,G=z(q,se);return(0,l.deepMerge)({stroke:ie,lineDash:[G,0]},re)}function z(U){var re=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!re)return(0,l.getPolylineLength)(U);var ie=f(U);return p(ie)}function Z(U){var re=U.lineStyle.lineDash,ie=H(U)[0],se=ie.style.lineDash;return re?se=[0,0]:se=(0,r.default)(se).reverse(),ie.style.lineDash=se,[ie]}function J(U){var re=U.animationCurve,ie=U.animationFrame,se=U.rLevel,q=P(U),G=W(U);return q.map(function(m){return{name:"circle",index:se+2,visible:U.linePoint.show,animationCurve:re,animationFrame:ie,shape:m,style:G}})}function P(U){var re=U.linePosition,ie=U.linePoint.radius;return re.map(function(se){var q=(0,n.default)(se,2),G=q[0],m=q[1];return{r:ie,rx:G,ry:m}})}function W(U){var re=U.color,ie=U.linePoint.style;return(0,l.deepMerge)({stroke:re},ie)}function D(U){var re=J(U);return re.forEach(function(ie){ie.shape.r=.1}),re}function V(U){var re=U.animationCurve,ie=U.animationFrame,se=U.rLevel,q=X(U),G=me(U);return q.map(function(m,_){return{name:"text",index:se+3,visible:U.label.show,animationCurve:re,animationFrame:ie,shape:m,style:G}})}function X(U){var re=ce(U),ie=te(U);return re.map(function(se,q){return{content:se,position:ie[q]}})}function te(U){var re=U.linePosition,ie=U.lineFillBottomPos,se=U.label,q=se.position,G=se.offset,m=ie.changeIndex,_=ie.changeValue;return re.map(function(N){if(q==="bottom"&&(N=(0,r.default)(N),N[m]=_),q==="center"){var K=(0,r.default)(N);K[m]=_,N=le(N,K)}return ne(N,G)})}function ne(U,re){var ie=(0,n.default)(U,2),se=ie[0],q=ie[1],G=(0,n.default)(re,2),m=G[0],_=G[1];return[se+m,q+_]}function le(U,re){var ie=(0,n.default)(U,2),se=ie[0],q=ie[1],G=(0,n.default)(re,2),m=G[0],_=G[1];return[(se+m)/2,(q+_)/2]}function ce(U){var re=U.data,ie=U.label.formatter;if(re=re.filter(function(q){return typeof q=="number"}).map(function(q){return q.toString()}),!ie)return re;var se=(0,t.default)(ie);return se==="string"?re.map(function(q){return ie.replace("{value}",q)}):se==="function"?re.map(function(q,G){return ie({value:q,index:G})}):re}function me(U){var re=U.color,ie=U.label.style;return(0,l.deepMerge)({fill:re},ie)}return Bo}var Vo={},Yd;function oS(){if(Yd)return Vo;Yd=1;var e=We;Object.defineProperty(Vo,"__esModule",{value:!0}),Vo.bar=f;var t=e(wt()),n=e(an()),r=e(Nt()),i=e(pt()),o=gn(),s=hn(),a=mt(),l=Wt();function u(w,B){var E=Object.keys(w);if(Object.getOwnPropertySymbols){var k=Object.getOwnPropertySymbols(w);B&&(k=k.filter(function($){return Object.getOwnPropertyDescriptor(w,$).enumerable})),E.push.apply(E,k)}return E}function c(w){for(var B=1;B<arguments.length;B++){var E=arguments[B]!=null?arguments[B]:{};B%2?u(Object(E),!0).forEach(function(k){(0,n.default)(w,k,E[k])}):Object.getOwnPropertyDescriptors?Object.defineProperties(w,Object.getOwnPropertyDescriptors(E)):u(Object(E)).forEach(function(k){Object.defineProperty(w,k,Object.getOwnPropertyDescriptor(E,k))})}return w}function f(w){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=B.xAxis,k=B.yAxis,$=B.series,F=[];E&&k&&$&&(F=(0,l.initNeedSeries)($,s.barConfig,"bar"),F=p(F,w),F=d(F),F=T(F)),(0,o.doUpdate)({chart:w,series:F.slice(-1),key:"backgroundBar",getGraphConfig:z}),F.reverse(),(0,o.doUpdate)({chart:w,series:F,key:"bar",getGraphConfig:W,getStartGraphConfig:me,beforeUpdate:se}),(0,o.doUpdate)({chart:w,series:F,key:"barLabel",getGraphConfig:q})}function p(w,B){var E=B.axisData;return w.forEach(function(k){var $=k.xAxisIndex,F=k.yAxisIndex;typeof $!="number"&&($=0),typeof F!="number"&&(F=0);var Q=E.find(function(pe){var he=pe.axis,ge=pe.index;return"".concat(he).concat(ge)==="x".concat($)}),Y=E.find(function(pe){var he=pe.axis,ge=pe.index;return"".concat(he).concat(ge)==="y".concat(F)}),oe=[Q,Y],ue=oe.findIndex(function(pe){var he=pe.data;return he==="value"});k.valueAxis=oe[ue],k.labelAxis=oe[1-ue]}),w}function d(w,B){var E=x(w);return E.forEach(function(k){h(k),L(k),A(k),g(k),R(k)}),w}function h(w){var B=j(w);B=B.map(function(k){return{stack:k,index:-1}});var E=0;w.forEach(function(k){var $=k.stack;if(!$)k.barIndex=E,E++;else{var F=B.find(function(Q){var Y=Q.stack;return Y===$});F.index===-1&&(F.index=E,E++),k.barIndex=F.index}})}function x(w){var B=w.map(function(E){var k=E.labelAxis,$=k.axis,F=k.index;return $+F});return B=(0,i.default)(new Set(B)),B.map(function(E){return w.filter(function(k){var $=k.labelAxis,F=$.axis,Q=$.index;return F+Q===E})})}function j(w){var B=[];return w.forEach(function(E){var k=E.stack;k&&B.push(k)}),(0,i.default)(new Set(B))}function L(w){var B=(0,i.default)(new Set(w.map(function(E){var k=E.barIndex;return k}))).length;w.forEach(function(E){return E.barNum=B})}function A(w){var B=w.slice(-1)[0],E=B.barCategoryGap,k=B.labelAxis.tickGap,$=0;typeof E=="number"?$=E:$=(1-parseInt(E)/100)*k,w.forEach(function(F){return F.barCategoryWidth=$})}function g(w){var B=w.slice(-1)[0],E=B.barCategoryWidth,k=B.barWidth,$=B.barGap,F=B.barNum,Q=[];typeof k=="number"||k!=="auto"?Q=C(E,k,$):k==="auto"&&(Q=b(E,k,$,F));var Y=Q,oe=(0,r.default)(Y,2),ue=oe[0],pe=oe[1];w.forEach(function(he){he.barWidth=ue,he.barGap=pe})}function C(w,B,E){var k=0,$=0;return typeof B=="number"?k=B:k=parseInt(B)/100*w,typeof E=="number"?$=E:$=parseInt(E)/100*k,[k,$]}function b(w,B,E,k){var $=0,F=0,Q=w/k;if(typeof E=="number")F=E,$=Q-F;else{var Y=10+parseInt(E)/10;Y===0?($=Q*2,F=-$):($=Q/Y*10,F=Q-$)}return[$,F]}function R(w){var B=w.slice(-1)[0],E=B.barGap,k=B.barWidth,$=B.barNum,F=(E+k)*$-E;w.forEach(function(Q){return Q.barAllWidthAndGap=F})}function T(w,B){return w=y(w),w=O(w),w=H(w),w=I(w),w}function O(w){return w.map(function(B){var E=B.labelAxis,k=B.barAllWidthAndGap,$=B.barGap,F=B.barWidth,Q=B.barIndex,Y=E.tickGap,oe=E.tickPosition,ue=E.axis,pe=ue==="x"?0:1,he=oe.map(function(ge,be){var Te=oe[be][pe]-Y/2,tt=Te+(Y-k)/2;return tt+(Q+.5)*F+Q*$});return c(c({},B),{},{barLabelAxisPos:he})})}function y(w){return w.map(function(B){var E=(0,l.mergeSameStackData)(B,w);E=S(B,E);var k=B.valueAxis,$=k.axis,F=k.minValue,Q=k.maxValue,Y=k.linePosition,oe=M(F,Q,F<0?0:F,Y,$),ue=E.map(function(he){return M(F,Q,he,Y,$)}),pe=ue.map(function(he){return[oe,he]});return c(c({},B),{},{barValueAxisPos:pe})})}function S(w,B){var E=w.data;return B.map(function(k,$){return typeof E[$]=="number"?k:null}).filter(function(k){return k!==null})}function H(w){return w.map(function(B){var E=B.barLabelAxisPos,k=B.data;return k.forEach(function($,F){typeof $!="number"&&(E[F]=null)}),c(c({},B),{},{barLabelAxisPos:E.filter(function($){return $!==null})})})}function I(w){return w.forEach(function(B){var E=B.data,k=B.barLabelAxisPos,$=B.barValueAxisPos,F=E.filter(function(Y){return typeof Y=="number"}).length,Q=k.length;Q>F&&(k.splice(F),$.splice(F))}),w}function M(w,B,E,k,$){if(typeof E!="number")return null;var F=B-w,Q=$==="x"?0:1,Y=k[1][Q]-k[0][Q],oe=(E-w)/F;F===0&&(oe=0);var ue=oe*Y;return ue+k[0][Q]}function z(w){var B=w.animationCurve,E=w.animationFrame,k=w.rLevel,$=Z(w),F=P(w);return $.map(function(Q){return{name:"rect",index:k,visible:w.backgroundBar.show,animationCurve:B,animationFrame:E,shape:Q,style:F}})}function Z(w){var B=w.labelAxis,E=w.valueAxis,k=B.tickPosition,$=E.axis,F=E.linePosition,Q=J(w),Y=Q/2,oe=$==="x"?0:1,ue=k.map(function(be){return be[1-oe]}),pe=[F[0][oe],F[1][oe]],he=pe[0],ge=pe[1];return ue.map(function(be){return $==="x"?{x:he,y:be-Y,w:ge-he,h:Q}:{x:be-Y,y:ge,w:Q,h:he-ge}})}function J(w){var B=w.barAllWidthAndGap,E=w.barCategoryWidth,k=w.backgroundBar,$=k.width;return typeof $=="number"?$:$==="auto"?B:parseInt($)/100*E}function P(w){return w.backgroundBar.style}function W(w){var B=w.barLabelAxisPos,E=w.animationCurve,k=w.animationFrame,$=w.rLevel,F=D(w);return B.map(function(Q,Y){return{name:F,index:$,animationCurve:E,animationFrame:k,shape:V(w,Y),style:le(w,Y)}})}function D(w){var B=w.shapeType;return B==="leftEchelon"||B==="rightEchelon"?"polyline":"rect"}function V(w,B){var E=w.shapeType;return E==="leftEchelon"?X(w,B):E==="rightEchelon"?te(w,B):ne(w,B)}function X(w,B){var E=w.barValueAxisPos,k=w.barLabelAxisPos,$=w.barWidth,F=w.echelonOffset,Q=(0,r.default)(E[B],2),Y=Q[0],oe=Q[1],ue=k[B],pe=$/2,he=w.valueAxis.axis,ge=[];return he==="x"?(ge[0]=[oe,ue-pe],ge[1]=[oe,ue+pe],ge[2]=[Y,ue+pe],ge[3]=[Y+F,ue-pe],oe-Y<F&&ge.splice(3,1)):(ge[0]=[ue-pe,oe],ge[1]=[ue+pe,oe],ge[2]=[ue+pe,Y],ge[3]=[ue-pe,Y-F],Y-oe<F&&ge.splice(3,1)),{points:ge,close:!0}}function te(w,B){var E=w.barValueAxisPos,k=w.barLabelAxisPos,$=w.barWidth,F=w.echelonOffset,Q=(0,r.default)(E[B],2),Y=Q[0],oe=Q[1],ue=k[B],pe=$/2,he=w.valueAxis.axis,ge=[];return he==="x"?(ge[0]=[oe,ue+pe],ge[1]=[oe,ue-pe],ge[2]=[Y,ue-pe],ge[3]=[Y+F,ue+pe],oe-Y<F&&ge.splice(2,1)):(ge[0]=[ue+pe,oe],ge[1]=[ue-pe,oe],ge[2]=[ue-pe,Y],ge[3]=[ue+pe,Y-F],Y-oe<F&&ge.splice(2,1)),{points:ge,close:!0}}function ne(w,B){var E=w.barValueAxisPos,k=w.barLabelAxisPos,$=w.barWidth,F=(0,r.default)(E[B],2),Q=F[0],Y=F[1],oe=k[B],ue=w.valueAxis.axis,pe={};return ue==="x"?(pe.x=Q,pe.y=oe-$/2,pe.w=Y-Q,pe.h=$):(pe.x=oe-$/2,pe.y=Y,pe.w=$,pe.h=Q-Y),pe}function le(w,B){var E=w.barStyle,k=w.gradient,$=w.color,F=w.independentColor,Q=w.independentColors,Y=[E.fill||$],oe=(0,l.deepMerge)(Y,k.color);if(F){var ue=Q[B%Q.length];oe=ue instanceof Array?ue:[ue]}oe.length===1&&oe.push(oe[0]);var pe=ce(w,B);return(0,l.deepMerge)({gradientColor:oe,gradientParams:pe,gradientType:"linear",gradientWith:"fill"},E)}function ce(w,B){var E=w.barValueAxisPos,k=w.barLabelAxisPos,$=w.data,F=w.valueAxis,Q=F.linePosition,Y=F.axis,oe=(0,r.default)(E[B],2),ue=oe[0],pe=oe[1],he=k[B],ge=$[B],be=(0,r.default)(Q,2),Te=be[0],tt=be[1],Ye=Y==="x"?0:1,Mt=pe;return w.gradient.local||(Mt=ge<0?Te[Ye]:tt[Ye]),Y==="y"?[he,Mt,he,ue]:[Mt,he,ue,he]}function me(w){var B=W(w),E=w.shapeType;return B.forEach(function(k){var $=k.shape;E==="leftEchelon"?$=U($,w):E==="rightEchelon"?$=re($,w):$=ie($,w),k.shape=$}),B}function U(w,B){var E=B.valueAxis.axis;w=(0,a.deepClone)(w);var k=w,$=k.points,F=E==="x"?0:1,Q=$[2][F];return $.forEach(function(Y){return Y[F]=Q}),w}function re(w,B){var E=B.valueAxis.axis;w=(0,a.deepClone)(w);var k=w,$=k.points,F=E==="x"?0:1,Q=$[2][F];return $.forEach(function(Y){return Y[F]=Q}),w}function ie(w,B){var E=B.valueAxis.axis,k=w.x,$=w.y,F=w.w,Q=w.h;return E==="x"?F=0:($=$+Q,Q=0),{x:k,y:$,w:F,h:Q}}function se(w,B,E,k){var $=k.chart.render,F=D(B);w[E]&&w[E][0].name!==F&&(w[E].forEach(function(Q){return $.delGraph(Q)}),w[E]=null)}function q(w){var B=w.animationCurve,E=w.animationFrame,k=w.rLevel,$=G(w),F=K(w);return $.map(function(Q){return{name:"text",index:k,visible:w.label.show,animationCurve:B,animationFrame:E,shape:Q,style:F}})}function G(w){var B=m(w),E=_(w);return E.map(function(k,$){return{position:k,content:B[$]}})}function m(w){var B=w.data,E=w.label,k=E.formatter;if(B=B.filter(function(F){return typeof F=="number"}).map(function(F){return F.toString()}),!k)return B;var $=(0,t.default)(k);return $==="string"?B.map(function(F){return k.replace("{value}",F)}):$==="function"?B.map(function(F,Q){return k({value:F,index:Q})}):B}function _(w){var B=w.label,E=w.barValueAxisPos,k=w.barLabelAxisPos,$=B.position,F=B.offset,Q=w.valueAxis.axis;return E.map(function(Y,oe){var ue=(0,r.default)(Y,2),pe=ue[0],he=ue[1],ge=k[oe],be=[he,ge];return $==="bottom"&&(be=[pe,ge]),$==="center"&&(be=[(pe+he)/2,ge]),Q==="y"&&be.reverse(),N(be,F)})}function N(w,B){var E=(0,r.default)(w,2),k=E[0],$=E[1],F=(0,r.default)(B,2),Q=F[0],Y=F[1];return[k+Q,$+Y]}function K(w){var B=w.color,E=w.label.style,k=w.gradient.color;return k.length&&(B=k[0]),E=(0,l.deepMerge)({fill:B},E),E}return Vo}var Ho={},Zd;function sS(){if(Zd)return Ho;Zd=1;var e=We;Object.defineProperty(Ho,"__esModule",{value:!0}),Ho.pie=f;var t=e(an()),n=e(wt()),r=e(Nt()),i=e(pt()),o=gn(),s=T0(),a=mt(),l=Wt();function u(G,m){var _=Object.keys(G);if(Object.getOwnPropertySymbols){var N=Object.getOwnPropertySymbols(G);m&&(N=N.filter(function(K){return Object.getOwnPropertyDescriptor(G,K).enumerable})),_.push.apply(_,N)}return _}function c(G){for(var m=1;m<arguments.length;m++){var _=arguments[m]!=null?arguments[m]:{};m%2?u(Object(_),!0).forEach(function(N){(0,t.default)(G,N,_[N])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(_)):u(Object(_)).forEach(function(N){Object.defineProperty(G,N,Object.getOwnPropertyDescriptor(_,N))})}return G}function f(G){var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},_=m.series;_||(_=[]);var N=(0,l.initNeedSeries)(_,s.pieConfig,"pie");N=p(N,G),N=d(N,G),N=x(N),N=A(N),N=b(N),N=T(N),N=y(N),N=S(N),(0,o.doUpdate)({chart:G,series:N,key:"pie",getGraphConfig:J,getStartGraphConfig:P,beforeChange:W}),(0,o.doUpdate)({chart:G,series:N,key:"pieInsideLabel",getGraphConfig:X}),(0,o.doUpdate)({chart:G,series:N,key:"pieOutsideLabelLine",getGraphConfig:le,getStartGraphConfig:ce}),(0,o.doUpdate)({chart:G,series:N,key:"pieOutsideLabel",getGraphConfig:re,getStartGraphConfig:ie})}function p(G,m){var _=m.render.area;return G.forEach(function(N){var K=N.center;K=K.map(function(w,B){return typeof w=="number"?w:parseInt(w)/100*_[B]}),N.center=K}),G}function d(G,m){var _=Math.min.apply(Math,(0,i.default)(m.render.area))/2;return G.forEach(function(N){var K=N.radius,w=N.data;K=h(K,_),w.forEach(function(B){var E=B.radius;E||(E=K),E=h(E,_),B.radius=E}),N.radius=K}),G}function h(G,m){return G instanceof Array||(G=[0,G]),G=G.map(function(_){return typeof _=="number"?_:parseInt(_)/100*m}),G}function x(G,m){var _=G.filter(function(N){var K=N.roseType;return K});return _.forEach(function(N){var K=N.radius,w=N.data,B=N.roseSort,E=L(N),k=(0,i.default)(w);w=j(w),w.forEach(function($,F){$.radius[1]=K[1]-E*F}),B?w.reverse():N.data=k,N.roseIncrement=E}),G}function j(G){return G.sort(function(m,_){var N=m.value,K=_.value;if(N===K)return 0;if(N>K)return-1;if(N<K)return 1})}function L(G){var m=G.radius,_=G.roseIncrement;if(typeof _=="number")return _;if(_==="auto"){var N=G.data,K=N.reduce(function(E,k){var $=k.radius;return[].concat((0,i.default)(E),(0,i.default)($))},[]),w=Math.min.apply(Math,(0,i.default)(K)),B=Math.max.apply(Math,(0,i.default)(K));return(B-w)*.6/(N.length-1||1)}return parseInt(_)/100*m[1]}function A(G){return G.forEach(function(m){var _=m.data,N=m.percentToFixed,K=C(_);_.forEach(function(B){var E=B.value;B.percent=E/K*100,B.percentForLabel=g(E/K*100,N)});var w=(0,l.mulAdd)(_.slice(0,-1).map(function(B){var E=B.percent;return E}));_.slice(-1)[0].percent=100-w,_.slice(-1)[0].percentForLabel=g(100-w,N)}),G}function g(G){var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,_=G.toString(),N=_.split("."),K=N[1]||"0",w=K.slice(0,m);return N[1]=w,parseFloat(N.join("."))}function C(G){return(0,l.mulAdd)(G.map(function(m){var _=m.value;return _}))}function b(G){return G.forEach(function(m){var _=m.startAngle,N=m.data;N.forEach(function(K,w){var B=R(N,w),E=(0,r.default)(B,2),k=E[0],$=E[1];K.startAngle=_+k,K.endAngle=_+$})}),G}function R(G,m){var _=Math.PI*2,N=G.slice(0,m+1),K=(0,l.mulAdd)(N.map(function(E){var k=E.percent;return k})),w=G[m].percent,B=K-w;return[_*B/100,_*K/100]}function T(G){return G.forEach(function(m){var _=m.data;_.forEach(function(N){N.insideLabelPos=O(m,N)})}),G}function O(G,m){var _=G.center,N=m.startAngle,K=m.endAngle,w=(0,r.default)(m.radius,2),B=w[0],E=w[1],k=(B+E)/2,$=(N+K)/2;return a.getCircleRadianPoint.apply(void 0,(0,i.default)(_).concat([k,$]))}function y(G){return G.forEach(function(m){var _=m.data,N=m.center;_.forEach(function(K){var w=K.startAngle,B=K.endAngle,E=K.radius,k=(w+B)/2,$=a.getCircleRadianPoint.apply(void 0,(0,i.default)(N).concat([E[1],k]));K.edgeCenterPos=$})}),G}function S(G){return G.forEach(function(m){var _=M(m),N=M(m,!1);_=z(_),N=z(N),Z(_,m),Z(N,m,!1)}),G}function H(G){var m=G.outsideLabel.labelLineBendGap,_=I(G);return typeof m!="number"&&(m=parseInt(m)/100*_),m+_}function I(G){var m=G.data,_=m.map(function(N){var K=(0,r.default)(N.radius,2);K[0];var w=K[1];return w});return Math.max.apply(Math,(0,i.default)(_))}function M(G){var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,_=G.data,N=G.center,K=N[0];return _.filter(function(w){var B=w.edgeCenterPos,E=B[0];return m?E<=K:E>K})}function z(G){return G.sort(function(m,_){var N=(0,r.default)(m.edgeCenterPos,2);N[0];var K=N[1],w=(0,r.default)(_.edgeCenterPos,2);w[0];var B=w[1];if(K>B)return 1;if(K<B)return-1;if(K===B)return 0}),G}function Z(G,m){var _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,N=m.center,K=m.outsideLabel,w=H(m);G.forEach(function(B){var E=B.edgeCenterPos,k=B.startAngle,$=B.endAngle,F=K.labelLineEndLength,Q=(k+$)/2,Y=a.getCircleRadianPoint.apply(void 0,(0,i.default)(N).concat([w,Q])),oe=(0,i.default)(Y);oe[0]+=F*(_?-1:1),B.labelLine=[E,Y,oe],B.labelLineLength=(0,l.getPolylineLength)(B.labelLine),B.align={textAlign:"left",textBaseline:"middle"},_&&(B.align.textAlign="right")})}function J(G){var m=G.data,_=G.animationCurve,N=G.animationFrame,K=G.rLevel;return m.map(function(w,B){return{name:"pie",index:K,animationCurve:_,animationFrame:N,shape:D(G,B),style:V(G,B)}})}function P(G){var m=G.animationDelayGap,_=G.startAnimationCurve,N=J(G);return N.forEach(function(K,w){K.animationCurve=_,K.animationDelay=w*m,K.shape.or=K.shape.ir}),N}function W(G){G.animationDelay=0}function D(G,m){var _=G.center,N=G.data,K=N[m],w=K.radius,B=K.startAngle,E=K.endAngle;return{startAngle:B,endAngle:E,ir:w[0],or:w[1],rx:_[0],ry:_[1]}}function V(G,m){var _=G.pieStyle,N=G.data,K=N[m],w=K.color;return(0,l.deepMerge)({fill:w},_)}function X(G){var m=G.animationCurve,_=G.animationFrame,N=G.data,K=G.rLevel;return N.map(function(w,B){return{name:"text",index:K,visible:G.insideLabel.show,animationCurve:m,animationFrame:_,shape:te(G,B),style:ne(G)}})}function te(G,m){var _=G.insideLabel,N=G.data,K=_.formatter,w=N[m],B=(0,n.default)(K),E="";return B==="string"&&(E=K.replace("{name}",w.name),E=E.replace("{percent}",w.percentForLabel),E=E.replace("{value}",w.value)),B==="function"&&(E=K(w)),{content:E,position:w.insideLabelPos}}function ne(G,m){var _=G.insideLabel.style;return _}function le(G){var m=G.animationCurve,_=G.animationFrame,N=G.data,K=G.rLevel;return N.map(function(w,B){return{name:"polyline",index:K,visible:G.outsideLabel.show,animationCurve:m,animationFrame:_,shape:me(G,B),style:U(G,B)}})}function ce(G){var m=G.data,_=le(G);return _.forEach(function(N,K){N.style.lineDash=[0,m[K].labelLineLength]}),_}function me(G,m){var _=G.data,N=_[m];return{points:N.labelLine}}function U(G,m){var _=G.outsideLabel,N=G.data,K=_.labelLineStyle,w=N[m].color;return(0,l.deepMerge)({stroke:w,lineDash:[N[m].labelLineLength,0]},K)}function re(G){var m=G.animationCurve,_=G.animationFrame,N=G.data,K=G.rLevel;return N.map(function(w,B){return{name:"text",index:K,visible:G.outsideLabel.show,animationCurve:m,animationFrame:_,shape:se(G,B),style:q(G,B)}})}function ie(G){var m=G.data,_=re(G);return _.forEach(function(N,K){N.shape.position=m[K].labelLine[1]}),_}function se(G,m){var _=G.outsideLabel,N=G.data,K=_.formatter,w=N[m],B=w.labelLine,E=w.name,k=w.percentForLabel,$=w.value,F=(0,n.default)(K),Q="";return F==="string"&&(Q=K.replace("{name}",E),Q=Q.replace("{percent}",k),Q=Q.replace("{value}",$)),F==="function"&&(Q=K(N[m])),{content:Q,position:B[2]}}function q(G,m){var _=G.outsideLabel,N=G.data,K=N[m],w=K.color,B=K.align,E=_.style;return(0,l.deepMerge)(c({fill:w},B),E)}return Ho}var Go={},ep;function aS(){if(ep)return Go;ep=1;var e=We;Object.defineProperty(Go,"__esModule",{value:!0}),Go.radarAxis=c;var t=e(Nt()),n=e(an()),r=e(pt()),i=gn(),o=hn(),s=mt(),a=Wt();function l(P,W){var D=Object.keys(P);if(Object.getOwnPropertySymbols){var V=Object.getOwnPropertySymbols(P);W&&(V=V.filter(function(X){return Object.getOwnPropertyDescriptor(P,X).enumerable})),D.push.apply(D,V)}return D}function u(P){for(var W=1;W<arguments.length;W++){var D=arguments[W]!=null?arguments[W]:{};W%2?l(Object(D),!0).forEach(function(V){(0,n.default)(P,V,D[V])}):Object.getOwnPropertyDescriptors?Object.defineProperties(P,Object.getOwnPropertyDescriptors(D)):l(Object(D)).forEach(function(V){Object.defineProperty(P,V,Object.getOwnPropertyDescriptor(D,V))})}return P}function c(P){var W=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},D=W.radar,V=[];D&&(V=f(D),V=p(V,P),V=d(V,P),V=h(V),V=x(V),V=j(V),V=[V]);var X=V;V.length&&!V[0].show&&(X=[]),(0,i.doUpdate)({chart:P,series:X,key:"radarAxisSplitArea",getGraphConfig:L,beforeUpdate:C,beforeChange:b}),(0,i.doUpdate)({chart:P,series:X,key:"radarAxisSplitLine",getGraphConfig:R,beforeUpdate:y,beforeChange:S}),(0,i.doUpdate)({chart:P,series:X,key:"radarAxisLine",getGraphConfig:H}),(0,i.doUpdate)({chart:P,series:X,key:"radarAxisLable",getGraphConfig:z}),P.radarAxis=V[0]}function f(P){return(0,a.deepMerge)((0,s.deepClone)(o.radarAxisConfig),P)}function p(P,W){var D=W.render.area,V=P.center;return P.centerPos=V.map(function(X,te){return typeof X=="number"?X:parseInt(X)/100*D[te]}),P}function d(P,W){var D=W.render.area,V=P.splitNum,X=P.radius,te=Math.min.apply(Math,(0,r.default)(D))/2;typeof X!="number"&&(X=parseInt(X)/100*te);var ne=X/V;return P.ringRadius=new Array(V).fill(0).map(function(le,ce){return ne*(ce+1)}),P.radius=X,P}function h(P){var W=P.indicator,D=P.centerPos,V=P.radius,X=P.startAngle,te=Math.PI*2,ne=W.length,le=te/ne,ce=new Array(ne).fill(0).map(function(me,U){return le*U+X});return P.axisLineAngles=ce,P.axisLinePosition=ce.map(function(me){return s.getCircleRadianPoint.apply(void 0,(0,r.default)(D).concat([V,me]))}),P}function x(P){var W=P.ringRadius,D=W[0]/2;return P.areaRadius=W.map(function(V){return V-D}),P}function j(P){var W=P.axisLineAngles,D=P.centerPos,V=P.radius,X=P.axisLabel;return V+=X.labelGap,P.axisLabelPosition=W.map(function(te){return s.getCircleRadianPoint.apply(void 0,(0,r.default)(D).concat([V,te]))}),P}function L(P){var W=P.areaRadius,D=P.polygon,V=P.animationCurve,X=P.animationFrame,te=P.rLevel,ne=D?"regPolygon":"ring";return W.map(function(le,ce){return{name:ne,index:te,visible:P.splitArea.show,animationCurve:V,animationFrame:X,shape:A(P,ce),style:g(P,ce)}})}function A(P,W){var D=P.polygon,V=P.areaRadius,X=P.indicator,te=P.centerPos,ne=X.length,le={rx:te[0],ry:te[1],r:V[W]};return D&&(le.side=ne),le}function g(P,W){var D=P.splitArea,V=P.ringRadius,X=P.axisLineAngles,te=P.polygon,ne=P.centerPos,le=D.color,ce=D.style;ce=u({fill:"rgba(0, 0, 0, 0)"},ce);var me=V[0]-0;if(te){var U=s.getCircleRadianPoint.apply(void 0,(0,r.default)(ne).concat([V[0],X[0]])),re=s.getCircleRadianPoint.apply(void 0,(0,r.default)(ne).concat([V[0],X[1]]));me=(0,a.getPointToLineDistance)(ne,U,re)}if(ce=(0,a.deepMerge)((0,s.deepClone)(ce,!0),{lineWidth:me}),!le.length)return ce;var ie=le.length;return(0,a.deepMerge)(ce,{stroke:le[W%ie]})}function C(P,W,D,V){var X=P[D];if(X){var te=V.chart.render,ne=W.polygon,le=X[0].name,ce=ne?"regPolygon":"ring",me=ce!==le;me&&(X.forEach(function(U){return te.delGraph(U)}),P[D]=null)}}function b(P,W){var D=W.shape.side;typeof D=="number"&&(P.shape.side=D)}function R(P){var W=P.ringRadius,D=P.polygon,V=P.animationCurve,X=P.animationFrame,te=P.rLevel,ne=D?"regPolygon":"ring";return W.map(function(le,ce){return{name:ne,index:te,animationCurve:V,animationFrame:X,visible:P.splitLine.show,shape:T(P,ce),style:O(P,ce)}})}function T(P,W){var D=P.ringRadius,V=P.centerPos,X=P.indicator,te=P.polygon,ne={rx:V[0],ry:V[1],r:D[W]},le=X.length;return te&&(ne.side=le),ne}function O(P,W){var D=P.splitLine,V=D.color,X=D.style;if(X=u({fill:"rgba(0, 0, 0, 0)"},X),!V.length)return X;var te=V.length;return(0,a.deepMerge)(X,{stroke:V[W%te]})}function y(P,W,D,V){var X=P[D];if(X){var te=V.chart.render,ne=W.polygon,le=X[0].name,ce=ne?"regPolygon":"ring",me=ce!==le;me&&(X.forEach(function(U){return te.delGraph(U)}),P[D]=null)}}function S(P,W){var D=W.shape.side;typeof D=="number"&&(P.shape.side=D)}function H(P){var W=P.axisLinePosition,D=P.animationCurve,V=P.animationFrame,X=P.rLevel;return W.map(function(te,ne){return{name:"polyline",index:X,visible:P.axisLine.show,animationCurve:D,animationFrame:V,shape:I(P,ne),style:M(P,ne)}})}function I(P,W){var D=P.centerPos,V=P.axisLinePosition,X=[D,V[W]];return{points:X}}function M(P,W){var D=P.axisLine,V=D.color,X=D.style;if(!V.length)return X;var te=V.length;return(0,a.deepMerge)(X,{stroke:V[W%te]})}function z(P){var W=P.axisLabelPosition,D=P.animationCurve,V=P.animationFrame,X=P.rLevel;return W.map(function(te,ne){return{name:"text",index:X,visible:P.axisLabel.show,animationCurve:D,animationFrame:V,shape:Z(P,ne),style:J(P,ne)}})}function Z(P,W){var D=P.axisLabelPosition,V=P.indicator;return{content:V[W].name,position:D[W]}}function J(P,W){var D=P.axisLabel,V=(0,t.default)(P.centerPos,2),X=V[0],te=V[1],ne=P.axisLabelPosition,le=D.color,ce=D.style,me=(0,t.default)(ne[W],2),U=me[0],re=me[1],ie=U>X?"left":"right",se=re>te?"top":"bottom";if(ce=(0,a.deepMerge)({textAlign:ie,textBaseline:se},ce),!le.length)return ce;var q=le.length;return(0,a.deepMerge)(ce,{fill:le[W%q]})}return Go}var Uo={},tp;function lS(){if(tp)return Uo;tp=1;var e=We;Object.defineProperty(Uo,"__esModule",{value:!0}),Uo.radar=p;var t=e(an()),n=e(wt()),r=e(Nt()),i=e(pt()),o=gn(),s=hn(),a=mt(),l=vo,u=Wt();function c(M,z){var Z=Object.keys(M);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(M);z&&(J=J.filter(function(P){return Object.getOwnPropertyDescriptor(M,P).enumerable})),Z.push.apply(Z,J)}return Z}function f(M){for(var z=1;z<arguments.length;z++){var Z=arguments[z]!=null?arguments[z]:{};z%2?c(Object(Z),!0).forEach(function(J){(0,t.default)(M,J,Z[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(M,Object.getOwnPropertyDescriptors(Z)):c(Object(Z)).forEach(function(J){Object.defineProperty(M,J,Object.getOwnPropertyDescriptor(Z,J))})}return M}function p(M){var z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=z.series;Z||(Z=[]);var J=(0,u.initNeedSeries)(Z,s.radarConfig,"radar");J=d(J,M),J=h(J,M),J=x(J,M),(0,o.doUpdate)({chart:M,series:J,key:"radar",getGraphConfig:j,getStartGraphConfig:L,beforeChange:C}),(0,o.doUpdate)({chart:M,series:J,key:"radarPoint",getGraphConfig:b,getStartGraphConfig:R}),(0,o.doUpdate)({chart:M,series:J,key:"radarLabel",getGraphConfig:y})}function d(M,z){var Z=z.radarAxis;if(!Z)return[];var J=Z.indicator,P=Z.axisLineAngles,W=Z.radius,D=Z.centerPos;return M.forEach(function(V){var X=V.data;V.dataRadius=[],V.radarPosition=J.map(function(te,ne){var le=te.max,ce=te.min,me=X[ne];typeof le!="number"&&(le=me),typeof ce!="number"&&(ce=0),typeof me!="number"&&(me=ce);var U=(me-ce)/(le-ce)*W;return V.dataRadius[ne]=U,a.getCircleRadianPoint.apply(void 0,(0,i.default)(D).concat([U,P[ne]]))})}),M}function h(M,z){var Z=z.radarAxis;if(!Z)return[];var J=Z.centerPos,P=Z.axisLineAngles;return M.forEach(function(W){var D=W.dataRadius,V=W.label,X=V.labelGap;W.labelPosition=D.map(function(te,ne){return a.getCircleRadianPoint.apply(void 0,(0,i.default)(J).concat([te+X,P[ne]]))})}),M}function x(M,z){var Z=z.radarAxis;if(!Z)return[];var J=(0,r.default)(Z.centerPos,2),P=J[0],W=J[1];return M.forEach(function(D){var V=D.labelPosition,X=V.map(function(te){var ne=(0,r.default)(te,2),le=ne[0],ce=ne[1],me=le>P?"left":"right",U=ce>W?"top":"bottom";return{textAlign:me,textBaseline:U}});D.labelAlign=X}),M}function j(M){var z=M.animationCurve,Z=M.animationFrame,J=M.rLevel;return[{name:"polyline",index:J,animationCurve:z,animationFrame:Z,shape:A(M),style:g(M)}]}function L(M,z){var Z=z.chart.radarAxis.centerPos,J=j(M)[0],P=J.shape.points.length,W=new Array(P).fill(0).map(function(D){return(0,i.default)(Z)});return J.shape.points=W,[J]}function A(M){var z=M.radarPosition;return{points:z,close:!0}}function g(M){var z=M.radarStyle,Z=M.color,J=(0,l.getRgbaValue)(Z);J[3]=.5;var P={stroke:Z,fill:(0,l.getColorFromRgbValue)(J)};return(0,u.deepMerge)(P,z)}function C(M,z){var Z=z.shape,J=M.shape.points,P=J.length,W=Z.points.length;if(W>P){var D=J.slice(-1)[0],V=new Array(W-P).fill(0).map(function(X){return(0,i.default)(D)});J.push.apply(J,(0,i.default)(V))}else W<P&&J.splice(W)}function b(M){var z=M.radarPosition,Z=M.animationCurve,J=M.animationFrame,P=M.rLevel;return z.map(function(W,D){return{name:"circle",index:P,animationCurve:Z,animationFrame:J,visible:M.point.show,shape:T(M,D),style:O(M)}})}function R(M){var z=b(M);return z.forEach(function(Z){return Z.shape.r=.01}),z}function T(M,z){var Z=M.radarPosition,J=M.point,P=J.radius,W=Z[z];return{rx:W[0],ry:W[1],r:P}}function O(M,z){var Z=M.point,J=M.color,P=Z.style;return(0,u.deepMerge)({stroke:J},P)}function y(M){var z=M.labelPosition,Z=M.animationCurve,J=M.animationFrame,P=M.rLevel;return z.map(function(W,D){return{name:"text",index:P,visible:M.label.show,animationCurve:Z,animationFrame:J,shape:S(M,D),style:I(M,D)}})}function S(M,z){var Z=M.labelPosition,J=M.label,P=M.data,W=J.offset,D=J.formatter,V=H(Z[z],W),X=P[z]?P[z].toString():"0",te=(0,n.default)(D);return te==="string"&&(X=D.replace("{value}",X)),te==="function"&&(X=D(X)),{content:X,position:V}}function H(M,z){var Z=(0,r.default)(M,2),J=Z[0],P=Z[1],W=(0,r.default)(z,2),D=W[0],V=W[1];return[J+D,P+V]}function I(M,z){var Z=M.label,J=M.color,P=M.labelAlign,W=Z.style,D=f({fill:J},P[z]);return(0,u.deepMerge)(D,W)}return Uo}var Wo={},np;function uS(){if(np)return Wo;np=1;var e=We;Object.defineProperty(Wo,"__esModule",{value:!0}),Wo.gauge=p;var t=e(an()),n=e(wt()),r=e(Nt()),i=e(pt()),o=gn(),s=O0(),a=mt(),l=Wt(),u=vo;function c(q,G){var m=Object.keys(q);if(Object.getOwnPropertySymbols){var _=Object.getOwnPropertySymbols(q);G&&(_=_.filter(function(N){return Object.getOwnPropertyDescriptor(q,N).enumerable})),m.push.apply(m,_)}return m}function f(q){for(var G=1;G<arguments.length;G++){var m=arguments[G]!=null?arguments[G]:{};G%2?c(Object(m),!0).forEach(function(_){(0,t.default)(q,_,m[_])}):Object.getOwnPropertyDescriptors?Object.defineProperties(q,Object.getOwnPropertyDescriptors(m)):c(Object(m)).forEach(function(_){Object.defineProperty(q,_,Object.getOwnPropertyDescriptor(m,_))})}return q}function p(q){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=G.series;m||(m=[]);var _=(0,l.initNeedSeries)(m,s.gaugeConfig,"gauge");_=d(_,q),_=h(_,q),_=x(_,q),_=j(_),_=L(_),_=A(_),_=g(_),_=C(_),_=b(_),_=R(_),(0,o.doUpdate)({chart:q,series:_,key:"gaugeAxisTick",getGraphConfig:O}),(0,o.doUpdate)({chart:q,series:_,key:"gaugeAxisLabel",getGraphConfig:H}),(0,o.doUpdate)({chart:q,series:_,key:"gaugeBackgroundArc",getGraphConfig:z,getStartGraphConfig:P}),(0,o.doUpdate)({chart:q,series:_,key:"gaugeArc",getGraphConfig:W,getStartGraphConfig:X,beforeChange:te}),(0,o.doUpdate)({chart:q,series:_,key:"gaugePointer",getGraphConfig:ne,getStartGraphConfig:U}),(0,o.doUpdate)({chart:q,series:_,key:"gaugeDetails",getGraphConfig:re})}function d(q,G){var m=G.render.area;return q.forEach(function(_){var N=_.center;N=N.map(function(K,w){return typeof K=="number"?K:parseInt(K)/100*m[w]}),_.center=N}),q}function h(q,G){var m=G.render.area,_=Math.min.apply(Math,(0,i.default)(m))/2;return q.forEach(function(N){var K=N.radius;typeof K!="number"&&(K=parseInt(K)/100*_),N.radius=K}),q}function x(q,G){var m=G.render.area,_=Math.min.apply(Math,(0,i.default)(m))/2;return q.forEach(function(N){var K=N.radius,w=N.data,B=N.arcLineWidth;w.forEach(function(E){var k=E.radius,$=E.lineWidth;k||(k=K),typeof k!="number"&&(k=parseInt(k)/100*_),E.radius=k,$||($=B),E.lineWidth=$})}),q}function j(q,G){return q.forEach(function(m){var _=m.startAngle,N=m.endAngle,K=m.data,w=m.min,B=m.max,E=N-_,k=B-w;K.forEach(function($){var F=$.value,Q=Math.abs((F-w)/k*E);$.startAngle=_,$.endAngle=_+Q})}),q}function L(q,G){return q.forEach(function(m){var _=m.data;_.forEach(function(N){var K=N.color,w=N.gradient;(!w||!w.length)&&(w=K),w instanceof Array||(w=[w]),N.gradient=w})}),q}function A(q,G){return q.forEach(function(m){var _=m.startAngle,N=m.endAngle,K=m.splitNum,w=m.center,B=m.radius,E=m.arcLineWidth,k=m.axisTick,$=k.tickLength,F=k.style.lineWidth,Q=N-_,Y=B-E/2,oe=Y-$,ue=Q/(K-1),pe=2*Math.PI*B*Q/(Math.PI*2),he=Math.ceil(F/2)/pe*Q;m.tickAngles=[],m.tickInnerRadius=[],m.tickPosition=new Array(K).fill(0).map(function(ge,be){var Te=_+ue*be;return be===0&&(Te+=he),be===K-1&&(Te-=he),m.tickAngles[be]=Te,m.tickInnerRadius[be]=oe,[a.getCircleRadianPoint.apply(void 0,(0,i.default)(w).concat([Y,Te])),a.getCircleRadianPoint.apply(void 0,(0,i.default)(w).concat([oe,Te]))]})}),q}function g(q,G){return q.forEach(function(m){var _=m.center,N=m.tickInnerRadius,K=m.tickAngles,w=m.axisLabel.labelGap,B=K.map(function(k,$){return a.getCircleRadianPoint.apply(void 0,(0,i.default)(_).concat([N[$]-w,K[$]]))}),E=B.map(function(k){var $=(0,r.default)(k,2),F=$[0],Q=$[1];return{textAlign:F>_[0]?"right":"left",textBaseline:Q>_[1]?"bottom":"top"}});m.labelPosition=B,m.labelAlign=E}),q}function C(q,G){return q.forEach(function(m){var _=m.axisLabel,N=m.min,K=m.max,w=m.splitNum,B=_.data,E=_.formatter,k=(K-N)/(w-1),$=new Array(w).fill(0).map(function(Q,Y){return parseInt(N+k*Y)}),F=(0,n.default)(E);B=(0,l.deepMerge)($,B).map(function(Q,Y){var oe=Q;return F==="string"&&(oe=E.replace("{value}",Q)),F==="function"&&(oe=E({value:Q,index:Y})),oe}),_.data=B}),q}function b(q,G){return q.forEach(function(m){var _=m.data,N=m.details,K=m.center,w=N.position,B=N.offset,E=_.map(function(k){var $=k.startAngle,F=k.endAngle,Q=k.radius,Y=null;return w==="center"?Y=K:w==="start"?Y=a.getCircleRadianPoint.apply(void 0,(0,i.default)(K).concat([Q,$])):w==="end"&&(Y=a.getCircleRadianPoint.apply(void 0,(0,i.default)(K).concat([Q,F]))),T(Y,B)});m.detailsPosition=E}),q}function R(q,G){return q.forEach(function(m){var _=m.data,N=m.details,K=N.formatter,w=(0,n.default)(K),B=_.map(function(E){var k=E.value;return w==="string"&&(k=K.replace("{value}","{nt}"),k=k.replace("{name}",E.name)),w==="function"&&(k=K(E)),k.toString()});m.detailsContent=B}),q}function T(q,G){var m=(0,r.default)(q,2),_=m[0],N=m[1],K=(0,r.default)(G,2),w=K[0],B=K[1];return[_+w,N+B]}function O(q){var G=q.tickPosition,m=q.animationCurve,_=q.animationFrame,N=q.rLevel;return G.map(function(K,w){return{name:"polyline",index:N,visible:q.axisTick.show,animationCurve:m,animationFrame:_,shape:y(q,w),style:S(q)}})}function y(q,G){var m=q.tickPosition;return{points:m[G]}}function S(q,G){var m=q.axisTick.style;return m}function H(q){var G=q.labelPosition,m=q.animationCurve,_=q.animationFrame,N=q.rLevel;return G.map(function(K,w){return{name:"text",index:N,visible:q.axisLabel.show,animationCurve:m,animationFrame:_,shape:I(q,w),style:M(q,w)}})}function I(q,G){var m=q.labelPosition,_=q.axisLabel.data;return{content:_[G].toString(),position:m[G]}}function M(q,G){var m=q.labelAlign,_=q.axisLabel,N=_.style;return(0,l.deepMerge)(f({},m[G]),N)}function z(q){var G=q.animationCurve,m=q.animationFrame,_=q.rLevel;return[{name:"arc",index:_,visible:q.backgroundArc.show,animationCurve:G,animationFrame:m,shape:Z(q),style:J(q)}]}function Z(q){var G=q.startAngle,m=q.endAngle,_=q.center,N=q.radius;return{rx:_[0],ry:_[1],r:N,startAngle:G,endAngle:m}}function J(q){var G=q.backgroundArc,m=q.arcLineWidth,_=G.style;return(0,l.deepMerge)({lineWidth:m},_)}function P(q){var G=z(q)[0],m=f({},G.shape);return m.endAngle=G.shape.startAngle,G.shape=m,[G]}function W(q){var G=q.data,m=q.animationCurve,_=q.animationFrame,N=q.rLevel;return G.map(function(K,w){return{name:"agArc",index:N,animationCurve:m,animationFrame:_,shape:D(q,w),style:V(q,w)}})}function D(q,G){var m=q.data,_=q.center,N=q.endAngle,K=m[G],w=K.radius,B=K.startAngle,E=K.endAngle,k=K.localGradient;return k&&(N=E),{rx:_[0],ry:_[1],r:w,startAngle:B,endAngle:E,gradientEndAngle:N}}function V(q,G){var m=q.data,_=q.dataItemStyle,N=m[G],K=N.lineWidth,w=N.gradient;return w=w.map(function(B){return(0,u.getRgbaValue)(B)}),(0,l.deepMerge)({lineWidth:K,gradient:w},_)}function X(q){var G=W(q);return G.map(function(m){var _=f({},m.shape);_.endAngle=m.shape.startAngle,m.shape=_}),G}function te(q,G){var m=q.style.gradient,_=m.length,N=G.style.gradient.length;if(_>N)m.splice(N);else{var K=m.slice(-1)[0];m.push.apply(m,(0,i.default)(new Array(N-_).fill(0).map(function(w){return(0,i.default)(K)})))}}function ne(q){var G=q.animationCurve,m=q.animationFrame,_=q.center,N=q.rLevel;return[{name:"polyline",index:N,visible:q.pointer.show,animationCurve:G,animationFrame:m,shape:le(q),style:ce(q),setGraphCenter:function(K,w){w.style.graphCenter=_}}]}function le(q){var G=q.center;return{points:me(G),close:!0}}function ce(q){var G=q.startAngle,m=q.endAngle,_=q.min,N=q.max,K=q.data,w=q.pointer,B=q.center,E=w.valueIndex,k=w.style,$=K[E]?K[E].value:0,F=($-_)/(N-_)*(m-G)+G+Math.PI/2;return(0,l.deepMerge)({rotate:(0,l.radianToAngle)(F),scale:[1,1],graphCenter:B},k)}function me(q){var G=(0,r.default)(q,2),m=G[0],_=G[1],N=[m,_-40],K=[m+5,_],w=[m,_+10],B=[m-5,_];return[N,K,w,B]}function U(q){var G=q.startAngle,m=ne(q)[0];return m.style.rotate=(0,l.radianToAngle)(G+Math.PI/2),[m]}function re(q){var G=q.detailsPosition,m=q.animationCurve,_=q.animationFrame,N=q.rLevel,K=q.details.show;return G.map(function(w,B){return{name:"numberText",index:N,visible:K,animationCurve:m,animationFrame:_,shape:ie(q,B),style:se(q,B)}})}function ie(q,G){var m=q.detailsPosition,_=q.detailsContent,N=q.data,K=q.details,w=m[G],B=_[G],E=N[G].value,k=K.valueToFixed;return{number:[E],content:B,position:w,toFixed:k}}function se(q,G){var m=q.details,_=q.data,N=m.style,K=_[G].color;return(0,l.deepMerge)({fill:K},N)}return Wo}var Xo={},rp;function cS(){if(rp)return Xo;rp=1;var e=We;Object.defineProperty(Xo,"__esModule",{value:!0}),Xo.legend=l;var t=e(an()),n=e(Nt()),r=e(wt()),i=gn(),o=mt(),s=hn(),a=Wt();function l(P){var W=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},D=W.legend;D?(D=(0,a.deepMerge)((0,o.deepClone)(s.legendConfig,!0),D),D=u(D),D=c(D,W,P),D=f(D,P),D=h(D,P),D=[D]):D=[],(0,i.doUpdate)({chart:P,series:D,key:"legendIcon",getGraphConfig:y}),(0,i.doUpdate)({chart:P,series:D,key:"legendText",getGraphConfig:I})}function u(P){var W=P.data;return P.data=W.map(function(D){var V=(0,r.default)(D);return V==="string"?{name:D}:V==="object"?D:{name:""}}),P}function c(P,W,D){var V=W.series,X=D.legendStatus,te=P.data.filter(function(ne){var le=ne.name,ce=V.find(function(me){var U=me.name;return le===U});return ce?(ne.color||(ne.color=ce.color),ne.icon||(ne.icon=ce.type),ne):!1});return(!X||X.length!==P.data.length)&&(X=new Array(P.data.length).fill(!0)),te.forEach(function(ne,le){return ne.status=X[le]}),P.data=te,D.legendStatus=X,P}function f(P,W){var D=W.render.ctx,V=P.data,X=P.textStyle,te=P.textUnselectedStyle;return V.forEach(function(ne){var le=ne.status,ce=ne.name;ne.textWidth=p(D,ce,le?X:te)}),P}function p(P,W,D){return P.font=d(D),P.measureText(W).width}function d(P){var W=P.fontFamily,D=P.fontSize;return"".concat(D,"px ").concat(W)}function h(P,W){var D=P.orient;return D==="vertical"?b(P,W):x(P,W),P}function x(P,W){var D=P.iconHeight,V=P.itemGap,X=j(P,W),te=X.map(function(ce){return A(ce,P,W)}),ne=g(P,W),le={textAlign:"left",textBaseline:"middle"};X.forEach(function(ce,me){return ce.forEach(function(U){var re=U.iconPosition,ie=U.textPosition,se=te[me],q=ne+me*(V+D);U.iconPosition=C(re,[se,q]),U.textPosition=C(ie,[se,q]),U.align=le})})}function j(P,W){var D=P.data,V=P.iconWidth,X=W.render.area[0],te=0,ne=[[]];return D.forEach(function(le,ce){var me=L(te,ce,P),U=me+V+5+le.textWidth;U>=X&&(te=ce,me=L(te,ce,P),ne.push([])),le.iconPosition=[me,0],le.textPosition=[me+V+5,0],ne.slice(-1)[0].push(le)}),ne}function L(P,W,D){var V=D.data,X=D.iconWidth,te=D.itemGap,ne=V.slice(P,W);return(0,a.mulAdd)(ne.map(function(le){var ce=le.textWidth;return ce}))+(W-P)*(te+5+X)}function A(P,W,D){var V=W.left,X=W.right,te=W.iconWidth,ne=W.itemGap,le=D.render.area[0],ce=P.length,me=(0,a.mulAdd)(P.map(function(re){var ie=re.textWidth;return ie}))+ce*(5+te)+(ce-1)*ne,U=[V,X].findIndex(function(re){return re!=="auto"});return U===-1?(le-me)/2:U===0?typeof V=="number"?V:parseInt(V)/100*le:(typeof X!="number"&&(X=parseInt(X)/100*le),le-(me+X))}function g(P,W){var D=P.top,V=P.bottom,X=P.iconHeight,te=W.render.area[1],ne=[D,V].findIndex(function(re){return re!=="auto"}),le=X/2;if(ne===-1){var ce=W.gridArea,me=ce.y,U=ce.h;return me+U+45-le}else return ne===0?typeof D=="number"?D-le:parseInt(D)/100*te-le:(typeof V!="number"&&(V=parseInt(V)/100*te),te-V-le)}function C(P,W){var D=(0,n.default)(P,2),V=D[0],X=D[1],te=(0,n.default)(W,2),ne=te[0],le=te[1];return[V+ne,X+le]}function b(P,W){var D=R(P,W),V=(0,n.default)(D,2),X=V[0],te=V[1],ne=T(P,W);O(P,X);var le={textAlign:"left",textBaseline:"middle"};P.data.forEach(function(ce){var me=ce.textPosition,U=ce.iconPosition;ce.textPosition=C(me,[te,ne]),ce.iconPosition=C(U,[te,ne]),ce.align=le})}function R(P,W){var D=P.left,V=P.right,X=W.render.area[0],te=[D,V].findIndex(function(le){return le!=="auto"});if(te===-1)return[!0,X-10];var ne=[D,V][te];return typeof ne!="number"&&(ne=parseInt(ne)/100*X),[!!te,ne]}function T(P,W){var D=P.iconHeight,V=P.itemGap,X=P.data,te=P.top,ne=P.bottom,le=W.render.area[1],ce=X.length,me=ce*D+(ce-1)*V,U=[te,ne].findIndex(function(ie){return ie!=="auto"});if(U===-1)return(le-me)/2;var re=[te,ne][U];return typeof re!="number"&&(re=parseInt(re)/100*le),U===1&&(re=le-re-me),re}function O(P,W){var D=P.data,V=P.iconWidth,X=P.iconHeight,te=P.itemGap,ne=X/2;D.forEach(function(le,ce){var me=le.textWidth,U=(X+te)*ce+ne,re=W?0-V:0,ie=W?re-5-me:V+5;le.iconPosition=[re,U],le.textPosition=[ie,U]})}function y(P,W){var D=P.data,V=P.selectAble,X=P.animationCurve,te=P.animationFrame,ne=P.rLevel;return D.map(function(le,ce){return(0,t.default)({name:le.icon==="line"?"lineIcon":"rect",index:ne,visible:P.show,hover:V,click:V,animationCurve:X,animationFrame:te,shape:S(P,ce),style:H(P,ce)},"click",J(P,ce,W))})}function S(P,W){var D=P.data,V=P.iconWidth,X=P.iconHeight,te=(0,n.default)(D[W].iconPosition,2),ne=te[0],le=te[1],ce=X/2;return{x:ne,y:le-ce,w:V,h:X}}function H(P,W){var D=P.data,V=P.iconStyle,X=P.iconUnselectedStyle,te=D[W],ne=te.status,le=te.color,ce=ne?V:X;return(0,a.deepMerge)({fill:le},ce)}function I(P,W){var D=P.data,V=P.selectAble,X=P.animationCurve,te=P.animationFrame,ne=P.rLevel;return D.map(function(le,ce){return{name:"text",index:ne,visible:P.show,hover:V,animationCurve:X,animationFrame:te,hoverRect:Z(P,ce),shape:M(P,ce),style:z(P,ce),click:J(P,ce,W)}})}function M(P,W){var D=P.data[W],V=D.textPosition,X=D.name;return{content:X,position:V}}function z(P,W){var D=P.textStyle,V=P.textUnselectedStyle,X=P.data[W],te=X.status,ne=X.align,le=te?D:V;return(0,a.deepMerge)((0,o.deepClone)(le,!0),ne)}function Z(P,W){var D=P.textStyle,V=P.textUnselectedStyle,X=P.data[W],te=X.status,ne=(0,n.default)(X.textPosition,2),le=ne[0],ce=ne[1],me=X.textWidth,U=te?D:V,re=U.fontSize;return[le,ce-re/2,me,re]}function J(P,W,D){var V=P.data[W].name;return function(){var X=D.chart,te=X.legendStatus,ne=X.option,le=!te[W],ce=ne.series.find(function(me){var U=me.name;return U===V});ce.show=le,te[W]=le,D.chart.setOption(ne)}}return Xo}var ip;function fS(){return ip||(ip=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"mergeColor",{enumerable:!0,get:function(){return t.mergeColor}}),Object.defineProperty(e,"title",{enumerable:!0,get:function(){return n.title}}),Object.defineProperty(e,"grid",{enumerable:!0,get:function(){return r.grid}}),Object.defineProperty(e,"axis",{enumerable:!0,get:function(){return i.axis}}),Object.defineProperty(e,"line",{enumerable:!0,get:function(){return o.line}}),Object.defineProperty(e,"bar",{enumerable:!0,get:function(){return s.bar}}),Object.defineProperty(e,"pie",{enumerable:!0,get:function(){return a.pie}}),Object.defineProperty(e,"radarAxis",{enumerable:!0,get:function(){return l.radarAxis}}),Object.defineProperty(e,"radar",{enumerable:!0,get:function(){return u.radar}}),Object.defineProperty(e,"gauge",{enumerable:!0,get:function(){return c.gauge}}),Object.defineProperty(e,"legend",{enumerable:!0,get:function(){return f.legend}});var t=eS(),n=tS(),r=nS(),i=rS(),o=iS(),s=oS(),a=sS(),l=aS(),u=lS(),c=uS(),f=cS()}($d)),$d}var op;function dS(){return op||(op=1,function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t(wt()),r=t(mo()),i=t(fa),o=mt(),s=fS(),a=function l(u){if((0,r.default)(this,l),!u)return console.error("Charts Missing parameters!"),!1;var c=u.clientWidth,f=u.clientHeight,p=document.createElement("canvas");p.setAttribute("width",c),p.setAttribute("height",f),u.appendChild(p);var d={container:u,canvas:p,render:new i.default(p),option:null};Object.assign(this,d)};e.default=a,a.prototype.setOption=function(l){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!l||(0,n.default)(l)!=="object")return console.error("setOption Missing parameters!"),!1;u&&this.render.graphs.forEach(function(f){return f.animationEnd()});var c=(0,o.deepClone)(l,!0);(0,s.mergeColor)(this,c),(0,s.grid)(this,c),(0,s.axis)(this,c),(0,s.radarAxis)(this,c),(0,s.title)(this,c),(0,s.bar)(this,c),(0,s.line)(this,c),(0,s.pie)(this,c),(0,s.radar)(this,c),(0,s.gauge)(this,c),(0,s.legend)(this,c),this.option=l,this.render.launchAnimation()},a.prototype.resize=function(){var l=this.container,u=this.canvas,c=this.render,f=this.option,p=l.clientWidth,d=l.clientHeight;u.setAttribute("width",p),u.setAttribute("height",d),c.area=[p,d],this.setOption(f)}}(Od)),Od}(function(e){var t=We;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"changeDefaultConfig",{enumerable:!0,get:function(){return r.changeDefaultConfig}}),e.default=void 0;var n=t(dS()),r=hn(),i=n.default;e.default=i})(S0);const P0=b0(S0),pS={__name:"index",props:{option:{type:Object,default:()=>({})}},setup(e){const t=e,n=ye(null),r=ye(null);let i=ke({});De(r,a,o),we(()=>t.option,()=>{i&&i.setOption(t.option,!0)},{deep:!0});function o(){s()}function s(){i=new P0(r.value),t.option&&i.setOption(t.option)}function a(){i&&i.resize()}return(l,u)=>(fe(),de("div",{ref_key:"chartsContainerRef",ref:n,class:"dv-charts-container"},[ae("div",{ref_key:"chartRef",ref:r,class:"charts-canvas-container"},null,512)],512))}},sp={install(e){e.component("DvCharts",pS)}},hS={class:"dv-capsule-chart"},gS={class:"label-column"},mS={class:"capsule-container"},vS={key:0,class:"capsule-item-value"},yS={class:"unit-label"},bS={key:0,class:"unit-text"},_S={__name:"index",props:{config:{type:Object,default:()=>({})}},setup(e){Or(l=>({"1b634ae3":v(i),"63348aba":v(r)}));const t=e,n=ke({defaultConfig:{data:[],colors:["#37a2da","#32c5e9","#67e0e3","#9fe6b8","#ffdb5c","#ff9f7f","#fb7293"],unit:"",showValue:!1,textColor:"#fff",fontSize:12,labelNum:6},mergedConfig:null,capsuleLength:[],capsuleValue:[],labelData:[],labelDataLength:[]});we(()=>t.config,()=>{o()},{deep:!0});const r=Ce(()=>`${t.config.fontSize?t.config.fontSize:n.defaultConfig.fontSize}px`),i=Ce(()=>t.config.textColor?t.config.textColor:n.defaultConfig.textColor);function o(){s(),a()}function s(){n.mergedConfig=je(Be(n.defaultConfig),t.config||{})}function a(){const{data:l,labelNum:u}=n.mergedConfig;if(!l.length||l.length===0){n.labelData=[],n.capsuleLength=[];return}const c=l.map(({value:h})=>h),f=Math.max(...c);n.capsuleValue=c,n.capsuleLength=c.map(h=>f?h/f:0);const p=f/5,d=Array.from(new Set(Array.from({length:u}).fill(0).map((h,x)=>Math.ceil(x*p))));n.labelData=d,n.labelDataLength=Array.from(d).map(h=>f?h/f:0)}return Ue(()=>{o()}),(l,u)=>(fe(),de("div",hS,[v(n).mergedConfig?(fe(),de(Se,{key:0},[ae("div",gS,[(fe(!0),de(Se,null,qe(v(n).mergedConfig.data,c=>(fe(),de("div",{key:c.name},ct(c.name),1))),128)),u[0]||(u[0]=ae("div",null," ",-1))]),ae("div",mS,[(fe(!0),de(Se,null,qe(v(n).capsuleLength,(c,f)=>(fe(),de("div",{key:f,class:"capsule-item"},[ae("div",{class:"capsule-item-column",style:Ve(`width: ${c*100}%; background-color: ${v(n).mergedConfig.colors[f%v(n).mergedConfig.colors.length]};`)},[v(n).mergedConfig.showValue?(fe(),de("div",vS,ct(v(n).capsuleValue[f]),1)):Re("",!0)],4)]))),128)),ae("div",yS,[(fe(!0),de(Se,null,qe(v(n).labelData,(c,f)=>(fe(),de("div",{key:c+f},ct(c),1))),128))])]),v(n).mergedConfig.unit?(fe(),de("div",bS,ct(v(n).mergedConfig.unit),1)):Re("",!0)],64)):Re("",!0)]))}},ap={install(e){e.component("DvCapsuleChart",_S)}},CS={class:"dv-digital-flop"},A0={__name:"index",props:{config:{type:Object,default:()=>{}}},setup(e){const t=e,n=ye(null),r=ke({renderer:null,defaultConfig:{number:[],content:"",toFixed:0,textAlign:"center",rowGap:0,style:{fontSize:30,fill:"#3de7c9"},formatter:void 0,animationCurve:"easeOutCubic",animationFrame:50},mergedConfig:null,graph:null});we(()=>t.config,()=>{c()},{deep:!0}),Ue(()=>{i()});function i(){o(),s(),a()}function o(){r.renderer=new E0(n.value)}function s(){r.mergedConfig=je(Be(r.defaultConfig),t.config||{})}function a(){const p=l(),d=u();r.graph=r.renderer.add({name:"numberText",animationCurve:r.mergedConfig.animationCurve,animationFrame:r.mergedConfig.animationFrame,shape:p,style:d})}function l(){const{number:p,content:d,toFixed:h,textAlign:x,rowGap:j,formatter:L}=r.mergedConfig,[A,g]=r.renderer.area,C=[A/2,g/2];return x==="left"&&(C[0]=0),x==="right"&&(C[0]=A),{number:p,content:d,toFixed:h,position:C,rowGap:j,formatter:L}}function u(){const{style:p,textAlign:d}=r.mergedConfig;return je(p,{textAlign:d,textBaseline:"middle"})}function c(){if(r.graph.animationEnd(),s(),!r.graph)return;const{animationCurve:p,animationFrame:d}=r.mergedConfig,h=l(),x=u();f(r.graph,h),r.graph.animationCurve=p,r.graph.animationFrame=d,r.graph.animation("style",x,!0),r.graph.animation("shape",h)}function f(p,d){const h=p.shape.number.length,x=d.number.length;h!==x&&(p.shape.number=d.number)}return(p,d)=>(fe(),de("div",CS,[ae("canvas",{ref_key:"digitalFlop",ref:n},null,512)]))}},xS={class:"dv-active-ring-chart"},ES={class:"active-ring-info"},SS={key:0},wS={__name:"index",props:{config:{type:Object,default:()=>({})},isDigitalFlop:{type:Boolean,default:!0}},setup(e){Or(L=>({"18d51787":v(u)}));const t=e,n=ye(null),r=ke({defaultConfig:{radius:"50%",activeRadius:"55%",data:[{name:"",value:0}],lineWidth:20,activeTimeGap:3e3,color:[],textColor:"#fff",digitalFlopStyle:{fontSize:25,fill:"#fff"},digitalFlopToFixed:0,numToFixed:0,digitalFlopUnit:"",animationCurve:"easeOutCubic",animationFrame:50,showOriginValue:!1},mergedConfig:null,chart:null,activeIndex:0,animationHandler:""}),i=Ce(()=>{if(!r.mergedConfig)return 0;const{data:L,showOriginValue:A}=r.mergedConfig,g=L.map(({value:b})=>b);let C;if(A)C=g[r.activeIndex];else{const b=g.reduce((R,T)=>R+T,0);C=Number.parseFloat(g[r.activeIndex]/b*100)||0}return C}),o=Ce(()=>{if(!r.mergedConfig)return i.value.toFixed(r.defaultConfig.numToFixed);const{numToFixed:L,showOriginValue:A}=r.mergedConfig;return`${A?i.value.toFixed(L):`${i.value.toFixed(L)}%`}`}),s=Ce(()=>{if(!r.mergedConfig)return{};const{digitalFlopStyle:L,digitalFlopToFixed:A,showOriginValue:g,digitalFlopUnit:C}=r.mergedConfig;return{content:g?`{nt}${C}`:`{nt}${C||"%"}`,number:[i.value],style:L,toFixed:A}}),a=Ce(()=>r.mergedConfig?r.mergedConfig.data[r.activeIndex].name:""),l=Ce(()=>r.mergedConfig?`font-size: ${r.mergedConfig.digitalFlopStyle.fontSize}px;`:""),u=Ce(()=>t.config.textColor?t.config.textColor:r.defaultConfig.textColor);we(()=>t.config,()=>{clearTimeout(r.animationHandler),r.activeIndex=0,p(),d()},{deep:!0}),Ue(()=>{c()}),Mn(()=>{clearTimeout(r.animationHandler)});function c(){f(),p(),d()}function f(){r.chart=new P0(n.value)}function p(){r.mergedConfig=je(Be(r.defaultConfig),t.config||{})}function d(){const L=h();r.chart.setOption(L,!0),j()}function h(){const L=x();return r.mergedConfig.data.forEach(A=>{A.radius=L}),{series:[{type:"pie",...r.mergedConfig,outsideLabel:{show:!1}}],color:r.mergedConfig.color}}function x(L=!1){const{radius:A,activeRadius:g,lineWidth:C}=r.mergedConfig,b=Math.min(...r.chart.render.area)/2,R=C/2;let T=L?g:A;typeof T!="number"&&(T=Number.parseInt(T)/100*b);const O=T-R,y=T+R;return[O,y]}function j(){const L=x(),A=x(!0),g=h(),{data:C}=g.series[0];C.forEach((R,T)=>{T===r.activeIndex?R.radius=A:R.radius=L}),r.chart.setOption(g,!0);const{activeTimeGap:b}=g.series[0];r.animationHandler=setTimeout(()=>{r.activeIndex+=1,r.activeIndex>=C.length&&(r.activeIndex=0),j()},b)}return(L,A)=>(fe(),de("div",xS,[ae("div",{ref_key:"activeRingChart",ref:n,class:"active-ring-chart-container"},null,512),ae("div",ES,[e.isDigitalFlop?(fe(),de("div",SS,[ee(A0,{config:v(s)},null,8,["config"])])):(fe(),de("div",{key:1,class:"active-ring-name",style:Ve(v(l))},ct(v(o)),5)),ae("div",{class:"active-ring-name",style:Ve(v(l))},ct(v(a)),5)])]))}},lp={install(e){e.component("DvActiveRingChart",wS)}},up={install(e){e.component("DvDigitalFlop",A0)}},TS=Ne({__name:"index",setup(e){const t=ye(null),n=ke({allWidth:0,scale:0,datavRoot:"",ready:!1});function r(){const{width:a,height:l}=screen;n.allWidth=a,t.value&&(t.value.style.width=`${a}px`,t.value.style.height=`${l}px`)}function i(){const a=document.body.clientWidth;t.value&&(t.value.style.transform=`scale(${a/n.allWidth})`)}function o(){i()}function s(){r(),i(),n.ready=!0}return De(t,o,s),(a,l)=>(fe(),de("div",{id:"dv-full-screen-container",ref_key:"fullScreenContainer",ref:t},[v(n).ready?ze(a.$slots,"default",{key:0}):Re("",!0)],512))}}),cp={install(e){e.component("DvFullScreenContainer",TS)}},OS=["width","height"],PS=["fill","x","y","width","height"],AS=["values","begin"],IS=["fill","x","y","width","height"],kS=["values"],$S=["values"],NS=["values"],MS=["values"],RS=["fill","x","y","height"],LS=["values"],DS=Ne({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,n=ye(null),r=ke([200,50]),i=ye(4),o=ye(20),s=ye(2.5),a=ye(s.value/2),l=ke(["#fff","#0de7c2"]),u=ke({mergedColor:[],rects:[],points:[],svgScale:[1,1]});function c(){L()}function f(){L()}const{width:p,height:d}=De(n,c,f);function h(){const[g,C]=r,b=g/(o.value+1),R=C/(i.value+1),T=Array.from({length:i.value}).fill(0).map((O,y)=>Array.from({length:o.value}).fill(0).map((S,H)=>[b*(H+1),R*(y+1)]));u.points=T.reduce((O,y)=>[...O,...y],[])}function x(){const g=u.points[o.value*2-1],C=u.points[o.value*2-3];u.rects=[g,C]}function j(){const[g,C]=r;u.svgScale=[p.value/g,d.value/C]}function L(){h(),x(),j()}function A(){u.mergedColor=je(Be(l),t.color||[])}return we(()=>t.color,()=>{A()},{deep:!0}),Ue(()=>{A()}),(g,C)=>(fe(),de("div",{ref_key:"dvDecoration1",ref:n,class:"dv-decoration-1"},[(fe(),de("svg",{width:`${v(r)[0]}px`,height:`${v(r)[1]}px`,style:Ve(`transform:scale(${v(u).svgScale[0]}, ${v(u).svgScale[1]});`)},[(fe(!0),de(Se,null,qe(v(u).points,b=>(fe(),de(Se,{key:b},[Math.random()>.6?(fe(),de("rect",{key:0,fill:v(u).mergedColor[0],x:b[0]-v(a),y:b[1]-v(a),width:v(s),height:v(s)},[Math.random()>.6?(fe(),de("animate",{key:0,attributeName:"fill",values:`${v(u).mergedColor[0]};transparent`,dur:"1s",begin:Math.random()*2,repeatCount:"indefinite"},null,8,AS)):Re("",!0)],8,PS)):Re("",!0)],64))),128)),v(u).rects[0]?(fe(),de("rect",{key:0,fill:v(u).mergedColor[1],x:v(u).rects[0][0]-v(s),y:v(u).rects[0][1]-v(s),width:v(s)*2,height:v(s)*2},[ae("animate",{attributeName:"width",values:`0;${v(s)*2}`,dur:"2s",repeatCount:"indefinite"},null,8,kS),ae("animate",{attributeName:"height",values:`0;${v(s)*2}`,dur:"2s",repeatCount:"indefinite"},null,8,$S),ae("animate",{attributeName:"x",values:`${v(u).rects[0][0]};${v(u).rects[0][0]-v(s)}`,dur:"2s",repeatCount:"indefinite"},null,8,NS),ae("animate",{attributeName:"y",values:`${v(u).rects[0][1]};${v(u).rects[0][1]-v(s)}`,dur:"2s",repeatCount:"indefinite"},null,8,MS)],8,IS)):Re("",!0),v(u).rects[1]?(fe(),de("rect",{key:1,fill:v(u).mergedColor[1],x:v(u).rects[1][0]-40,y:v(u).rects[1][1]-v(s),width:40,height:v(s)*2},[C[0]||(C[0]=ae("animate",{attributeName:"width",values:"0;40;0",dur:"2s",repeatCount:"indefinite"},null,-1)),ae("animate",{attributeName:"x",values:`${v(u).rects[1][0]};${v(u).rects[1][0]-40};${v(u).rects[1][0]}`,dur:"2s",repeatCount:"indefinite"},null,8,LS)],8,RS)):Re("",!0)],12,OS))],512))}}),fp={install(e){e.component("DvDecoration1",DS)}},FS=["width","height"],jS=["x","y","width","height","fill"],BS=["attributeName","to","dur"],VS=["x","y","fill"],HS=["attributeName","to","dur"],GS=Ne({__name:"index",props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1},dur:{type:Number,default:6}},setup(e){const t=e,n=ye(null),r=ke({x:0,y:0,w:0,h:0,defaultColor:["#3faacb","#fff"],mergedColor:[]});function i(){r.mergedColor=je(Be(r.defaultColor),t.color||[])}function o(){u()}function s(){u()}const{width:a,height:l}=De(n,o,s);function u(){t.reverse?(r.w=1,r.h=l.value,r.x=a.value/2,r.y=0):(r.w=a.value,r.h=1,r.x=0,r.y=l.value/2)}return we(()=>t.color,()=>{i()},{deep:!0}),we(()=>t.reverse,()=>{u()}),Ue(()=>{i()}),(c,f)=>(fe(),de("div",{ref_key:"decoration2",ref:n,class:"dv-decoration-2"},[(fe(),de("svg",{width:`${v(a)}px`,height:`${v(l)}px`},[ae("rect",{x:v(r).x,y:v(r).y,width:v(r).w,height:v(r).h,fill:v(r).mergedColor[0]},[ae("animate",{attributeName:e.reverse?"height":"width",from:"0",to:e.reverse?v(l):v(a),dur:`${e.dur}s`,calcMode:"spline",keyTimes:"0;1",keySplines:".42,0,.58,1",repeatCount:"indefinite"},null,8,BS)],8,jS),ae("rect",{x:v(r).x,y:v(r).y,width:"1",height:"1",fill:v(r).mergedColor[1]},[ae("animate",{attributeName:e.reverse?"y":"x",from:"0",to:e.reverse?v(l):v(a),dur:`${e.dur}s`,calcMode:"spline",keyTimes:"0;1",keySplines:"0.42,0,0.58,1",repeatCount:"indefinite"},null,8,HS)],8,VS)],8,FS))],512))}}),dp={install(e){e.component("DvDecoration2",GS)}},US=["width","height"],WS=["fill","x","y"],XS=["values","dur","begin"],Ko=7,KS=Ne({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,n=ye(null),r=ke({svgWH:[300,35],svgScale:[1,1],rowNum:2,rowPoints:25,pointSideLength:Ko,halfPointSideLength:Ko/2,points:[],defaultColor:["#7acaec","transparent"],mergedColor:[]});function i(){const[p,d]=r.svgWH,h=p/(r.rowPoints+1),x=d/(r.rowNum+1),j=Array.from({length:r.rowNum}).fill(0).map((L,A)=>Array.from({length:r.rowPoints}).fill(0).map((g,C)=>[h*(C+1),x*(A+1)]));r.points=j.reduce((L,A)=>[...L,...A],[])}function o(){s()}function s(){i(),c()}function a(){s()}const{width:l,height:u}=De(n,a,o);function c(){const[p,d]=r.svgWH;r.svgScale=[l.value/p,u.value/d]}function f(){r.mergedColor=je(Be(r.defaultColor),t.color||[])}return we(()=>t.color,()=>{f()},{deep:!0}),Ue(()=>{f()}),(p,d)=>(fe(),de("div",{ref_key:"decoration3",ref:n,class:"dv-decoration-3"},[(fe(),de("svg",{width:`${v(r).svgWH[0]}px`,height:`${v(r).svgWH[1]}px`,style:Ve(`transform:scale(${v(r).svgScale[0]},${v(r).svgScale[1]});`)},[(fe(!0),de(Se,null,qe(v(r).points,h=>(fe(),de("rect",{key:h,fill:v(r).mergedColor[0],x:h[0]-v(r).halfPointSideLength,y:h[1]-v(r).halfPointSideLength,width:Ko,height:Ko},[Math.random()>.6?(fe(),de("animate",{key:0,attributeName:"fill",values:`${v(r).mergedColor.join(";")}`,dur:`${Math.random()+1}s`,begin:Math.random()*2,repeatCount:"indefinite"},null,8,XS)):Re("",!0)],8,WS))),128))],12,US))],512))}}),pp={install(e){e.component("DvDecoration3",KS)}},qS=["width","height"],zS=["stroke","points"],JS=["stroke","points"],QS=Ne({__name:"index",props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1},dur:{type:Number,default:3}},setup(e){const t=e,n=ye(null),r=ke({defaultColor:["rgba(255, 255, 255, 0.3)","rgba(255, 255, 255, 0.3)"],mergedColor:[]});function i(){r.mergedColor=je(Be(r.defaultColor),t.color||[])}const{width:o,height:s}=De(n);return we(()=>t.color,()=>{i()},{deep:!0}),Ue(()=>{i()}),(a,l)=>(fe(),de("div",{ref_key:"decoration3",ref:n,class:"dv-decoration-4"},[ae("div",{class:tr(`container ${e.reverse?"reverse":"normal"}`),style:Ve(e.reverse?`width:${v(o)}px;height:5px;animation-duration:${e.dur}s`:`width:5px;height:${v(s)}px;animation-duration:${e.dur}s`)},[(fe(),de("svg",{width:e.reverse?v(o):5,height:e.reverse?5:v(s)},[ae("polyline",{stroke:v(r).mergedColor[0],points:e.reverse?`0, 2.5 ${v(o)}, 2.5`:`2.5, 0 2.5, ${v(s)}`},null,8,zS),ae("polyline",{class:"bold-line",stroke:v(r).mergedColor[1],"stroke-width":"3","stroke-dasharray":"20, 80","stroke-dashoffset":"-30",points:e.reverse?`0, 2.5 ${v(o)}, 2.5`:`2.5, 0 2.5, ${v(s)}`},null,8,JS)],8,qS))],6)],512))}}),hp={install(e){e.component("DvDecoration4",QS)}},YS=["width","height"],ZS=["stroke","points"],ew=["from","to","dur"],tw=["stroke","points"],nw=["from","to","dur"],rw=Ne({__name:"index",props:{color:{type:Array,default:()=>[]},dur:{type:Number,default:1.2}},setup(e){const t=e,n=ye(null),r=ke({line1Points:"",line2Points:"",line1Length:0,line2Length:0,defaultColor:["#3f96a5","#3f96a5"],mergedColor:[]});function i(){l()}function o(){l()}const{width:s,height:a}=De(n,o,i);function l(){const c=[{x:0,y:a.value*.2},{x:s.value*.18,y:a.value*.2},{x:s.value*.2,y:a.value*.4},{x:s.value*.25,y:a.value*.4},{x:s.value*.27,y:a.value*.6},{x:s.value*.72,y:a.value*.6},{x:s.value*.75,y:a.value*.4},{x:s.value*.8,y:a.value*.4},{x:s.value*.82,y:a.value*.2},{x:s.value,y:a.value*.2}],f=[{x:s.value*.3,y:a.value*.8},{x:s.value*.7,y:a.value*.8}],p=hf(c),d=hf(f);r.line1Points=gf(c),r.line2Points=gf(f),r.line1Length=p,r.line2Length=d}function u(){r.mergedColor=je(Be(r.defaultColor),t.color||[])}return we(()=>t.color,()=>{u()},{deep:!0}),Ue(()=>{u()}),(c,f)=>(fe(),de("div",{ref_key:"decoration5",ref:n,class:"dv-decoration-5"},[(fe(),de("svg",{width:v(s),height:v(a)},[ae("polyline",{fill:"transparent",stroke:v(r).mergedColor[0],"stroke-width":"3",points:v(r).line1Points},[ae("animate",{attributeName:"stroke-dasharray",attributeType:"XML",from:`0, ${v(r).line1Length/2}, 0, ${v(r).line1Length/2}`,to:`0, 0, ${v(r).line1Length}, 0`,dur:`${e.dur}s`,begin:"0s",calcMode:"spline",keyTimes:"0;1",keySplines:"0.4,1,0.49,0.98",repeatCount:"indefinite"},null,8,ew)],8,ZS),ae("polyline",{fill:"transparent",stroke:v(r).mergedColor[1],"stroke-width":"2",points:v(r).line2Points},[ae("animate",{attributeName:"stroke-dasharray",attributeType:"XML",from:`0, ${v(r).line2Length/2}, 0, ${v(r).line2Length/2}`,to:`0, 0, ${v(r).line2Length}, 0`,dur:`${e.dur}s`,begin:"0s",calcMode:"spline",keyTimes:"0;1",keySplines:".4,1,.49,.98",repeatCount:"indefinite"},null,8,nw)],8,tw)],8,YS))],512))}}),gp={install(e){e.component("DvDecoration5",rw)}},iw=["width","height"],ow=["fill","x","y","height"],sw=["values","dur"],aw=["values","dur"],Ka=7,lw=Ne({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,n=ye(null),r=ke({svgWH:[300,35],svgScale:[1,1],rowNum:1,rowPoints:40,rectWidth:Ka,halfRectWidth:Ka/2,points:[],heights:[],minHeights:[],randoms:[],defaultColor:["#7acaec","#7acaec"],mergedColor:[]});we(()=>t.color,()=>{f()},{deep:!0}),Ue(()=>{f()});const{width:i,height:o}=De(n,c,s);function s(){a()}function a(){l(),u()}function l(){const[p,d]=r.svgWH,h=p/(r.rowPoints+1),x=d/(r.rowNum+1),j=Array.from({length:r.rowNum}).fill(0).map((A,g)=>Array.from({length:r.rowPoints}).fill(0).map((C,b)=>[h*(b+1),x*(g+1)]));r.points=j.reduce((A,g)=>[...A,...g],[]);const L=r.heights=Array.from({length:r.rowNum*r.rowPoints}).fill(0).map(()=>Math.random()>.8?qi(.7*d,d):qi(.2*d,.5*d));r.minHeights=Array.from({length:r.rowNum*r.rowPoints}).fill(0).map((A,g)=>L[g]*Math.random()),r.randoms=Array.from({length:r.rowNum*r.rowPoints}).fill(0).map(()=>Math.random()+1.5)}function u(){const[p,d]=r.svgWH;r.svgScale=[i.value/p,o.value/d]}function c(){a()}function f(){r.mergedColor=je(Be(r.defaultColor),t.color||[])}return(p,d)=>(fe(),de("div",{ref_key:"decoration6",ref:n,class:"dv-decoration-6"},[(fe(),de("svg",{width:`${v(r).svgWH[0]}px`,height:`${v(r).svgWH[1]}px`,style:Ve(`transform:scale(${v(r).svgScale[0]},${v(r).svgScale[1]});`)},[(fe(!0),de(Se,null,qe(v(r).points,(h,x)=>(fe(),de("rect",{key:x,fill:v(r).mergedColor[Math.random()>.5?0:1],x:h[0]-v(r).halfRectWidth,y:h[1]-v(r).heights[x]/2,width:Ka,height:v(r).heights[x]},[ae("animate",{attributeName:"y",values:`${h[1]-v(r).minHeights[x]/2};${h[1]-v(r).heights[x]/2};${h[1]-v(r).minHeights[x]/2}`,dur:`${v(r).randoms[x]}s`,keyTimes:"0;0.5;1",calcMode:"spline",keySplines:"0.42,0,0.58,1;0.42,0,0.58,1",begin:"0s",repeatCount:"indefinite"},null,8,sw),ae("animate",{attributeName:"height",values:`${v(r).minHeights[x]};${v(r).heights[x]};${v(r).minHeights[x]}`,dur:`${v(r).randoms[x]}s`,keyTimes:"0;0.5;1",calcMode:"spline",keySplines:"0.42,0,0.58,1;0.42,0,0.58,1",begin:"0s",repeatCount:"indefinite"},null,8,aw)],8,ow))),128))],12,iw))],512))}}),mp={install(e){e.component("DvDecoration6",lw)}},uw={class:"dv-decoration-7"},cw={width:"21px",height:"20px"},fw=["stroke"],dw=["stroke"],pw={width:"21px",height:"20px"},hw=["stroke"],gw=["stroke"],mw=Ne({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,n=ke({defaultColor:["#1dc1f5","#1dc1f5"],mergedColor:[]});we(()=>t.color,()=>{r()},{deep:!0}),Ue(()=>{r()});function r(){n.mergedColor=je(Be(n.defaultColor),t.color||[])}return(i,o)=>(fe(),de("div",uw,[(fe(),de("svg",cw,[ae("polyline",{"stroke-width":"4",fill:"transparent",stroke:v(n).mergedColor[0],points:"10, 0 19, 10 10, 20"},null,8,fw),ae("polyline",{"stroke-width":"2",fill:"transparent",stroke:v(n).mergedColor[1],points:"2, 0 11, 10 2, 20"},null,8,dw)])),ze(i.$slots,"default"),(fe(),de("svg",pw,[ae("polyline",{"stroke-width":"4",fill:"transparent",stroke:v(n).mergedColor[0],points:"11, 0 2, 10 11, 20"},null,8,hw),ae("polyline",{"stroke-width":"2",fill:"transparent",stroke:v(n).mergedColor[1],points:"19, 0 10, 10 19, 20"},null,8,gw)]))]))}}),vp={install(e){e.component("DvDecoration7",mw)}},vw=["width","height"],yw=["stroke","points"],bw=["stroke","points"],_w=["stroke","points"],Cw=Ne({__name:"index",props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1}},setup(e){const t=e,n=ye(null),r=ke({defaultColor:["#3f96a5","#3f96a5"],mergedColor:[]});we(()=>t.color,()=>{a()},{deep:!0}),Ue(()=>{a()});const{width:i,height:o}=De(n);function s(l){return t.reverse?i.value-l:l}function a(){r.mergedColor=je(Be(r.defaultColor),t.color||[])}return(l,u)=>(fe(),de("div",{ref_key:"decoration8",ref:n,class:"dv-decoration-8"},[(fe(),de("svg",{width:v(i),height:v(o)},[ae("polyline",{stroke:v(r).mergedColor[0],"stroke-width":"2",fill:"transparent",points:`${s(0)}, 0 ${s(30)}, ${v(o)/2}`},null,8,yw),ae("polyline",{stroke:v(r).mergedColor[0],"stroke-width":"2",fill:"transparent",points:`${s(20)}, 0 ${s(50)}, ${v(o)/2} ${s(v(i))}, ${v(o)/2}`},null,8,bw),ae("polyline",{stroke:v(r).mergedColor[1],fill:"transparent","stroke-width":"3",points:`${s(0)}, ${v(o)-3}, ${s(200)}, ${v(o)-3}`},null,8,_w)],8,vw))],512))}}),yp={install(e){e.component("DvDecoration8",Cw)}},xw=["width","height"],Ew=["id"],Sw=["stroke"],ww=["dur"],Tw=["stroke"],Ow=["dur"],Pw=["stroke"],Aw=["xlink:href","stroke","fill"],Iw=["dur","begin"],kw=["stroke"],$w={__name:"index",props:{color:{type:Array,default:()=>[]},dur:{type:Number,default:3}},setup(e){const t=e,n=sn(),r=ye(null),i=ke({polygonId:`decoration-9-polygon-${n}`,svgWH:[100,100],svgScale:[1,1],defaultColor:["rgba(3, 166, 224, 0.8)","rgba(3, 166, 224, 0.5)"],mergedColor:[]});we(()=>t.color,()=>{c()},{deep:!0}),Ue(()=>{c()});const{width:o,height:s}=De(r,u,a);function a(){l()}function l(){const[f,p]=i.svgWH;i.svgScale=[o.value/f,s.value/p]}function u(){l()}function c(){i.mergedColor=je(Be(i.defaultColor),t.color||[])}return(f,p)=>(fe(),de("div",{ref_key:"decoration9",ref:r,class:"dv-decoration-9"},[(fe(),de("svg",{width:`${v(i).svgWH[0]}px`,height:`${v(i).svgWH[1]}px`,style:Ve(`transform:scale(${v(i).svgScale[0]},${v(i).svgScale[1]});`)},[ae("defs",null,[ae("polygon",{id:v(i).polygonId,points:"15, 46.5, 21, 47.5, 21, 52.5, 15, 53.5"},null,8,Ew)]),ae("circle",{cx:"50",cy:"50",r:"45",fill:"transparent",stroke:v(i).mergedColor[1],"stroke-width":"10","stroke-dasharray":"80, 100, 30, 100"},[ae("animateTransform",{attributeName:"transform",type:"rotate",values:"0 50 50;360 50 50",dur:`${e.dur}s`,repeatCount:"indefinite"},null,8,ww)],8,Sw),ae("circle",{cx:"50",cy:"50",r:"45",fill:"transparent",stroke:v(i).mergedColor[0],"stroke-width":"6","stroke-dasharray":"50, 66, 100, 66"},[ae("animateTransform",{attributeName:"transform",type:"rotate",values:"0 50 50;-360 50 50",dur:`${e.dur}s`,repeatCount:"indefinite"},null,8,Ow)],8,Tw),ae("circle",{cx:"50",cy:"50",r:"38",fill:"transparent",stroke:v(ut)(v(i).mergedColor[1]||v(i).defaultColor[1],30),"stroke-width":"1","stroke-dasharray":"5, 1"},null,8,Pw),(fe(!0),de(Se,null,qe(new Array(20).fill(0),(d,h)=>(fe(),de("use",{key:h,"xlink:href":`#${v(i).polygonId}`,stroke:v(i).mergedColor[1],fill:Math.random()>.4?"transparent":v(i).mergedColor[0]},[ae("animateTransform",{attributeName:"transform",type:"rotate",values:"0 50 50;360 50 50",dur:`${e.dur}s`,begin:`${h*e.dur/20}s`,repeatCount:"indefinite"},null,8,Iw)],8,Aw))),128)),ae("circle",{cx:"50",cy:"50",r:"26",fill:"transparent",stroke:v(ut)(v(i).mergedColor[1]||v(i).defaultColor[1],30),"stroke-width":"1","stroke-dasharray":"5, 1"},null,8,kw)],12,xw)),ze(f.$slots,"default")],512))}},bp={install(e){e.component("DvDecoration9",$w)}},Nw=["width","height"],Mw=["stroke","points"],Rw=["stroke","points","stroke-dasharray"],Lw=["id","values","begin"],Dw=["values","begin"],Fw=["stroke","points","stroke-dasharray"],jw=["id","values","begin"],Bw=["values","begin"],Vw=["stroke","points","stroke-dasharray"],Hw=["id","values","begin"],Gw=["values","begin"],Uw=["cy","fill"],Ww=["id","values","begin"],Xw=["cx","cy","fill"],Kw=["id","values","begin"],qw=["values","begin"],zw=["cx","cy","fill"],Jw=["id","values","begin"],Qw=["values","begin"],Yw=["cx","cy","fill"],Zw=["id","values","begin"],e2=["values","begin"],t2=Ne({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,n=sn(),r=ye(null),i=ke({animationId1:`d10ani1${n}`,animationId2:`d10ani2${n}`,animationId3:`d10ani3${n}`,animationId4:`d10ani4${n}`,animationId5:`d10ani5${n}`,animationId6:`d10ani6${n}`,animationId7:`d10ani7${n}`,defaultColor:["#00c2ff","rgba(0, 194, 255, 0.3)"],mergedColor:[]}),{width:o,height:s}=De(r);we(()=>t.color,()=>{a()},{deep:!0}),Ue(()=>{a()});function a(){i.mergedColor=je(Be(i.defaultColor),t.color||[])}return(l,u)=>(fe(),de("div",{ref_key:"decoration10",ref:r,class:"dv-decoration-10"},[(fe(),de("svg",{width:v(o),height:v(s)},[ae("polyline",{stroke:v(i).mergedColor[1],"stroke-width":"2",points:`0, ${v(s)/2} ${v(o)}, ${v(s)/2}`},null,8,Mw),ae("polyline",{stroke:v(i).mergedColor[0],"stroke-width":"2",points:`5, ${v(s)/2} ${v(o)*.2-3}, ${v(s)/2}`,"stroke-dasharray":`0, ${v(o)*.2}`,fill:"freeze"},[ae("animate",{id:v(i).animationId2,attributeName:"stroke-dasharray",values:`0, ${v(o)*.2};${v(o)*.2}, 0;`,dur:"3s",begin:`${v(i).animationId1}.end`,fill:"freeze"},null,8,Lw),ae("animate",{attributeName:"stroke-dasharray",values:`${v(o)*.2}, 0;0, ${v(o)*.2}`,dur:"0.01s",begin:`${v(i).animationId7}.end`,fill:"freeze"},null,8,Dw)],8,Rw),ae("polyline",{stroke:v(i).mergedColor[0],"stroke-width":"2",points:`${v(o)*.2+3}, ${v(s)/2} ${v(o)*.8-3}, ${v(s)/2}`,"stroke-dasharray":`0, ${v(o)*.6}`},[ae("animate",{id:v(i).animationId4,attributeName:"stroke-dasharray",values:`0, ${v(o)*.6};${v(o)*.6}, 0`,dur:"3s",begin:`${v(i).animationId3}.end + 1s`,fill:"freeze"},null,8,jw),ae("animate",{attributeName:"stroke-dasharray",values:`${v(o)*.6}, 0;0, ${v(o)*.6}`,dur:"0.01s",begin:`${v(i).animationId7}.end`,fill:"freeze"},null,8,Bw)],8,Fw),ae("polyline",{stroke:v(i).mergedColor[0],"stroke-width":"2",points:`${v(o)*.8+3}, ${v(s)/2} ${v(o)-5}, ${v(s)/2}`,"stroke-dasharray":`0, ${v(o)*.2}`},[ae("animate",{id:v(i).animationId6,attributeName:"stroke-dasharray",values:`0, ${v(o)*.2};${v(o)*.2}, 0`,dur:"3s",begin:`${v(i).animationId5}.end + 1s`,fill:"freeze"},null,8,Hw),ae("animate",{attributeName:"stroke-dasharray",values:`${v(o)*.2}, 0;0, ${v(o)*.3}`,dur:"0.01s",begin:`${v(i).animationId7}.end`,fill:"freeze"},null,8,Gw)],8,Vw),ae("circle",{cx:"2",cy:v(s)/2,r:"2",fill:v(i).mergedColor[1]},[ae("animate",{id:v(i).animationId1,attributeName:"fill",values:`${v(i).mergedColor[1]};${v(i).mergedColor[0]}`,begin:`0s;${v(i).animationId7}.end`,dur:"0.3s",fill:"freeze"},null,8,Ww)],8,Uw),ae("circle",{cx:v(o)*.2,cy:v(s)/2,r:"2",fill:v(i).mergedColor[1]},[ae("animate",{id:v(i).animationId3,attributeName:"fill",values:`${v(i).mergedColor[1]};${v(i).mergedColor[0]}`,begin:`${v(i).animationId2}.end`,dur:"0.3s",fill:"freeze"},null,8,Kw),ae("animate",{attributeName:"fill",values:`${v(i).mergedColor[1]};${v(i).mergedColor[1]}`,dur:"0.01s",begin:`${v(i).animationId7}.end`,fill:"freeze"},null,8,qw)],8,Xw),ae("circle",{cx:v(o)*.8,cy:v(s)/2,r:"2",fill:v(i).mergedColor[1]},[ae("animate",{id:v(i).animationId5,attributeName:"fill",values:`${v(i).mergedColor[1]};${v(i).mergedColor[0]}`,begin:`${v(i).animationId4}.end`,dur:"0.3s",fill:"freeze"},null,8,Jw),ae("animate",{attributeName:"fill",values:`${v(i).mergedColor[1]};${v(i).mergedColor[1]}`,dur:"0.01s",begin:`${v(i).animationId7}.end`,fill:"freeze"},null,8,Qw)],8,zw),ae("circle",{cx:v(o)-2,cy:v(s)/2,r:"2",fill:v(i).mergedColor[1]},[ae("animate",{id:v(i).animationId7,attributeName:"fill",values:`${v(i).mergedColor[1]};${v(i).mergedColor[0]}`,begin:`${v(i).animationId6}.end`,dur:"0.3s",fill:"freeze"},null,8,Zw),ae("animate",{attributeName:"fill",values:`${v(i).mergedColor[1]};${v(i).mergedColor[1]}`,dur:"0.01s",begin:`${v(i).animationId7}.end`,fill:"freeze"},null,8,e2)],8,Yw)],8,Nw))],512))}}),_p={install(e){e.component("DvDecoration10",t2)}},n2=["width","height"],r2=["fill","stroke"],i2=["fill","stroke","points"],o2=["fill","stroke","points"],s2=["fill","stroke","points"],a2=["fill","stroke","points"],l2=["stroke","points"],u2=["stroke","points"],c2={class:"decoration-content"},f2={__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,n=ye(null),r=ke({defaultColor:["#1a98fc","#2cf7fe"],mergedColor:[]}),{width:i,height:o}=De(n);we(()=>t.color,()=>{s()},{deep:!0}),Ue(()=>{s()});function s(){r.mergedColor=je(Be(r.defaultColor),t.color||[])}return(a,l)=>(fe(),de("div",{ref_key:"decoration11",ref:n,class:"dv-decoration-11"},[(fe(),de("svg",{width:v(i),height:v(o)},[ae("polygon",{fill:v(ut)(v(r).mergedColor[1]||v(r).defaultColor[1],10),stroke:v(r).mergedColor[1],points:"20 10, 25 4, 55 4 60 10"},null,8,r2),ae("polygon",{fill:v(ut)(v(r).mergedColor[1]||v(r).defaultColor[1],10),stroke:v(r).mergedColor[1],points:`20 ${v(o)-10}, 25 ${v(o)-4}, 55 ${v(o)-4} 60 ${v(o)-10}`},null,8,i2),ae("polygon",{fill:v(ut)(v(r).mergedColor[1]||v(r).defaultColor[1],10),stroke:v(r).mergedColor[1],points:`${v(i)-20} 10, ${v(i)-25} 4, ${v(i)-55} 4 ${v(i)-60} 10`},null,8,o2),ae("polygon",{fill:v(ut)(v(r).mergedColor[1]||v(r).defaultColor[1],10),stroke:v(r).mergedColor[1],points:`${v(i)-20} ${v(o)-10}, ${v(i)-25} ${v(o)-4}, ${v(i)-55} ${v(o)-4} ${v(i)-60} ${v(o)-10}`},null,8,s2),ae("polygon",{fill:v(ut)(v(r).mergedColor[0]||v(r).defaultColor[0],20),stroke:v(r).mergedColor[0],points:`
          20 10, 5 ${v(o)/2} 20 ${v(o)-10}
          ${v(i)-20} ${v(o)-10} ${v(i)-5} ${v(o)/2} ${v(i)-20} 10
        `},null,8,a2),ae("polyline",{fill:"transparent",stroke:v(ut)(v(r).mergedColor[0]||v(r).defaultColor[0],70),points:`25 18, 15 ${v(o)/2} 25 ${v(o)-18}`},null,8,l2),ae("polyline",{fill:"transparent",stroke:v(ut)(v(r).mergedColor[0]||v(r).defaultColor[0],70),points:`${v(i)-25} 18, ${v(i)-15} ${v(o)/2} ${v(i)-25} ${v(o)-18}`},null,8,u2)],8,n2)),ae("div",c2,[ze(a.$slots,"default")])],512))}},Cp={install(e){e.component("DvDecoration11",f2)}},d2=["width","height"],p2=["id"],h2=["stroke","stroke-width","d"],g2=["id"],m2=["stop-color"],v2=["r","cx","cy","stroke"],y2=["cx","cy","fill"],b2=["values","dur"],_2=["dur"],C2=["cx","cy","fill"],x2={key:0},E2=["points","stroke"],S2=["d","stroke"],w2=["xlink:href"],T2=["values","dur"],O2={class:"decoration-content"},P2={__name:"index",props:{color:{type:Array,default:()=>[]},scanDur:{type:Number,default:3},haloDur:{type:Number,default:2}},setup(e){const t=e,n=sn(),r=ye(null),{width:i,height:o}=De(r,()=>{},j),s=ke({gId:`decoration-12-g-${n}`,gradientId:`decoration-12-gradient-${n}`,defaultColor:["#2783ce","#2cf7fe"],mergedColor:[],pathD:[],pathColor:[],circleR:[],splitLinePoints:[],arcD:[],segment:30,sectorAngle:Math.PI/3,ringNum:3,ringWidth:1,showSplitLine:!0}),a=Ce(()=>i.value/2),l=Ce(()=>o.value/2);we(()=>t.color,()=>{c(),p()},{deep:!0});function u(){c(),f(),p(),d(),h(),x()}function c(){s.mergedColor=je(Be(s.defaultColor),t.color||[])}function f(){const L=-Math.PI/2,A=s.sectorAngle/s.segment,g=i.value/4;let C=Ir(a.value,l.value,g,L);s.pathD=Array.from({length:s.segment}).fill("").map((b,R)=>{const T=Ir(a.value,l.value,g,L-(R+1)*A).map(y=>Number.parseFloat(y.toFixed(5))),O=`M${C.join(",")} A${g}, ${g} 0 0 0 ${T.join(",")}`;return C=T,O})}function p(){const L=100/(s.segment-1);s.pathColor=Array.from({length:s.segment}).fill(s.mergedColor[0]).map((A,g)=>ut(s.mergedColor[0],100-g*L))}function d(){const L=(i.value/2-s.ringWidth/2)/s.ringNum;s.circleR=Array.from({length:s.ringNum}).fill(0).map((A,g)=>L*(g+1))}function h(){const L=Math.PI/6,A=i.value/2;s.splitLinePoints=Array.from({length:6}).fill("").map((g,C)=>{const b=L*(C+1),R=b+Math.PI,T=Ir(a.value,l.value,A,b),O=Ir(a.value,l.value,A,R);return`${T.join(",")} ${O.join(",")}`})}function x(){const L=Math.PI/6,A=i.value/2-1;s.arcD=Array.from({length:4}).fill("").map((g,C)=>{const b=L*(3*C+1),R=b+L,T=Ir(a.value,l.value,A,b),O=Ir(a.value,l.value,A,R);return`M${T.join(",")} A${a.value}, ${l.value} 0 0 1 ${O.join(",")}`})}function j(){u()}return(L,A)=>(fe(),de("div",{ref_key:"decoration12",ref:r,class:"dv-decoration-12"},[(fe(),de("svg",{width:v(i),height:v(o)},[ae("defs",null,[ae("g",{id:v(s).gId},[(fe(!0),de(Se,null,qe(v(s).pathD,(g,C)=>(fe(),de("path",{key:g,stroke:v(s).pathColor[C],"stroke-width":v(i)/2,fill:"transparent",d:g},null,8,h2))),128))],8,p2),ae("radialGradient",{id:v(s).gradientId,cx:"50%",cy:"50%",r:"50%"},[A[0]||(A[0]=ae("stop",{offset:"0%","stop-color":"transparent","stop-opacity":"1"},null,-1)),ae("stop",{offset:"100%","stop-color":v(ut)(v(s).mergedColor[1]||v(s).defaultColor[1],30),"stop-opacity":"1"},null,8,m2)],8,g2)]),(fe(!0),de(Se,null,qe(v(s).circleR,g=>(fe(),de("circle",{key:g,r:g,cx:v(a),cy:v(l),stroke:v(s).mergedColor[1],"stroke-width":.8,fill:"transparent"},null,8,v2))),128)),ae("circle",{r:"1",cx:v(a),cy:v(l),stroke:"transparent",fill:`url(#${v(s).gradientId})`},[ae("animate",{attributeName:"r",values:`1;${v(i)/2}`,dur:`${e.haloDur}s`,repeatCount:"indefinite"},null,8,b2),ae("animate",{attributeName:"opacity",values:"1;0",dur:`${e.haloDur}s`,repeatCount:"indefinite"},null,8,_2)],8,y2),ae("circle",{r:"2",cx:v(a),cy:v(l),fill:v(s).mergedColor[1]},null,8,C2),v(s).showSplitLine?(fe(),de("g",x2,[(fe(!0),de(Se,null,qe(v(s).splitLinePoints,g=>(fe(),de("polyline",{key:g,points:g,stroke:v(s).mergedColor[1],"stroke-width":.5,opacity:"50"},null,8,E2))),128))])):Re("",!0),(fe(!0),de(Se,null,qe(v(s).arcD,g=>(fe(),de("path",{key:g,d:g,stroke:v(s).mergedColor[1],"stroke-width":"2.3",fill:"transparent"},null,8,S2))),128)),ae("use",{"xlink:href":`#${v(s).gId}`},[ae("animateTransform",{attributeName:"transform",type:"rotate",values:`0, ${v(a)} ${v(l)};360, ${v(a)} ${v(l)}`,dur:`${e.scanDur}s`,repeatCount:"indefinite"},null,8,T2)],8,w2)],8,d2)),ae("div",O2,[ze(L.$slots,"default")])],512))}},xp={install(e){e.component("DvDecoration12",P2)}},Xt={color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}};function Kt(e,t){return Ce(()=>t.value.length===0?e:t.value)}const A2=["left-top","right-top","left-bottom","right-bottom"],I2=["#4fd2dd","#235fa7"],k2=Ne({props:Xt,setup(e){const t=ye(null),n=Kt(I2,$t(e,"color")),{width:r,height:i,initWH:o}=De(t);return{width:r,height:i,initWH:o,mergedColor:n,borderBox1:t}},render(){const{backgroundColor:e,width:t,height:n,mergedColor:r,$slots:i}=this;return ee("div",{ref:"borderBox1",class:"dv-border-box-1"},[ee("svg",{class:"dv-border",width:t,height:n},[ee("polygon",{fill:e,points:`10, 27 10, ${n-27} 13, ${n-24} 13, ${n-21} 24, ${n-11}
      38, ${n-11} 41, ${n-8} 73, ${n-8} 75, ${n-10} 81, ${n-10}
      85, ${n-6} ${t-85}, ${n-6} ${t-81}, ${n-10} ${t-75}, ${n-10}
      ${t-73}, ${n-8} ${t-41}, ${n-8} ${t-38}, ${n-11}
      ${t-10}, ${n-27} ${t-10}, 27 ${t-13}, 25 ${t-13}, 21
      ${t-24}, 11 ${t-38}, 11 ${t-41}, 8 ${t-73}, 8 ${t-75}, 10
      ${t-81}, 10 ${t-85}, 6 85, 6 81, 10 75, 10 73, 8 41, 8 38, 11 24, 11 13, 21 13, 24`},null)]),A2.map(o=>ee("svg",{key:o,width:"150px",height:"150px",class:`${o} dv-border`},[ee("polygon",{fill:r[0],points:"6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"},[ee("animate",{attributeName:"fill",values:`${r[0]};${r[1]};${r[0]}`,dur:"0.5s",begin:"0s",repeatCount:"indefinite"},null)]),ee("polygon",{fill:r[1],points:"27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"},[ee("animate",{attributeName:"fill",values:`${r[1]};${r[0]};${r[1]}`,dur:"0.5s",begin:"0s",repeatCount:"indefinite"},null)]),ee("polygon",{fill:r[0],points:"9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"},[ee("animate",{attributeName:"fill",values:`${r[0]};${r[1]};transparent`,dur:"1s",begin:"0s",repeatCount:"indefinite"},null)])])),ee("div",{class:"border-box-content"},[ze(i,"default")])])}}),Ep={install(e){e.component("DvBorderBox1",k2)}},$2=["#fff","rgba(255, 255, 255, 0.6)"],N2=Ne({props:Xt,setup(e){const t=ye(null),n=Kt($2,$t(e,"color")),{width:r,height:i,initWH:o}=De(t);return{width:r,height:i,initWH:o,mergedColor:n,borderBox2:t}},render(){const{$slots:e,backgroundColor:t,width:n,height:r,mergedColor:i}=this;return ee("div",{ref:"borderBox2",class:"dv-border-box-2"},[ee("svg",{class:"dv-border-svg-container",width:n,height:r},[ee("polygon",{fill:t,points:`
        7, 7 ${n-7}, 7 ${n-7}, ${r-7} 7, ${r-7}
      `},null),ee("polyline",{stroke:i[0],points:`2, 2 ${n-2} ,2 ${n-2}, ${r-2} 2, ${r-2} 2, 2`},null),ee("polyline",{stroke:i[1],points:`6, 6 ${n-6}, 6 ${n-6}, ${r-6} 6, ${r-6} 6, 6`},null),ee("circle",{fill:i[0],cx:"11",cy:"11",r:"1"},null),ee("circle",{fill:i[0],cx:n-11,cy:"11",r:"1"},null),ee("circle",{fill:i[0],cx:n-11,cy:r-11,r:"1"},null),ee("circle",{fill:i[0],cx:"11",cy:r-11,r:"1"},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),Sp={install(e){e.component("DvBorderBox2",N2)}},M2=["#2862b7","#2862b7"],R2=Ne({props:Xt,setup(e){const t=ye(null),{width:n,height:r,initWH:i}=De(t),o=Kt(M2,$t(e,"color"));return{width:n,height:r,mergedColor:o,initWH:i,borderBox3:t}},render(){const{$slots:e,width:t,height:n,backgroundColor:r,mergedColor:i}=this;return ee("div",{ref:"borderBox3",class:"dv-border-box-3"},[ee("svg",{class:"dv-border-svg-container",width:t,height:n},[ee("polygon",{fill:r,points:`
              23, 23 ${t-24}, 23 ${t-24}, ${n-24} 23, ${n-24}
            `},null),ee("polyline",{class:"dv-bb3-line1",stroke:i[0],points:`4, 4 ${t-22} ,4 ${t-22}, ${n-22} 4, ${n-22} 4, 4`},null),ee("polyline",{class:"dv-bb3-line2",stroke:i[1],points:`10, 10 ${t-16}, 10 ${t-16}, ${n-16} 10, ${n-16} 10, 10`},null),ee("polyline",{class:"dv-bb3-line2",stroke:i[1],points:`16, 16 ${t-10}, 16 ${t-10}, ${n-10} 16, ${n-10} 16, 16`},null),ee("polyline",{class:"dv-bb3-line2",stroke:i[1],points:`22, 22 ${t-4}, 22 ${t-4}, ${n-4} 22, ${n-4} 22, 22`},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),wp={install(e){e.component("DvBorderBox3",R2)}},L2={...Xt,reverse:{type:Boolean,default:!1}},D2=["red","rgba(0,0,255,0.8)"],F2=Ne({props:L2,setup(e){const t=ye(null),{width:n,height:r,initWH:i}=De(t),o=Kt(D2,$t(e,"color"));return{width:n,height:r,initWH:i,mergedColor:o,borderBox4:t}},render(){const{$slots:e,backgroundColor:t,reverse:n,width:r,height:i,mergedColor:o}=this;return ee("div",{ref:"borderBox4",class:"dv-border-box-4"},[ee("svg",{class:`dv-border-svg-container ${n&&"dv-reverse"}`,width:r,height:i},[ee("polygon",{fill:t,points:`
        ${r-15}, 22 170, 22 150, 7 40, 7 28, 21 32, 24
        16, 42 16, ${i-32} 41, ${i-7} ${r-15}, ${i-7}
      `},null),ee("polyline",{class:"dv-bb4-line-1",stroke:o[0],points:`145, ${i-5} 40, ${i-5} 10, ${i-35}
          10, 40 40, 5 150, 5 170, 20 ${r-15}, 20`},null),ee("polyline",{stroke:o[1],class:"dv-bb4-line-2",points:`245, ${i-1} 36, ${i-1} 14, ${i-23}
          14, ${i-100}`},null),ee("polyline",{class:"dv-bb4-line-3",stroke:o[0],points:`7, ${i-40} 7, ${i-75}`},null),ee("polyline",{class:"dv-bb4-line-4",stroke:o[0],points:"28, 24 13, 41 13, 64"},null),ee("polyline",{class:"dv-bb4-line-5",stroke:o[0],points:"5, 45 5, 140"},null),ee("polyline",{class:"dv-bb4-line-6",stroke:o[1],points:"14, 75 14, 180"},null),ee("polyline",{class:"dv-bb4-line-7",stroke:o[1],points:"55, 11 147, 11 167, 26 250, 26"},null),ee("polyline",{class:"dv-bb4-line-8",stroke:o[1],points:"158, 5 173, 16"},null),ee("polyline",{class:"dv-bb4-line-9",stroke:o[0],points:`200, 17 ${r-10}, 17`},null),ee("polyline",{class:"dv-bb4-line-10",stroke:o[1],points:`385, 17 ${r-10}, 17`},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),Tp={install(e){e.component("DvBorderBox4",F2)}},j2={...Xt,reverse:{type:Boolean,default:!1}},B2=["rgba(255, 255, 255, 0.35)","rgba(255, 255, 255, 0.20)"],V2=Ne({props:j2,setup(e){const t=ye(null),{width:n,height:r,initWH:i}=De(t),o=Kt(B2,$t(e,"color"));return{width:n,height:r,initWH:i,mergedColor:o,borderBox5:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i,reverse:o}=this;return ee("div",{ref:"borderBox5",class:"dv-border-box-5"},[ee("svg",{class:`dv-border-svg-container  ${o&&"dv-reverse"}`,width:t,height:n},[ee("polygon",{fill:i,points:`
            10, 22 ${t-22}, 22 ${t-22}, ${n-86} ${t-84}, ${n-24} 10, ${n-24}
          `},null),ee("polyline",{class:"dv-bb5-line-1",stroke:r[0],points:`8, 5 ${t-5}, 5 ${t-5}, ${n-100}
          ${t-100}, ${n-5} 8, ${n-5} 8, 5`},null),ee("polyline",{class:"dv-bb5-line-2",stroke:r[1],points:`3, 5 ${t-20}, 5 ${t-20}, ${n-60}
          ${t-74}, ${n-5} 3, ${n-5} 3, 5`},null),ee("polyline",{class:"dv-bb5-line-3",stroke:r[1],points:`50, 13 ${t-35}, 13`},null),ee("polyline",{class:"dv-bb5-line-4",stroke:r[1],points:`15, 20 ${t-35}, 20`},null),ee("polyline",{class:"dv-bb5-line-5",stroke:r[1],points:`15, ${n-20} ${t-110}, ${n-20}`},null),ee("polyline",{class:"dv-bb5-line-6",stroke:r[1],points:`15, ${n-13} ${t-110}, ${n-13}`},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),Op={install(e){e.component("DvBorderBox5",V2)}},H2=["rgba(255, 255, 255, 0.35)","gray"],G2=Ne({props:Xt,setup(e){const t=ye(null),{width:n,height:r,initWH:i}=De(t),o=Kt(H2,$t(e,"color"));return{width:n,height:r,initWH:i,mergedColor:o,borderBox6:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i}=this;return ee("div",{ref:"borderBox6",class:"dv-border-box-6"},[ee("svg",{class:"dv-border-svg-container",width:t,height:n},[ee("polygon",{fill:i,points:`
            9, 7 ${t-9}, 7 ${t-9}, ${n-7} 9, ${n-7}
            `},null),ee("circle",{fill:r[1],cx:"5",cy:"5",r:"2"},null),ee("circle",{fill:r[1],cx:t-5,cy:"5",r:"2"},null),ee("circle",{fill:r[1],cx:t-5,cy:n-5,r:"2"},null),ee("circle",{fill:r[1],cx:"5",cy:n-5,r:"2"},null),ee("polyline",{stroke:r[0],points:`10, 4 ${t-10}, 4`},null),ee("polyline",{stroke:r[0],points:`10, ${n-4} ${t-10}, ${n-4}`},null),ee("polyline",{stroke:r[0],points:`5, 70 5, ${n-70}`},null),ee("polyline",{stroke:r[0],points:`${t-5}, 70 ${t-5}, ${n-70}`},null),ee("polyline",{stroke:r[0],points:"3, 10, 3, 50"},null),ee("polyline",{stroke:r[0],points:"7, 30 7, 80"},null),ee("polyline",{stroke:r[0],points:`${t-3}, 10 ${t-3}, 50`},null),ee("polyline",{stroke:r[0],points:`${t-7}, 30 ${t-7}, 80`},null),ee("polyline",{stroke:r[0],points:`3, ${n-10} 3, ${n-50}`},null),ee("polyline",{stroke:r[0],points:`7, ${n-30} 7, ${n-80}`},null),ee("polyline",{stroke:r[0],points:`${t-3}, ${n-10} ${t-3}, ${n-50}`},null),ee("polyline",{stroke:r[0],points:`${t-7}, ${n-30} ${t-7}, ${n-80}`},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),Pp={install(e){e.component("DvBorderBox6",G2)}},U2=["rgba(128,128,128,0.3)","rgba(128,128,128,0.5)"],W2=Ne({props:Xt,setup(e){const t=ye(null),{width:n,height:r,initWH:i}=De(t),o=Kt(U2,$t(e,"color"));return{width:n,height:r,initWH:i,mergedColor:o,borderBox7:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i}=this;return ee("div",{ref:"borderBox7",class:"dv-border-box-7",style:`box-shadow: inset 0 0 40px ${r[0]}; border: 1px solid ${r[0]}; background-color: ${i}`},[ee("svg",{class:"dv-border-svg-container",width:t,height:n},[ee("polyline",{class:"dv-bb7-line-width-2",stroke:r[0],points:"0, 25 0, 0 25, 0"},null),ee("polyline",{class:"dv-bb7-line-width-2",stroke:r[0],points:`${t-25}, 0 ${t}, 0 ${t}, 25`},null),ee("polyline",{class:"dv-bb7-line-width-2",stroke:r[0],points:`${t-25}, ${n} ${t}, ${n} ${t}, ${n-25}`},null),ee("polyline",{class:"dv-bb7-line-width-2",stroke:r[0],points:`0, ${n-25} 0, ${n} 25, ${n}`},null),ee("polyline",{class:"dv-bb7-line-width-5",stroke:r[1],points:"0, 10 0, 0 10, 0"},null),ee("polyline",{class:"dv-bb7-line-width-5",stroke:r[1],points:`${t-10}, 0 ${t}, 0 ${t}, 10`},null),ee("polyline",{class:"dv-bb7-line-width-5",stroke:r[1],points:`${t-10}, ${n} ${t}, ${n} ${t}, ${n-10}`},null),ee("polyline",{class:"dv-bb7-line-width-5",stroke:r[1],points:`0, ${n-10} 0, ${n} 10, ${n}`},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),Ap={install(e){e.component("DvBorderBox7",W2)}},X2={...Xt,reverse:{type:Boolean,default:!1},dur:{type:Number,default:3}},K2=["#235fa7","#4fd2dd"],q2=Ne({props:X2,setup(e){const t=sn(),n=ye(null),r=ke({path:`border-box-8-path-${t}`,gradient:`border-box-8-gradient-${t}`,mask:`border-box-8-mask-${t}`}),{width:i,height:o,initWH:s}=De(n),a=Ce(()=>(i.value+o.value-5)*2),l=Ce(()=>e.reverse?`M 2.5, 2.5 L 2.5, ${o.value-2.5} L ${i.value-2.5}, ${o.value-2.5} L ${i.value-2.5}, 2.5 L 2.5, 2.5`:`M2.5, 2.5 L${i.value-2.5}, 2.5 L${i.value-2.5}, ${o.value-2.5} L2.5, ${o.value-2.5} L2.5, 2.5`),u=Kt(K2,$t(e,"color"));return{width:i,height:o,initWH:s,state:r,mergedColor:u,pathD:l,length:a,borderBox8:n}},render(){const{$slots:e,width:t,height:n,state:r,mergedColor:i,pathD:o,length:s,backgroundColor:a,dur:l}=this;return ee("div",{ref:"borderBox8",class:"dv-border-box-8"},[ee("svg",{class:"dv-border-svg-container",width:t,height:n},[ee("defs",null,[ee("path",{id:r.path,d:o,fill:"transparent"},null),ee("radialGradient",{id:r.gradient,cx:"50%",cy:"50%",r:"50%"},[ee("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"1"},null),ee("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"0"},null)]),ee("mask",{id:r.mask},[ee("circle",{cx:"0",cy:"0",r:"150",fill:`url(#${r.gradient})`},[ai("animateMotion",{dur:`${l}s`,path:o,rotate:"auto",repeatCount:"indefinite"})])])]),ee("polygon",{fill:a,points:`5, 5 ${t-5}, 5 ${t-5} ${n-5} 5, ${n-5}`},null),ee("use",{stroke:i[0],"stroke-width":"1","xlink:href":`#${r.path}`},null),ee("use",{stroke:i[1],"stroke-width":"3","xlink:href":`#${r.path}`,mask:`url(#${r.mask})`},[ee("animate",{attributeName:"stroke-dasharray",from:`0, ${s}`,to:`${s}, 0`,dur:`${l}s`,repeatCount:"indefinite"},null)])]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),Ip={install(e){e.component("DvBorderBox8",q2)}},z2=["#11eefd","#0078d2"],J2=Ne({props:Xt,setup(e){const t=sn(),n=ye(null),{width:r,height:i,initWH:o}=De(n),s=ke({gradientId:`border-box-9-gradient-${t}`,maskId:`border-box-9-mask-${t}`}),a=Kt(z2,$t(e,"color"));return{width:r,height:i,initWH:o,state:s,mergedColor:a,borderBox9:n}},render(){const{$slots:e,width:t,height:n,state:r,mergedColor:i,backgroundColor:o}=this;return ee("div",{ref:"borderBox9",class:"dv-border-box-9"},[ee("svg",{class:"dv-border-svg-container",width:t,height:n},[ee("defs",null,[ee("linearGradient",{id:r.gradientId,x1:"0%",y1:"0%",x2:"100%",y2:"100%"},[ee("animate",{attributeName:"x1",values:"0%;100%;0%",dur:"10s",begin:"0s",repeatCount:"indefinite"},null),ee("animate",{attributeName:"x2",values:"100%;0%;100%",dur:"10s",begin:"0s",repeatCount:"indefinite"},null),ee("stop",{offset:"0%","stop-color":i[0]},[ee("animate",{attributeName:"stop-color",values:`${i[0]};${i[1]};${i[0]}`,dur:"10s",begin:"0s",repeatCount:"indefinite"},null)]),ee("stop",{offset:"100%","stop-color":i[1]},[ee("animate",{attributeName:"stop-color",values:`${i[1]};${i[0]};${i[1]}`,dur:"10s",begin:"0s",repeatCount:"indefinite"},null)])]),ee("mask",{id:r.maskId},[ee("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`8, ${n*.4} 8, 3, ${t*.4+7}, 3`},null),ee("polyline",{fill:"#fff",points:`8, ${n*.15} 8, 3, ${t*.1+7}, 3
              ${t*.1}, 8 14, 8 14, ${n*.15-7}
            `},null),ee("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`${t*.5}, 3 ${t-3}, 3, ${t-3}, ${n*.25}`},null),ee("polyline",{fill:"#fff",points:`
              ${t*.52}, 3 ${t*.58}, 3
              ${t*.58-7}, 9 ${t*.52+7}, 9
            `},null),ee("polyline",{fill:"#fff",points:`
              ${t*.9}, 3 ${t-3}, 3 ${t-3}, ${n*.1}
              ${t-9}, ${n*.1-7} ${t-9}, 9 ${t*.9+7}, 9
            `},null),ee("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`8, ${n*.5} 8, ${n-3} ${t*.3+7}, ${n-3}`},null),ee("polyline",{fill:"#fff",points:`
              8, ${n*.55} 8, ${n*.7}
              2, ${n*.7-7} 2, ${n*.55+7}
            `},null),ee("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`${t*.35}, ${n-3} ${t-3}, ${n-3} ${t-3}, ${n*.35}`},null),ee("polyline",{fill:"#fff",points:`
              ${t*.92}, ${n-3} ${t-3}, ${n-3} ${t-3}, ${n*.8}
              ${t-9}, ${n*.8+7} ${t-9}, ${n-9} ${t*.92+7}, ${n-9}
            `},null)])]),ee("polygon",{fill:o,points:`
              15, 9 ${t*.1+1}, 9 ${t*.1+4}, 6 ${t*.52+2}, 6
              ${t*.52+6}, 10 ${t*.58-7}, 10 ${t*.58-2}, 6
              ${t*.9+2}, 6 ${t*.9+6}, 10 ${t-10}, 10 ${t-10}, ${n*.1-6}
              ${t-6}, ${n*.1-1} ${t-6}, ${n*.8+1} ${t-10}, ${n*.8+6}
              ${t-10}, ${n-10} ${t*.92+7}, ${n-10}  ${t*.92+2}, ${n-6}
              11, ${n-6} 11, ${n*.15-2} 15, ${n*.15-7}
            `},null),ee("rect",{x:"0",y:"0",width:t,height:n,fill:`url(#${r.gradientId})`,mask:`url(#${r.maskId})`},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),kp={install(e){e.component("DvBorderBox9",J2)}},Q2=["left-top","right-top","left-bottom","right-bottom"],Y2=["#1d48c4","#d3e1f8"],Z2=Ne({props:Xt,setup(e){const t=ye(null),{width:n,height:r,initWH:i}=De(t),o=Kt(Y2,$t(e,"color"));return{width:n,height:r,initWH:i,mergedColor:o,borderBox10:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i}=this;return ee("div",{ref:"borderBox10",class:"dv-border-box-10",style:`box-shadow: inset 0 0 25px 3px ${r[0]}`},[ee("svg",{class:"dv-border-svg-container",width:t,height:n},[ee("polygon",{fill:i,points:`
              4, 0 ${t-4}, 0 ${t}, 4 ${t}, ${n-4} ${t-4}, ${n}
              4, ${n} 0, ${n-4} 0, 4
            `},null)]),Q2.map(o=>ee("svg",{width:"150px",height:"150px",class:`${o} dv-border-svg-container`},[ee("polygon",{fill:r[1],points:"40, 0 5, 0 0, 5 0, 16 3, 19 3, 7 7, 3 35, 3"},null)])),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),$p={install(e){e.component("DvBorderBox10",Z2)}},eT={...Xt,title:{type:String,default:""},titleWidth:{type:Number,default:250},animate:{type:Boolean,default:!0}},Np=["#8aaafb","#1f33a2"],tT=Ne({props:eT,setup(e){const t=sn(),n=ye(null),{width:r,height:i,initWH:o}=De(n),s=ye(`border-box-11-filterId-${t}`),a=Kt(Np,$t(e,"color"));return{width:r,height:i,initWH:o,filterId:s,mergedColor:a,borderBox11:n}},render(){const{$slots:e,width:t,height:n,filterId:r,mergedColor:i,backgroundColor:o,title:s,titleWidth:a,animate:l}=this;return ee("div",{ref:"borderBox11",class:"dv-border-box-11"},[ee("svg",{class:"dv-border-svg-container",width:t,height:n},[ee("defs",null,[ee("filter",{id:r,height:"150%",width:"150%",x:"-25%",y:"-25%"},[ee("feMorphology",{operator:"dilate",radius:"2",in:"SourceAlpha",result:"thicken"},null),ee("feGaussianBlur",{in:"thicken",stdDeviation:"3",result:"blurred"},null),ee("feFlood",{"flood-color":i[1],result:"glowColor"},null),ee("feComposite",{in:"glowColor",in2:"blurred",operator:"in",result:"softGlowColored"},null),ee("feMerge",null,[ee("feMergeNode",{in:"softGlowColored"},null),ee("feMergeNode",{in:"SourceGraphic"},null)])])]),ee("polygon",{fill:o,points:`
        20, 32 ${t*.5-a/2}, 32 ${t*.5-a/2+20}, 53
        ${t*.5+a/2-20}, 53 ${t*.5+a/2}, 32
        ${t-20}, 32 ${t-8}, 48 ${t-8}, ${n-25} ${t-20}, ${n-8}
        20, ${n-8} 8, ${n-25} 8, 50
      `},null),ee("polyline",{stroke:i[0],filter:`url(#${r})`,points:`
          ${(t-a)/2}, 30
          20, 30 7, 50 7, ${50+(n-167)/2}
          13, ${55+(n-167)/2} 13, ${135+(n-167)/2}
          7, ${140+(n-167)/2} 7, ${n-27}
          20, ${n-7} ${t-20}, ${n-7} ${t-7}, ${n-27}
          ${t-7}, ${140+(n-167)/2} ${t-13}, ${135+(n-167)/2}
          ${t-13}, ${55+(n-167)/2} ${t-7}, ${50+(n-167)/2}
          ${t-7}, 50 ${t-20}, 30 ${(t+a)/2}, 30
          ${(t+a)/2-20}, 7 ${(t-a)/2+20}, 7
          ${(t-a)/2}, 30 ${(t-a)/2+20}, 52
          ${(t+a)/2-20}, 52 ${(t+a)/2}, 30
        `},null),ee("polygon",{stroke:i[0],fill:"transparent",points:`
          ${(t+a)/2-5}, 30 ${(t+a)/2-21}, 11
          ${(t+a)/2-27}, 11 ${(t+a)/2-8}, 34
        `},null),ee("polygon",{stroke:i[0],fill:"transparent",points:`
          ${(t-a)/2+5}, 30 ${(t-a)/2+22}, 49
          ${(t-a)/2+28}, 49 ${(t-a)/2+8}, 26
        `},null),ee("polygon",{stroke:i[0],fill:ut(i[1]||Np[1],30),filter:`url(#${r})`,points:`
          ${(t+a)/2-11}, 37 ${(t+a)/2-32}, 11
          ${(t-a)/2+23}, 11 ${(t-a)/2+11}, 23
          ${(t-a)/2+33}, 49 ${(t+a)/2-22}, 49
        `},null),ee("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"1",points:`
          ${(t-a)/2-10}, 37 ${(t-a)/2-31}, 37
          ${(t-a)/2-25}, 46 ${(t-a)/2-4}, 46
        `},[l&&ee("animate",{attributeName:"opacity",values:"1;0.7;1",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),ee("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"0.7",points:`
          ${(t-a)/2-40}, 37 ${(t-a)/2-61}, 37
          ${(t-a)/2-55}, 46 ${(t-a)/2-34}, 46
        `},[l&&ee("animate",{attributeName:"opacity",values:"0.7;0.4;0.7",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),ee("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"0.5",points:`
          ${(t-a)/2-70}, 37 ${(t-a)/2-91}, 37
          ${(t-a)/2-85}, 46 ${(t-a)/2-64}, 46
        `},[l&&ee("animate",{attributeName:"opacity",values:"0.5;0.2;0.5",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),ee("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"1",points:`
          ${(t+a)/2+30}, 37 ${(t+a)/2+9}, 37
          ${(t+a)/2+3}, 46 ${(t+a)/2+24}, 46
        `},[l&&ee("animate",{attributeName:"opacity",values:"1;0.7;1",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),ee("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"0.7",points:`
          ${(t+a)/2+60}, 37 ${(t+a)/2+39}, 37
          ${(t+a)/2+33}, 46 ${(t+a)/2+54}, 46
        `},[l&&ee("animate",{attributeName:"opacity",values:"0.7;0.4;0.7",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),ee("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"0.5",points:`
          ${(t+a)/2+90}, 37 ${(t+a)/2+69}, 37
          ${(t+a)/2+63}, 46 ${(t+a)/2+84}, 46
        `},[l&&ee("animate",{attributeName:"opacity",values:"0.5;0.2;0.5",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),ee("text",{class:"dv-border-box-11-title",x:`${t/2}`,y:"32",fill:"#fff","font-size":"18","text-anchor":"middle","dominant-baseline":"middle"},[s]),ee("polygon",{fill:i[0],filter:`url(#${r})`,points:`
          7, ${53+(n-167)/2} 11, ${57+(n-167)/2}
          11, ${133+(n-167)/2} 7, ${137+(n-167)/2}
        `},null),ee("polygon",{fill:i[0],filter:`url(#${r})`,points:`
          ${t-7}, ${53+(n-167)/2} ${t-11}, ${57+(n-167)/2}
          ${t-11}, ${133+(n-167)/2} ${t-7}, ${137+(n-167)/2}
        `},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),Mp={install(e){e.component("DvBorderBox11",tT)}},Si=["#2e6099","#7ce7fd"],nT=Ne({props:Xt,setup(e){const t=sn(),n=ye(null),{width:r,height:i,initWH:o}=De(n),s=ye(`borderr-box-12-filterId-${t}`),a=Kt(Si,$t(e,"color"));return{width:r,height:i,filterId:s,mergedColor:a,initWH:o,borderBox12:n}},render(){const{$slots:e,width:t,height:n,filterId:r,mergedColor:i,backgroundColor:o}=this;return ee("div",{ref:"borderBox12",class:"dv-border-box-12"},[ee("svg",{class:"dv-border-svg-container",width:t,height:n},[ee("defs",null,[ee("filter",{id:r,height:"150%",width:"150%",x:"-25%",y:"-25%"},[ee("feMorphology",{operator:"dilate",radius:"1",in:"SourceAlpha",result:"thicken"},null),ee("feGaussianBlur",{in:"thicken",stdDeviation:"2",result:"blurred"},null),ee("feFlood",{"flood-color":ut(i[1]||Si[1],70),result:"glowColor"},[ee("animate",{attributeName:"flood-color",values:`
                ${ut(i[1]||Si[1],70)};
                ${ut(i[1]||Si[1],30)};
                ${ut(i[1]||Si[1],70)};
              `,dur:"3s",begin:"0s",repeatCount:"indefinite"},null)]),ee("feComposite",{in:"glowColor",in2:"blurred",operator:"in",result:"softGlowColored"},null),ee("feMerge",null,[ee("feMergeNode",{in:"softGlowColored"},null),ee("feMergeNode",{in:"SourceGraphic"},null)])])]),t&&n&&ee("path",{fill:o,"stroke-width":"2",stroke:i[0],d:`
          M15 5 L ${t-15} 5 Q ${t-5} 5, ${t-5} 15
          L ${t-5} ${n-15} Q ${t-5} ${n-5}, ${t-15} ${n-5}
          L 15, ${n-5} Q 5 ${n-5} 5 ${n-15} L 5 15
          Q 5 5 15 5
        `},null),ee("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${r})`,stroke:i[1],d:"M 20 5 L 15 5 Q 5 5 5 15 L 5 20"},null),ee("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${r})`,stroke:i[1],d:`M ${t-20} 5 L ${t-15} 5 Q ${t-5} 5 ${t-5} 15 L ${t-5} 20`},null),ee("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${r})`,stroke:i[1],d:`
          M ${t-20} ${n-5} L ${t-15} ${n-5}
          Q ${t-5} ${n-5} ${t-5} ${n-15}
          L ${t-5} ${n-20}
          `},null),ee("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${r})`,stroke:i[1],d:`
          M 20 ${n-5} L 15 ${n-5}
          Q 5 ${n-5} 5 ${n-15}
          L 5 ${n-20}
          `},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),Rp={install(e){e.component("DvBorderBox12",nT)}},rT=["#6586ec","#2cf7fe"],iT=Ne({props:Xt,setup(e){const t=ye(null),{width:n,height:r,initWH:i}=De(t),o=Kt(rT,$t(e,"color"));return{width:n,height:r,mergedColor:o,initWH:i,borderBox13:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i}=this;return ee("div",{ref:"borderBox13",class:"dv-border-box-13"},[ee("svg",{class:"dv-border-svg-container",width:t,height:n},[ee("path",{fill:i,stroke:r[0],d:`
          M 5 20 L 5 10 L 12 3  L 60 3 L 68 10
          L ${t-20} 10 L ${t-5} 25
          L ${t-5} ${n-5} L 20 ${n-5}
          L 5 ${n-20} L 5 20
        `},null),ee("path",{fill:"transparent","stroke-width":"3","stroke-linecap":"round","stroke-dasharray":"10, 5",stroke:r[0],d:"M 16 9 L 61 9"},null),ee("path",{fill:"transparent",stroke:r[1],d:"M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"},null),ee("path",{fill:"transparent",stroke:r[1],d:`M ${t-5} ${n-30} L ${t-5} ${n-5} L ${t-30} ${n-5}`},null)]),ee("div",{class:"border-box-content"},[ze(e,"default")])])}}),Lp={install(e){e.component("DvBorderBox13",iT)}},IP={install(e){var t,n,r,i,o,s,a,l,u,c,f,p,d,h,x,j,L,A,g,C,b,R,T,O,y,S,H,I,M,z,Z,J,P,W,D,V,X,te,ne;(t=pf.install)==null||t.call(pf,e),(n=mf.install)==null||n.call(mf,e),(r=vf.install)==null||r.call(vf,e),(i=Cd.install)==null||i.call(Cd,e),(o=xd.install)==null||o.call(xd,e),(s=Ed.install)==null||s.call(Ed,e),(a=Sd.install)==null||a.call(Sd,e),(l=wd.install)==null||l.call(wd,e),(u=Td.install)==null||u.call(Td,e),(c=sp.install)==null||c.call(sp,e),(f=ap.install)==null||f.call(ap,e),(p=lp.install)==null||p.call(lp,e),(d=up.install)==null||d.call(up,e),(h=cp.install)==null||h.call(cp,e),(x=fp.install)==null||x.call(fp,e),(j=dp.install)==null||j.call(dp,e),(L=pp.install)==null||L.call(pp,e),(A=hp.install)==null||A.call(hp,e),(g=gp.install)==null||g.call(gp,e),(C=mp.install)==null||C.call(mp,e),(b=vp.install)==null||b.call(vp,e),(R=yp.install)==null||R.call(yp,e),(T=bp.install)==null||T.call(bp,e),(O=_p.install)==null||O.call(_p,e),(y=Cp.install)==null||y.call(Cp,e),(S=xp.install)==null||S.call(xp,e),(H=Ep.install)==null||H.call(Ep,e),(I=Sp.install)==null||I.call(Sp,e),(M=wp.install)==null||M.call(wp,e),(z=Tp.install)==null||z.call(Tp,e),(Z=Op.install)==null||Z.call(Op,e),(J=Pp.install)==null||J.call(Pp,e),(P=Ap.install)==null||P.call(Ap,e),(W=Ip.install)==null||W.call(Ip,e),(D=kp.install)==null||D.call(kp,e),(V=$p.install)==null||V.call($p,e),(X=Mp.install)==null||X.call(Mp,e),(te=Rp.install)==null||te.call(Rp,e),(ne=Lp.install)==null||ne.call(Lp,e)}};function oT(e,t,n){return(t=lT(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Dp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function sT(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Dp(Object(n),!0).forEach(function(r){oT(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Dp(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function aT(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t);if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function lT(e){var t=aT(e,"string");return typeof t=="symbol"?t:t+""}function Ri(e){"@babel/helpers - typeof";return Ri=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ri(e)}var Vr=Symbol("unassigned"),uT="The Handsontable instance bound to this component was destroyed and cannot be used properly.";function Es(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function I0(e){var t=Qa.hooks.getRegistered(),n={};Object.assign(n,Qa.DefaultSettings);for(var r in n)n[r]={default:Vr};for(var i=0;i<t.length;i++)n[t[i]]={default:Vr};return n.settings={default:Vr},e==="HotTable"&&(n.id={type:String,default:"hot-".concat(Math.random().toString(36).substring(5))}),n}function k0(e){var t={},n=e.settings;if(n!==Vr)for(var r in n)Es(n,r)&&n[r]!==Vr&&(t[r]=n[r]);for(var i in e)Es(e,i)&&i!=="settings"&&e[i]!==Vr&&(t[i]=e[i]);return t}function Fp(e,t){var n=k0(e),r=e.settings?e.settings:n,i=e.settings?n:null,o={};for(var s in r)Es(r,s)&&r[s]!==void 0&&(!(t&&s!=="data")||!jp(t[s],r[s]))&&(o[s]=r[s]);for(var a in i)Es(i,a)&&a!=="id"&&a!=="settings"&&i[a]!==void 0&&(!(t&&a!=="data")||!jp(t[a],i[a]))&&(o[a]=i[a]);return o}function jp(e,t){var n=function(i){var o=function(){var s=new WeakSet;return function(a,l){if(Ri(l)==="object"&&l!==null){if(s.has(l))return;s.add(l)}return l}}();return JSON.stringify(i,o)};return typeof e=="function"&&typeof t=="function"?e.toString()===t.toString():Ri(e)!==Ri(t)?!1:n(e)===n(t)}var cT="15.3.0",$0=Ne({name:"HotTable",props:I0("HotTable"),provide:function(){return{columnsCache:this.columnsCache}},watch:{$props:{handler:function(t){var n=Fp(t,this.hotInstance?this.hotInstance.getSettings():void 0);!this.hotInstance||n===void 0||(n.data&&(this.hotInstance.isColumnModificationAllowed()||!this.hotInstance.isColumnModificationAllowed()&&this.hotInstance.countSourceCols()===this.miscCache.currentSourceColumns)&&(this.matchHotMappersSize(),delete n.data),Object.keys(n).length?this.hotInstance.updateSettings(n):this.hotInstance.render(),this.miscCache.currentSourceColumns=this.hotInstance.countSourceCols())},deep:!0,immediate:!0}},data:function(){return{__hotInstance:null,miscCache:{currentSourceColumns:null},columnSettings:null,columnsCache:new Map,get hotInstance(){return!this.__hotInstance||this.__hotInstance&&!this.__hotInstance.isDestroyed?this.__hotInstance:(console.warn(uT),null)},set hotInstance(t){this.__hotInstance=t}}},methods:{hotInit:function(){var t=Fp(this.$props);t.columns=this.columnSettings?this.columnSettings:t.columns,this.hotInstance=Gs(new Qa.Core(this.$el,t)),this.hotInstance.init(),this.miscCache.currentSourceColumns=this.hotInstance.countSourceCols()},matchHotMappersSize:function(){var t=this;if(this.hotInstance){var n=this.hotInstance.getSourceData(),r=[],i=[],o=this.hotInstance.rowIndexMapper.getNumberOfIndexes(),s=this.hotInstance.isColumnModificationAllowed(),a=0;if(n&&n.length!==o&&n.length<o)for(var l=n.length;l<o;l++)r.push(l);if(s){var u;if(a=this.hotInstance.columnIndexMapper.getNumberOfIndexes(),n&&n[0]&&((u=n[0])===null||u===void 0?void 0:u.length)!==a&&n[0].length<a)for(var c=n[0].length;c<a;c++)i.push(c)}this.hotInstance.batch(function(){r.length>0?t.hotInstance.rowIndexMapper.removeIndexes(r):t.hotInstance.rowIndexMapper.insertIndexes(o-1,n.length-o),s&&n.length!==0&&(i.length>0?t.hotInstance.columnIndexMapper.removeIndexes(i):t.hotInstance.columnIndexMapper.insertIndexes(a-1,n[0].length-a))})}},getColumnSettings:function(){var t=Array.from(this.columnsCache.values());return t.length?t:void 0}},mounted:function(){this.columnSettings=this.getColumnSettings(),this.hotInit()},beforeUnmount:function(){this.hotInstance&&this.hotInstance.destroy()},version:cT}),fT=["id"];function dT(e,t,n,r,i,o){return fe(),de("div",{id:e.id},[ze(e.$slots,"default")],8,fT)}$0.render=dT;$0.__file="src/HotTable.vue";var pT=Ne({name:"HotColumn",props:I0("HotColumn"),inject:["columnsCache"],methods:{createColumnSettings:function(){var t=k0(this.$props),n=sT({},t);t.renderer&&(n.renderer=t.renderer),t.editor&&(n.editor=t.editor),this.columnsCache.set(this,n)}},mounted:function(){this.createColumnSettings()},unmounted:function(){this.columnsCache.delete(this)},render:function(){return null}});pT.__file="src/HotColumn.vue";var qa={exports:{}},za={};/**
* @vue/compiler-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Yr=Symbol(""),Hr=Symbol(""),pa=Symbol(""),Ji=Symbol(""),Eu=Symbol(""),Yn=Symbol(""),Su=Symbol(""),wu=Symbol(""),ha=Symbol(""),ga=Symbol(""),li=Symbol(""),ma=Symbol(""),Tu=Symbol(""),va=Symbol(""),ya=Symbol(""),ba=Symbol(""),_a=Symbol(""),Ca=Symbol(""),xa=Symbol(""),Ou=Symbol(""),Pu=Symbol(""),yo=Symbol(""),Qi=Symbol(""),Ea=Symbol(""),Sa=Symbol(""),Zr=Symbol(""),ui=Symbol(""),wa=Symbol(""),Ss=Symbol(""),N0=Symbol(""),ws=Symbol(""),Yi=Symbol(""),M0=Symbol(""),R0=Symbol(""),Ta=Symbol(""),L0=Symbol(""),D0=Symbol(""),Oa=Symbol(""),Au=Symbol(""),Cr={[Yr]:"Fragment",[Hr]:"Teleport",[pa]:"Suspense",[Ji]:"KeepAlive",[Eu]:"BaseTransition",[Yn]:"openBlock",[Su]:"createBlock",[wu]:"createElementBlock",[ha]:"createVNode",[ga]:"createElementVNode",[li]:"createCommentVNode",[ma]:"createTextVNode",[Tu]:"createStaticVNode",[va]:"resolveComponent",[ya]:"resolveDynamicComponent",[ba]:"resolveDirective",[_a]:"resolveFilter",[Ca]:"withDirectives",[xa]:"renderList",[Ou]:"renderSlot",[Pu]:"createSlots",[yo]:"toDisplayString",[Qi]:"mergeProps",[Ea]:"normalizeClass",[Sa]:"normalizeStyle",[Zr]:"normalizeProps",[ui]:"guardReactiveProps",[wa]:"toHandlers",[Ss]:"camelize",[N0]:"capitalize",[ws]:"toHandlerKey",[Yi]:"setBlockTracking",[M0]:"pushScopeId",[R0]:"popScopeId",[Ta]:"withCtx",[L0]:"unref",[D0]:"isRef",[Oa]:"withMemo",[Au]:"isMemoSame"};function F0(e){Object.getOwnPropertySymbols(e).forEach(t=>{Cr[t]=e[t]})}const hT={HTML:0,0:"HTML",SVG:1,1:"SVG",MATH_ML:2,2:"MATH_ML"},gT={ROOT:0,0:"ROOT",ELEMENT:1,1:"ELEMENT",TEXT:2,2:"TEXT",COMMENT:3,3:"COMMENT",SIMPLE_EXPRESSION:4,4:"SIMPLE_EXPRESSION",INTERPOLATION:5,5:"INTERPOLATION",ATTRIBUTE:6,6:"ATTRIBUTE",DIRECTIVE:7,7:"DIRECTIVE",COMPOUND_EXPRESSION:8,8:"COMPOUND_EXPRESSION",IF:9,9:"IF",IF_BRANCH:10,10:"IF_BRANCH",FOR:11,11:"FOR",TEXT_CALL:12,12:"TEXT_CALL",VNODE_CALL:13,13:"VNODE_CALL",JS_CALL_EXPRESSION:14,14:"JS_CALL_EXPRESSION",JS_OBJECT_EXPRESSION:15,15:"JS_OBJECT_EXPRESSION",JS_PROPERTY:16,16:"JS_PROPERTY",JS_ARRAY_EXPRESSION:17,17:"JS_ARRAY_EXPRESSION",JS_FUNCTION_EXPRESSION:18,18:"JS_FUNCTION_EXPRESSION",JS_CONDITIONAL_EXPRESSION:19,19:"JS_CONDITIONAL_EXPRESSION",JS_CACHE_EXPRESSION:20,20:"JS_CACHE_EXPRESSION",JS_BLOCK_STATEMENT:21,21:"JS_BLOCK_STATEMENT",JS_TEMPLATE_LITERAL:22,22:"JS_TEMPLATE_LITERAL",JS_IF_STATEMENT:23,23:"JS_IF_STATEMENT",JS_ASSIGNMENT_EXPRESSION:24,24:"JS_ASSIGNMENT_EXPRESSION",JS_SEQUENCE_EXPRESSION:25,25:"JS_SEQUENCE_EXPRESSION",JS_RETURN_STATEMENT:26,26:"JS_RETURN_STATEMENT"},mT={ELEMENT:0,0:"ELEMENT",COMPONENT:1,1:"COMPONENT",SLOT:2,2:"SLOT",TEMPLATE:3,3:"TEMPLATE"},vT={NOT_CONSTANT:0,0:"NOT_CONSTANT",CAN_SKIP_PATCH:1,1:"CAN_SKIP_PATCH",CAN_CACHE:2,2:"CAN_CACHE",CAN_STRINGIFY:3,3:"CAN_STRINGIFY"},at={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0},source:""};function j0(e,t=""){return{type:0,source:t,children:e,helpers:new Set,components:[],directives:[],hoists:[],imports:[],cached:[],temps:0,codegenNode:void 0,loc:at}}function ei(e,t,n,r,i,o,s,a=!1,l=!1,u=!1,c=at){return e&&(a?(e.helper(Yn),e.helper(Sr(e.inSSR,u))):e.helper(Er(e.inSSR,u)),s&&e.helper(Ca)),{type:13,tag:t,props:n,children:r,patchFlag:i,dynamicProps:o,directives:s,isBlock:a,disableTracking:l,isComponent:u,loc:c}}function zn(e,t=at){return{type:17,loc:t,elements:e}}function Gt(e,t=at){return{type:15,loc:t,properties:e}}function Qe(e,t){return{type:16,loc:at,key:xe(e)?Ee(e,!0):e,value:t}}function Ee(e,t=!1,n=at,r=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:r}}function yT(e,t){return{type:5,loc:t,content:xe(e)?Ee(e,!1,t):e}}function Yt(e,t=at){return{type:8,loc:t,children:e}}function nt(e,t=[],n=at){return{type:14,loc:n,callee:e,arguments:t}}function xr(e,t=void 0,n=!1,r=!1,i=at){return{type:18,params:e,returns:t,newline:n,isSlot:r,loc:i}}function Ts(e,t,n,r=!0){return{type:19,test:e,consequent:t,alternate:n,newline:r,loc:at}}function B0(e,t,n=!1,r=!1){return{type:20,index:e,value:t,needPauseTracking:n,inVOnce:r,needArraySpread:!1,loc:at}}function V0(e){return{type:21,body:e,loc:at}}function bT(e){return{type:22,elements:e,loc:at}}function _T(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:at}}function CT(e,t){return{type:24,left:e,right:t,loc:at}}function xT(e){return{type:25,expressions:e,loc:at}}function ET(e){return{type:26,returns:e,loc:at}}function Er(e,t){return e||t?ha:ga}function Sr(e,t){return e||t?Su:wu}function Pa(e,{helper:t,removeHelper:n,inSSR:r}){e.isBlock||(e.isBlock=!0,n(Er(r,e.isComponent)),t(Yn),t(Sr(r,e.isComponent)))}const Bp=new Uint8Array([123,123]),Vp=new Uint8Array([125,125]);function Hp(e){return e>=97&&e<=122||e>=65&&e<=90}function Ht(e){return e===32||e===10||e===9||e===12||e===13}function Dn(e){return e===47||e===62||Ht(e)}function Os(e){const t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}const vt={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97])};class ST{constructor(t,n){this.stack=t,this.cbs=n,this.state=1,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=1,this.inRCDATA=!1,this.inXML=!1,this.inVPre=!1,this.newlines=[],this.mode=0,this.delimiterOpen=Bp,this.delimiterClose=Vp,this.delimiterIndex=-1,this.currentSequence=void 0,this.sequenceIndex=0}get inSFCRoot(){return this.mode===2&&this.stack.length===0}reset(){this.state=1,this.mode=0,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=1,this.inRCDATA=!1,this.currentSequence=void 0,this.newlines.length=0,this.delimiterOpen=Bp,this.delimiterClose=Vp}getPos(t){let n=1,r=t+1;for(let i=this.newlines.length-1;i>=0;i--){const o=this.newlines[i];if(t>o){n=i+2,r=t-o;break}}return{column:r,line:n,offset:t}}peek(){return this.buffer.charCodeAt(this.index+1)}stateText(t){t===60?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=5,this.sectionStart=this.index):!this.inVPre&&t===this.delimiterOpen[0]&&(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(t))}stateInterpolationOpen(t){if(t===this.delimiterOpen[this.delimiterIndex])if(this.delimiterIndex===this.delimiterOpen.length-1){const n=this.index+1-this.delimiterOpen.length;n>this.sectionStart&&this.cbs.ontext(this.sectionStart,n),this.state=3,this.sectionStart=n}else this.delimiterIndex++;else this.inRCDATA?(this.state=32,this.stateInRCDATA(t)):(this.state=1,this.stateText(t))}stateInterpolation(t){t===this.delimiterClose[0]&&(this.state=4,this.delimiterIndex=0,this.stateInterpolationClose(t))}stateInterpolationClose(t){t===this.delimiterClose[this.delimiterIndex]?this.delimiterIndex===this.delimiterClose.length-1?(this.cbs.oninterpolation(this.sectionStart,this.index+1),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):this.delimiterIndex++:(this.state=3,this.stateInterpolation(t))}stateSpecialStartSequence(t){const n=this.sequenceIndex===this.currentSequence.length;if(!(n?Dn(t):(t|32)===this.currentSequence[this.sequenceIndex]))this.inRCDATA=!1;else if(!n){this.sequenceIndex++;return}this.sequenceIndex=0,this.state=6,this.stateInTagName(t)}stateInRCDATA(t){if(this.sequenceIndex===this.currentSequence.length){if(t===62||Ht(t)){const n=this.index-this.currentSequence.length;if(this.sectionStart<n){const r=this.index;this.index=n,this.cbs.ontext(this.sectionStart,n),this.index=r}this.sectionStart=n+2,this.stateInClosingTagName(t),this.inRCDATA=!1;return}this.sequenceIndex=0}(t|32)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:this.sequenceIndex===0?this.currentSequence===vt.TitleEnd||this.currentSequence===vt.TextareaEnd&&!this.inSFCRoot?!this.inVPre&&t===this.delimiterOpen[0]&&(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(t)):this.fastForwardTo(60)&&(this.sequenceIndex=1):this.sequenceIndex=+(t===60)}stateCDATASequence(t){t===vt.Cdata[this.sequenceIndex]?++this.sequenceIndex===vt.Cdata.length&&(this.state=28,this.currentSequence=vt.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=23,this.stateInDeclaration(t))}fastForwardTo(t){for(;++this.index<this.buffer.length;){const n=this.buffer.charCodeAt(this.index);if(n===10&&this.newlines.push(this.index),n===t)return!0}return this.index=this.buffer.length-1,!1}stateInCommentLike(t){t===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===vt.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index-2):this.cbs.oncomment(this.sectionStart,this.index-2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=1):this.sequenceIndex===0?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):t!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}startSpecial(t,n){this.enterRCDATA(t,n),this.state=31}enterRCDATA(t,n){this.inRCDATA=!0,this.currentSequence=t,this.sequenceIndex=n}stateBeforeTagName(t){t===33?(this.state=22,this.sectionStart=this.index+1):t===63?(this.state=24,this.sectionStart=this.index+1):Hp(t)?(this.sectionStart=this.index,this.mode===0?this.state=6:this.inSFCRoot?this.state=34:this.inXML?this.state=6:t===116?this.state=30:this.state=t===115?29:6):t===47?this.state=8:(this.state=1,this.stateText(t))}stateInTagName(t){Dn(t)&&this.handleTagName(t)}stateInSFCRootTagName(t){if(Dn(t)){const n=this.buffer.slice(this.sectionStart,this.index);n!=="template"&&this.enterRCDATA(Os("</"+n),0),this.handleTagName(t)}}handleTagName(t){this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(t)}stateBeforeClosingTagName(t){Ht(t)||(t===62?(this.state=1,this.sectionStart=this.index+1):(this.state=Hp(t)?9:27,this.sectionStart=this.index))}stateInClosingTagName(t){(t===62||Ht(t))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=10,this.stateAfterClosingTagName(t))}stateAfterClosingTagName(t){t===62&&(this.state=1,this.sectionStart=this.index+1)}stateBeforeAttrName(t){t===62?(this.cbs.onopentagend(this.index),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):t===47?this.state=7:t===60&&this.peek()===47?(this.cbs.onopentagend(this.index),this.state=5,this.sectionStart=this.index):Ht(t)||this.handleAttrStart(t)}handleAttrStart(t){t===118&&this.peek()===45?(this.state=13,this.sectionStart=this.index):t===46||t===58||t===64||t===35?(this.cbs.ondirname(this.index,this.index+1),this.state=14,this.sectionStart=this.index+1):(this.state=12,this.sectionStart=this.index)}stateInSelfClosingTag(t){t===62?(this.cbs.onselfclosingtag(this.index),this.state=1,this.sectionStart=this.index+1,this.inRCDATA=!1):Ht(t)||(this.state=11,this.stateBeforeAttrName(t))}stateInAttrName(t){(t===61||Dn(t))&&(this.cbs.onattribname(this.sectionStart,this.index),this.handleAttrNameEnd(t))}stateInDirName(t){t===61||Dn(t)?(this.cbs.ondirname(this.sectionStart,this.index),this.handleAttrNameEnd(t)):t===58?(this.cbs.ondirname(this.sectionStart,this.index),this.state=14,this.sectionStart=this.index+1):t===46&&(this.cbs.ondirname(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDirArg(t){t===61||Dn(t)?(this.cbs.ondirarg(this.sectionStart,this.index),this.handleAttrNameEnd(t)):t===91?this.state=15:t===46&&(this.cbs.ondirarg(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDynamicDirArg(t){t===93?this.state=14:(t===61||Dn(t))&&(this.cbs.ondirarg(this.sectionStart,this.index+1),this.handleAttrNameEnd(t))}stateInDirModifier(t){t===61||Dn(t)?(this.cbs.ondirmodifier(this.sectionStart,this.index),this.handleAttrNameEnd(t)):t===46&&(this.cbs.ondirmodifier(this.sectionStart,this.index),this.sectionStart=this.index+1)}handleAttrNameEnd(t){this.sectionStart=this.index,this.state=17,this.cbs.onattribnameend(this.index),this.stateAfterAttrName(t)}stateAfterAttrName(t){t===61?this.state=18:t===47||t===62?(this.cbs.onattribend(0,this.sectionStart),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(t)):Ht(t)||(this.cbs.onattribend(0,this.sectionStart),this.handleAttrStart(t))}stateBeforeAttrValue(t){t===34?(this.state=19,this.sectionStart=this.index+1):t===39?(this.state=20,this.sectionStart=this.index+1):Ht(t)||(this.sectionStart=this.index,this.state=21,this.stateInAttrValueNoQuotes(t))}handleInAttrValue(t,n){(t===n||this.fastForwardTo(n))&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(n===34?3:2,this.index+1),this.state=11)}stateInAttrValueDoubleQuotes(t){this.handleInAttrValue(t,34)}stateInAttrValueSingleQuotes(t){this.handleInAttrValue(t,39)}stateInAttrValueNoQuotes(t){Ht(t)||t===62?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(1,this.index),this.state=11,this.stateBeforeAttrName(t)):(t===39||t===60||t===61||t===96)&&this.cbs.onerr(18,this.index)}stateBeforeDeclaration(t){t===91?(this.state=26,this.sequenceIndex=0):this.state=t===45?25:23}stateInDeclaration(t){(t===62||this.fastForwardTo(62))&&(this.state=1,this.sectionStart=this.index+1)}stateInProcessingInstruction(t){(t===62||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeComment(t){t===45?(this.state=28,this.currentSequence=vt.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=23}stateInSpecialComment(t){(t===62||this.fastForwardTo(62))&&(this.cbs.oncomment(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeSpecialS(t){t===vt.ScriptEnd[3]?this.startSpecial(vt.ScriptEnd,4):t===vt.StyleEnd[3]?this.startSpecial(vt.StyleEnd,4):(this.state=6,this.stateInTagName(t))}stateBeforeSpecialT(t){t===vt.TitleEnd[3]?this.startSpecial(vt.TitleEnd,4):t===vt.TextareaEnd[3]?this.startSpecial(vt.TextareaEnd,4):(this.state=6,this.stateInTagName(t))}startEntity(){}stateInEntity(){}parse(t){for(this.buffer=t;this.index<this.buffer.length;){const n=this.buffer.charCodeAt(this.index);switch(n===10&&this.newlines.push(this.index),this.state){case 1:{this.stateText(n);break}case 2:{this.stateInterpolationOpen(n);break}case 3:{this.stateInterpolation(n);break}case 4:{this.stateInterpolationClose(n);break}case 31:{this.stateSpecialStartSequence(n);break}case 32:{this.stateInRCDATA(n);break}case 26:{this.stateCDATASequence(n);break}case 19:{this.stateInAttrValueDoubleQuotes(n);break}case 12:{this.stateInAttrName(n);break}case 13:{this.stateInDirName(n);break}case 14:{this.stateInDirArg(n);break}case 15:{this.stateInDynamicDirArg(n);break}case 16:{this.stateInDirModifier(n);break}case 28:{this.stateInCommentLike(n);break}case 27:{this.stateInSpecialComment(n);break}case 11:{this.stateBeforeAttrName(n);break}case 6:{this.stateInTagName(n);break}case 34:{this.stateInSFCRootTagName(n);break}case 9:{this.stateInClosingTagName(n);break}case 5:{this.stateBeforeTagName(n);break}case 17:{this.stateAfterAttrName(n);break}case 20:{this.stateInAttrValueSingleQuotes(n);break}case 18:{this.stateBeforeAttrValue(n);break}case 8:{this.stateBeforeClosingTagName(n);break}case 10:{this.stateAfterClosingTagName(n);break}case 29:{this.stateBeforeSpecialS(n);break}case 30:{this.stateBeforeSpecialT(n);break}case 21:{this.stateInAttrValueNoQuotes(n);break}case 7:{this.stateInSelfClosingTag(n);break}case 23:{this.stateInDeclaration(n);break}case 22:{this.stateBeforeDeclaration(n);break}case 25:{this.stateBeforeComment(n);break}case 24:{this.stateInProcessingInstruction(n);break}case 33:{this.stateInEntity();break}}this.index++}this.cleanup(),this.finish()}cleanup(){this.sectionStart!==this.index&&(this.state===1||this.state===32&&this.sequenceIndex===0?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):(this.state===19||this.state===20||this.state===21)&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}finish(){this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){const t=this.buffer.length;this.sectionStart>=t||(this.state===28?this.currentSequence===vt.CdataEnd?this.cbs.oncdata(this.sectionStart,t):this.cbs.oncomment(this.sectionStart,t):this.state===6||this.state===11||this.state===18||this.state===17||this.state===12||this.state===13||this.state===14||this.state===15||this.state===16||this.state===20||this.state===19||this.state===21||this.state===9||this.cbs.ontext(this.sectionStart,t))}emitCodePoint(t,n){}}const wT={COMPILER_IS_ON_ELEMENT:"COMPILER_IS_ON_ELEMENT",COMPILER_V_BIND_SYNC:"COMPILER_V_BIND_SYNC",COMPILER_V_BIND_OBJECT_ORDER:"COMPILER_V_BIND_OBJECT_ORDER",COMPILER_V_ON_NATIVE:"COMPILER_V_ON_NATIVE",COMPILER_V_IF_V_FOR_PRECEDENCE:"COMPILER_V_IF_V_FOR_PRECEDENCE",COMPILER_NATIVE_TEMPLATE:"COMPILER_NATIVE_TEMPLATE",COMPILER_INLINE_TEMPLATE:"COMPILER_INLINE_TEMPLATE",COMPILER_FILTERS:"COMPILER_FILTERS"},TT={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3-migration.vuejs.org/breaking-changes/v-model.html"},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3-migration.vuejs.org/breaking-changes/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html"},COMPILER_FILTERS:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3-migration.vuejs.org/breaking-changes/filters.html"}};function Sl(e,{compatConfig:t}){const n=t&&t[e];return e==="MODE"?n||3:n}function vr(e,t){const n=Sl("MODE",t),r=Sl(e,t);return n===3?r===!0:r!==!1}function ti(e,t,n,...r){return vr(e,t)}function OT(e,t,n,...r){if(Sl(e,t)==="suppress-warning")return;const{message:o,link:s}=TT[e],a=`(deprecation ${e}) ${typeof o=="function"?o(...r):o}${s?`
  Details: ${s}`:""}`,l=new SyntaxError(a);l.code=e,n&&(l.loc=n),t.onWarn(l)}function Iu(e){throw e}function H0(e){}function Xe(e,t,n,r){const i=`https://vuejs.org/error-reference/#compiler-${e}`,o=new SyntaxError(String(i));return o.code=e,o.loc=t,o}const PT={ABRUPT_CLOSING_OF_EMPTY_COMMENT:0,0:"ABRUPT_CLOSING_OF_EMPTY_COMMENT",CDATA_IN_HTML_CONTENT:1,1:"CDATA_IN_HTML_CONTENT",DUPLICATE_ATTRIBUTE:2,2:"DUPLICATE_ATTRIBUTE",END_TAG_WITH_ATTRIBUTES:3,3:"END_TAG_WITH_ATTRIBUTES",END_TAG_WITH_TRAILING_SOLIDUS:4,4:"END_TAG_WITH_TRAILING_SOLIDUS",EOF_BEFORE_TAG_NAME:5,5:"EOF_BEFORE_TAG_NAME",EOF_IN_CDATA:6,6:"EOF_IN_CDATA",EOF_IN_COMMENT:7,7:"EOF_IN_COMMENT",EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT:8,8:"EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT",EOF_IN_TAG:9,9:"EOF_IN_TAG",INCORRECTLY_CLOSED_COMMENT:10,10:"INCORRECTLY_CLOSED_COMMENT",INCORRECTLY_OPENED_COMMENT:11,11:"INCORRECTLY_OPENED_COMMENT",INVALID_FIRST_CHARACTER_OF_TAG_NAME:12,12:"INVALID_FIRST_CHARACTER_OF_TAG_NAME",MISSING_ATTRIBUTE_VALUE:13,13:"MISSING_ATTRIBUTE_VALUE",MISSING_END_TAG_NAME:14,14:"MISSING_END_TAG_NAME",MISSING_WHITESPACE_BETWEEN_ATTRIBUTES:15,15:"MISSING_WHITESPACE_BETWEEN_ATTRIBUTES",NESTED_COMMENT:16,16:"NESTED_COMMENT",UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME:17,17:"UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME",UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE:18,18:"UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE",UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME:19,19:"UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME",UNEXPECTED_NULL_CHARACTER:20,20:"UNEXPECTED_NULL_CHARACTER",UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME:21,21:"UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME",UNEXPECTED_SOLIDUS_IN_TAG:22,22:"UNEXPECTED_SOLIDUS_IN_TAG",X_INVALID_END_TAG:23,23:"X_INVALID_END_TAG",X_MISSING_END_TAG:24,24:"X_MISSING_END_TAG",X_MISSING_INTERPOLATION_END:25,25:"X_MISSING_INTERPOLATION_END",X_MISSING_DIRECTIVE_NAME:26,26:"X_MISSING_DIRECTIVE_NAME",X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END:27,27:"X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END",X_V_IF_NO_EXPRESSION:28,28:"X_V_IF_NO_EXPRESSION",X_V_IF_SAME_KEY:29,29:"X_V_IF_SAME_KEY",X_V_ELSE_NO_ADJACENT_IF:30,30:"X_V_ELSE_NO_ADJACENT_IF",X_V_FOR_NO_EXPRESSION:31,31:"X_V_FOR_NO_EXPRESSION",X_V_FOR_MALFORMED_EXPRESSION:32,32:"X_V_FOR_MALFORMED_EXPRESSION",X_V_FOR_TEMPLATE_KEY_PLACEMENT:33,33:"X_V_FOR_TEMPLATE_KEY_PLACEMENT",X_V_BIND_NO_EXPRESSION:34,34:"X_V_BIND_NO_EXPRESSION",X_V_ON_NO_EXPRESSION:35,35:"X_V_ON_NO_EXPRESSION",X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET:36,36:"X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET",X_V_SLOT_MIXED_SLOT_USAGE:37,37:"X_V_SLOT_MIXED_SLOT_USAGE",X_V_SLOT_DUPLICATE_SLOT_NAMES:38,38:"X_V_SLOT_DUPLICATE_SLOT_NAMES",X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN:39,39:"X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN",X_V_SLOT_MISPLACED:40,40:"X_V_SLOT_MISPLACED",X_V_MODEL_NO_EXPRESSION:41,41:"X_V_MODEL_NO_EXPRESSION",X_V_MODEL_MALFORMED_EXPRESSION:42,42:"X_V_MODEL_MALFORMED_EXPRESSION",X_V_MODEL_ON_SCOPE_VARIABLE:43,43:"X_V_MODEL_ON_SCOPE_VARIABLE",X_V_MODEL_ON_PROPS:44,44:"X_V_MODEL_ON_PROPS",X_INVALID_EXPRESSION:45,45:"X_INVALID_EXPRESSION",X_KEEP_ALIVE_INVALID_CHILDREN:46,46:"X_KEEP_ALIVE_INVALID_CHILDREN",X_PREFIX_ID_NOT_SUPPORTED:47,47:"X_PREFIX_ID_NOT_SUPPORTED",X_MODULE_MODE_NOT_SUPPORTED:48,48:"X_MODULE_MODE_NOT_SUPPORTED",X_CACHE_HANDLER_NOT_SUPPORTED:49,49:"X_CACHE_HANDLER_NOT_SUPPORTED",X_SCOPE_ID_NOT_SUPPORTED:50,50:"X_SCOPE_ID_NOT_SUPPORTED",X_VNODE_HOOKS:51,51:"X_VNODE_HOOKS",X_V_BIND_INVALID_SAME_NAME_ARGUMENT:52,52:"X_V_BIND_INVALID_SAME_NAME_ARGUMENT",__EXTEND_POINT__:53,53:"__EXTEND_POINT__"},AT={0:"Illegal comment.",1:"CDATA section is allowed only in XML context.",2:"Duplicate attribute.",3:"End tag cannot have attributes.",4:"Illegal '/' in tags.",5:"Unexpected EOF in tag.",6:"Unexpected EOF in CDATA section.",7:"Unexpected EOF in comment.",8:"Unexpected EOF in script.",9:"Unexpected EOF in tag.",10:"Incorrectly closed comment.",11:"Incorrectly opened comment.",12:"Illegal tag name. Use '&lt;' to print '<'.",13:"Attribute value was expected.",14:"End tag name was expected.",15:"Whitespace was expected.",16:"Unexpected '<!--' in comment.",17:`Attribute name cannot contain U+0022 ("), U+0027 ('), and U+003C (<).`,18:"Unquoted attribute value cannot contain U+0022 (\"), U+0027 ('), U+003C (<), U+003D (=), and U+0060 (`).",19:"Attribute name cannot start with '='.",21:"'<?' is allowed only in XML context.",20:"Unexpected null character.",22:"Illegal '/' in tags.",23:"Invalid end tag.",24:"Element is missing end tag.",25:"Interpolation end sign was not found.",27:"End bracket for dynamic directive argument was not found. Note that dynamic directive argument cannot contain spaces.",26:"Legal directive name was expected.",28:"v-if/v-else-if is missing expression.",29:"v-if/else branches must use unique keys.",30:"v-else/v-else-if has no adjacent v-if or v-else-if.",31:"v-for is missing expression.",32:"v-for has invalid expression.",33:"<template v-for> key should be placed on the <template> tag.",34:"v-bind is missing expression.",52:"v-bind with same-name shorthand only allows static argument.",35:"v-on is missing expression.",36:"Unexpected custom directive on <slot> outlet.",37:"Mixed v-slot usage on both the component and nested <template>. When there are multiple named slots, all slots should use <template> syntax to avoid scope ambiguity.",38:"Duplicate slot names found. ",39:"Extraneous children found when component already has explicitly named default slot. These children will be ignored.",40:"v-slot can only be used on components or <template> tags.",41:"v-model is missing expression.",42:"v-model value must be a valid JavaScript member expression.",43:"v-model cannot be used on v-for or v-slot scope variables because they are not writable.",44:`v-model cannot be used on a prop, because local prop bindings are not writable.
Use a v-bind binding combined with a v-on listener that emits update:x event instead.`,45:"Error parsing JavaScript expression: ",46:"<KeepAlive> expects exactly one child component.",51:"@vnode-* hooks in templates are no longer supported. Use the vue: prefix instead. For example, @vnode-mounted should be changed to @vue:mounted. @vnode-* hooks support has been removed in 3.4.",47:'"prefixIdentifiers" option is not supported in this build of compiler.',48:"ES module mode is not supported in this build of compiler.",49:'"cacheHandlers" option is only supported when the "prefixIdentifiers" option is enabled.',50:'"scopeId" option is only supported in module mode.',53:""};function IT(e,t,n=!1,r=[],i=Object.create(null)){}function kT(e,t,n){return!1}function $T(e,t){if(e&&(e.type==="ObjectProperty"||e.type==="ArrayPattern")){let n=t.length;for(;n--;){const r=t[n];if(r.type==="AssignmentExpression")return!0;if(r.type!=="ObjectProperty"&&!r.type.endsWith("Pattern"))break}}return!1}function NT(e){let t=e.length;for(;t--;){const n=e[t];if(n.type==="NewExpression")return!0;if(n.type!=="MemberExpression")break}return!1}function MT(e,t){for(const n of e.params)for(const r of Sn(n))t(r)}function RT(e,t){for(const n of e.body)if(n.type==="VariableDeclaration"){if(n.declare)continue;for(const r of n.declarations)for(const i of Sn(r.id))t(i)}else if(n.type==="FunctionDeclaration"||n.type==="ClassDeclaration"){if(n.declare||!n.id)continue;t(n.id)}else LT(n)&&DT(n,!0,t)}function LT(e){return e.type==="ForOfStatement"||e.type==="ForInStatement"||e.type==="ForStatement"}function DT(e,t,n){const r=e.type==="ForStatement"?e.init:e.left;if(r&&r.type==="VariableDeclaration"&&(r.kind==="var"&&t))for(const i of r.declarations)for(const o of Sn(i.id))n(o)}function Sn(e,t=[]){switch(e.type){case"Identifier":t.push(e);break;case"MemberExpression":let n=e;for(;n.type==="MemberExpression";)n=n.object;t.push(n);break;case"ObjectPattern":for(const r of e.properties)r.type==="RestElement"?Sn(r.argument,t):Sn(r.value,t);break;case"ArrayPattern":e.elements.forEach(r=>{r&&Sn(r,t)});break;case"RestElement":Sn(e.argument,t);break;case"AssignmentPattern":Sn(e.left,t);break}return t}const FT=e=>/Function(?:Expression|Declaration)$|Method$/.test(e.type),G0=e=>e&&(e.type==="ObjectProperty"||e.type==="ObjectMethod")&&!e.computed,jT=(e,t)=>G0(t)&&t.key===e,U0=["TSAsExpression","TSTypeAssertion","TSNonNullExpression","TSInstantiationExpression","TSSatisfiesExpression"];function W0(e){return U0.includes(e.type)?W0(e.expression):e}const It=e=>e.type===4&&e.isStatic;function ku(e){switch(e){case"Teleport":case"teleport":return Hr;case"Suspense":case"suspense":return pa;case"KeepAlive":case"keep-alive":return Ji;case"BaseTransition":case"base-transition":return Eu}}const BT=/^\d|[^\$\w\xA0-\uFFFF]/,bo=e=>!BT.test(e),VT=/[A-Za-z_$\xA0-\uFFFF]/,HT=/[\.\?\w$\xA0-\uFFFF]/,GT=/\s+[.[]\s*|\s*[.[]\s+/g,X0=e=>e.type===4?e.content:e.loc.source,K0=e=>{const t=X0(e).trim().replace(GT,a=>a.trim());let n=0,r=[],i=0,o=0,s=null;for(let a=0;a<t.length;a++){const l=t.charAt(a);switch(n){case 0:if(l==="[")r.push(n),n=1,i++;else if(l==="(")r.push(n),n=2,o++;else if(!(a===0?VT:HT).test(l))return!1;break;case 1:l==="'"||l==='"'||l==="`"?(r.push(n),n=3,s=l):l==="["?i++:l==="]"&&(--i||(n=r.pop()));break;case 2:if(l==="'"||l==='"'||l==="`")r.push(n),n=3,s=l;else if(l==="(")o++;else if(l===")"){if(a===t.length-1)return!1;--o||(n=r.pop())}break;case 3:l===s&&(n=r.pop(),s=null);break}}return!i&&!o},UT=ft,$u=K0,WT=/^\s*(async\s*)?(\([^)]*?\)|[\w$_]+)\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,q0=e=>WT.test(X0(e)),XT=ft,z0=q0;function KT(e,t,n=t.length){return J0({offset:e.offset,line:e.line,column:e.column},t,n)}function J0(e,t,n=t.length){let r=0,i=-1;for(let o=0;o<n;o++)t.charCodeAt(o)===10&&(r++,i=o);return e.offset+=n,e.line+=r,e.column=i===-1?e.column+n:n-i,e}function qT(e,t){if(!e)throw new Error(t||"unexpected compiler condition")}function At(e,t,n=!1){for(let r=0;r<e.props.length;r++){const i=e.props[r];if(i.type===7&&(n||i.exp)&&(xe(t)?i.name===t:t.test(i.name)))return i}}function _o(e,t,n=!1,r=!1){for(let i=0;i<e.props.length;i++){const o=e.props[i];if(o.type===6){if(n)continue;if(o.name===t&&(o.value||r))return o}else if(o.name==="bind"&&(o.exp||r)&&Wn(o.arg,t))return o}}function Wn(e,t){return!!(e&&It(e)&&e.content===t)}function Q0(e){return e.props.some(t=>t.type===7&&t.name==="bind"&&(!t.arg||t.arg.type!==4||!t.arg.isStatic))}function ts(e){return e.type===5||e.type===2}function Nu(e){return e.type===7&&e.name==="slot"}function ni(e){return e.type===1&&e.tagType===3}function Zi(e){return e.type===1&&e.tagType===2}const zT=new Set([Zr,ui]);function Y0(e,t=[]){if(e&&!xe(e)&&e.type===14){const n=e.callee;if(!xe(n)&&zT.has(n))return Y0(e.arguments[0],t.concat(e))}return[e,t]}function eo(e,t,n){let r,i=e.type===13?e.props:e.arguments[2],o=[],s;if(i&&!xe(i)&&i.type===14){const a=Y0(i);i=a[0],o=a[1],s=o[o.length-1]}if(i==null||xe(i))r=Gt([t]);else if(i.type===14){const a=i.arguments[0];!xe(a)&&a.type===15?Gp(t,a)||a.properties.unshift(t):i.callee===wa?r=nt(n.helper(Qi),[Gt([t]),i]):i.arguments.unshift(Gt([t])),!r&&(r=i)}else i.type===15?(Gp(t,i)||i.properties.unshift(t),r=i):(r=nt(n.helper(Qi),[Gt([t]),i]),s&&s.callee===ui&&(s=o[o.length-2]));e.type===13?s?s.arguments[0]=r:e.props=r:s?s.arguments[0]=r:e.arguments[2]=r}function Gp(e,t){let n=!1;if(e.key.type===4){const r=e.key.content;n=t.properties.some(i=>i.key.type===4&&i.key.content===r)}return n}function ri(e,t){return`_${t}_${e.replace(/[^\w]/g,(n,r)=>n==="-"?"_":e.charCodeAt(r).toString())}`}function Zt(e,t){if(!e||Object.keys(t).length===0)return!1;switch(e.type){case 1:for(let n=0;n<e.props.length;n++){const r=e.props[n];if(r.type===7&&(Zt(r.arg,t)||Zt(r.exp,t)))return!0}return e.children.some(n=>Zt(n,t));case 11:return Zt(e.source,t)?!0:e.children.some(n=>Zt(n,t));case 9:return e.branches.some(n=>Zt(n,t));case 10:return Zt(e.condition,t)?!0:e.children.some(n=>Zt(n,t));case 4:return!e.isStatic&&bo(e.content)&&!!t[e.content];case 8:return e.children.some(n=>Le(n)&&Zt(n,t));case 5:case 12:return Zt(e.content,t);case 2:case 3:case 20:return!1;default:return!1}}function Z0(e){return e.type===14&&e.callee===Oa?e.arguments[1].returns:e}const ev=/([\s\S]*?)\s+(?:in|of)\s+(\S[\s\S]*)/,tv={parseMode:"base",ns:0,delimiters:["{{","}}"],getNamespace:()=>0,isVoidTag:Lr,isPreTag:Lr,isIgnoreNewlineTag:Lr,isCustomElement:Lr,onError:Iu,onWarn:H0,comments:!1,prefixIdentifiers:!1};let Me=tv,to=null,Pn="",yt=null,Ie=null,Rt="",bn=-1,cr=-1,Mu=0,Un=!1,wl=null;const Ke=[],Je=new ST(Ke,{onerr:yn,ontext(e,t){qo(ht(e,t),e,t)},ontextentity(e,t,n){qo(e,t,n)},oninterpolation(e,t){if(Un)return qo(ht(e,t),e,t);let n=e+Je.delimiterOpen.length,r=t-Je.delimiterClose.length;for(;Ht(Pn.charCodeAt(n));)n++;for(;Ht(Pn.charCodeAt(r-1));)r--;let i=ht(n,r);i.includes("&")&&(i=Me.decodeEntities(i,!1)),Tl({type:5,content:rs(i,!1,Ze(n,r)),loc:Ze(e,t)})},onopentagname(e,t){const n=ht(e,t);yt={type:1,tag:n,ns:Me.getNamespace(n,Ke[0],Me.ns),tagType:0,props:[],children:[],loc:Ze(e-1,t),codegenNode:void 0}},onopentagend(e){Wp(e)},onclosetag(e,t){const n=ht(e,t);if(!Me.isVoidTag(n)){let r=!1;for(let i=0;i<Ke.length;i++)if(Ke[i].tag.toLowerCase()===n.toLowerCase()){r=!0,i>0&&yn(24,Ke[0].loc.start.offset);for(let s=0;s<=i;s++){const a=Ke.shift();ns(a,t,s<i)}break}r||yn(23,nv(e,60))}},onselfclosingtag(e){const t=yt.tag;yt.isSelfClosing=!0,Wp(e),Ke[0]&&Ke[0].tag===t&&ns(Ke.shift(),e)},onattribname(e,t){Ie={type:6,name:ht(e,t),nameLoc:Ze(e,t),value:void 0,loc:Ze(e)}},ondirname(e,t){const n=ht(e,t),r=n==="."||n===":"?"bind":n==="@"?"on":n==="#"?"slot":n.slice(2);if(!Un&&r===""&&yn(26,e),Un||r==="")Ie={type:6,name:n,nameLoc:Ze(e,t),value:void 0,loc:Ze(e)};else if(Ie={type:7,name:r,rawName:n,exp:void 0,arg:void 0,modifiers:n==="."?[Ee("prop")]:[],loc:Ze(e)},r==="pre"){Un=Je.inVPre=!0,wl=yt;const i=yt.props;for(let o=0;o<i.length;o++)i[o].type===7&&(i[o]=sO(i[o]))}},ondirarg(e,t){if(e===t)return;const n=ht(e,t);if(Un)Ie.name+=n,fr(Ie.nameLoc,t);else{const r=n[0]!=="[";Ie.arg=rs(r?n:n.slice(1,-1),r,Ze(e,t),r?3:0)}},ondirmodifier(e,t){const n=ht(e,t);if(Un)Ie.name+="."+n,fr(Ie.nameLoc,t);else if(Ie.name==="slot"){const r=Ie.arg;r&&(r.content+="."+n,fr(r.loc,t))}else{const r=Ee(n,!0,Ze(e,t));Ie.modifiers.push(r)}},onattribdata(e,t){Rt+=ht(e,t),bn<0&&(bn=e),cr=t},onattribentity(e,t,n){Rt+=e,bn<0&&(bn=t),cr=n},onattribnameend(e){const t=Ie.loc.start.offset,n=ht(t,e);Ie.type===7&&(Ie.rawName=n),yt.props.some(r=>(r.type===7?r.rawName:r.name)===n)&&yn(2,t)},onattribend(e,t){if(yt&&Ie){if(fr(Ie.loc,t),e!==0)if(Rt.includes("&")&&(Rt=Me.decodeEntities(Rt,!0)),Ie.type===6)Ie.name==="class"&&(Rt=iv(Rt).trim()),e===1&&!Rt&&yn(13,t),Ie.value={type:2,content:Rt,loc:e===1?Ze(bn,cr):Ze(bn-1,cr+1)},Je.inSFCRoot&&yt.tag==="template"&&Ie.name==="lang"&&Rt&&Rt!=="html"&&Je.enterRCDATA(Os("</template"),0);else{let n=0;Ie.exp=rs(Rt,!1,Ze(bn,cr),0,n),Ie.name==="for"&&(Ie.forParseResult=QT(Ie.exp));let r=-1;Ie.name==="bind"&&(r=Ie.modifiers.findIndex(i=>i.content==="sync"))>-1&&ti("COMPILER_V_BIND_SYNC",Me,Ie.loc,Ie.rawName)&&(Ie.name="model",Ie.modifiers.splice(r,1))}(Ie.type!==7||Ie.name!=="pre")&&yt.props.push(Ie)}Rt="",bn=cr=-1},oncomment(e,t){Me.comments&&Tl({type:3,content:ht(e,t),loc:Ze(e-4,t+3)})},onend(){const e=Pn.length;for(let t=0;t<Ke.length;t++)ns(Ke[t],e-1),yn(24,Ke[t].loc.start.offset)},oncdata(e,t){Ke[0].ns!==0?qo(ht(e,t),e,t):yn(1,e-9)},onprocessinginstruction(e){(Ke[0]?Ke[0].ns:Me.ns)===0&&yn(21,e-1)}}),Up=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,JT=/^\(|\)$/g;function QT(e){const t=e.loc,n=e.content,r=n.match(ev);if(!r)return;const[,i,o]=r,s=(f,p,d=!1)=>{const h=t.start.offset+p,x=h+f.length;return rs(f,!1,Ze(h,x),0,d?1:0)},a={source:s(o.trim(),n.indexOf(o,i.length)),value:void 0,key:void 0,index:void 0,finalized:!1};let l=i.trim().replace(JT,"").trim();const u=i.indexOf(l),c=l.match(Up);if(c){l=l.replace(Up,"").trim();const f=c[1].trim();let p;if(f&&(p=n.indexOf(f,u+l.length),a.key=s(f,p,!0)),c[2]){const d=c[2].trim();d&&(a.index=s(d,n.indexOf(d,a.key?p+f.length:u+l.length),!0))}}return l&&(a.value=s(l,u,!0)),a}function ht(e,t){return Pn.slice(e,t)}function Wp(e){Je.inSFCRoot&&(yt.innerLoc=Ze(e+1,e+1)),Tl(yt);const{tag:t,ns:n}=yt;n===0&&Me.isPreTag(t)&&Mu++,Me.isVoidTag(t)?ns(yt,e):(Ke.unshift(yt),(n===1||n===2)&&(Je.inXML=!0)),yt=null}function qo(e,t,n){{const o=Ke[0]&&Ke[0].tag;o!=="script"&&o!=="style"&&e.includes("&")&&(e=Me.decodeEntities(e,!1))}const r=Ke[0]||to,i=r.children[r.children.length-1];i&&i.type===2?(i.content+=e,fr(i.loc,n)):r.children.push({type:2,content:e,loc:Ze(t,n)})}function ns(e,t,n=!1){n?fr(e.loc,nv(t,60)):fr(e.loc,YT(t,62)+1),Je.inSFCRoot&&(e.children.length?e.innerLoc.end=Pe({},e.children[e.children.length-1].loc.end):e.innerLoc.end=Pe({},e.innerLoc.start),e.innerLoc.source=ht(e.innerLoc.start.offset,e.innerLoc.end.offset));const{tag:r,ns:i,children:o}=e;if(Un||(r==="slot"?e.tagType=2:Xp(e)?e.tagType=3:eO(e)&&(e.tagType=1)),Je.inRCDATA||(e.children=rv(o)),i===0&&Me.isIgnoreNewlineTag(r)){const s=o[0];s&&s.type===2&&(s.content=s.content.replace(/^\r?\n/,""))}i===0&&Me.isPreTag(r)&&Mu--,wl===e&&(Un=Je.inVPre=!1,wl=null),Je.inXML&&(Ke[0]?Ke[0].ns:Me.ns)===0&&(Je.inXML=!1);{const s=e.props;if(!Je.inSFCRoot&&vr("COMPILER_NATIVE_TEMPLATE",Me)&&e.tag==="template"&&!Xp(e)){const l=Ke[0]||to,u=l.children.indexOf(e);l.children.splice(u,1,...e.children)}const a=s.find(l=>l.type===6&&l.name==="inline-template");a&&ti("COMPILER_INLINE_TEMPLATE",Me,a.loc)&&e.children.length&&(a.value={type:2,content:ht(e.children[0].loc.start.offset,e.children[e.children.length-1].loc.end.offset),loc:a.loc})}}function YT(e,t){let n=e;for(;Pn.charCodeAt(n)!==t&&n<Pn.length-1;)n++;return n}function nv(e,t){let n=e;for(;Pn.charCodeAt(n)!==t&&n>=0;)n--;return n}const ZT=new Set(["if","else","else-if","for","slot"]);function Xp({tag:e,props:t}){if(e==="template"){for(let n=0;n<t.length;n++)if(t[n].type===7&&ZT.has(t[n].name))return!0}return!1}function eO({tag:e,props:t}){if(Me.isCustomElement(e))return!1;if(e==="component"||tO(e.charCodeAt(0))||ku(e)||Me.isBuiltInComponent&&Me.isBuiltInComponent(e)||Me.isNativeTag&&!Me.isNativeTag(e))return!0;for(let n=0;n<t.length;n++){const r=t[n];if(r.type===6){if(r.name==="is"&&r.value){if(r.value.content.startsWith("vue:"))return!0;if(ti("COMPILER_IS_ON_ELEMENT",Me,r.loc))return!0}}else if(r.name==="bind"&&Wn(r.arg,"is")&&ti("COMPILER_IS_ON_ELEMENT",Me,r.loc))return!0}return!1}function tO(e){return e>64&&e<91}const nO=/\r\n/g;function rv(e,t){const n=Me.whitespace!=="preserve";let r=!1;for(let i=0;i<e.length;i++){const o=e[i];if(o.type===2)if(Mu)o.content=o.content.replace(nO,`
`);else if(rO(o.content)){const s=e[i-1]&&e[i-1].type,a=e[i+1]&&e[i+1].type;!s||!a||n&&(s===3&&(a===3||a===1)||s===1&&(a===3||a===1&&iO(o.content)))?(r=!0,e[i]=null):o.content=" "}else n&&(o.content=iv(o.content))}return r?e.filter(Boolean):e}function rO(e){for(let t=0;t<e.length;t++)if(!Ht(e.charCodeAt(t)))return!1;return!0}function iO(e){for(let t=0;t<e.length;t++){const n=e.charCodeAt(t);if(n===10||n===13)return!0}return!1}function iv(e){let t="",n=!1;for(let r=0;r<e.length;r++)Ht(e.charCodeAt(r))?n||(t+=" ",n=!0):(t+=e[r],n=!1);return t}function Tl(e){(Ke[0]||to).children.push(e)}function Ze(e,t){return{start:Je.getPos(e),end:t==null?t:Je.getPos(t),source:t==null?t:ht(e,t)}}function oO(e){return Ze(e.start.offset,e.end.offset)}function fr(e,t){e.end=Je.getPos(t),e.source=ht(e.start.offset,t)}function sO(e){const t={type:6,name:e.rawName,nameLoc:Ze(e.loc.start.offset,e.loc.start.offset+e.rawName.length),value:void 0,loc:e.loc};if(e.exp){const n=e.exp.loc;n.end.offset<e.loc.end.offset&&(n.start.offset--,n.start.column--,n.end.offset++,n.end.column++),t.value={type:2,content:e.exp.content,loc:n}}return t}function rs(e,t=!1,n,r=0,i=0){return Ee(e,t,n,r)}function yn(e,t,n){Me.onError(Xe(e,Ze(t,t)))}function aO(){Je.reset(),yt=null,Ie=null,Rt="",bn=-1,cr=-1,Ke.length=0}function Ru(e,t){if(aO(),Pn=e,Me=Pe({},tv),t){let i;for(i in t)t[i]!=null&&(Me[i]=t[i])}Je.mode=Me.parseMode==="html"?1:Me.parseMode==="sfc"?2:0,Je.inXML=Me.ns===1||Me.ns===2;const n=t&&t.delimiters;n&&(Je.delimiterOpen=Os(n[0]),Je.delimiterClose=Os(n[1]));const r=to=j0([],e);return Je.parse(Pn),r.loc=Ze(0,e.length),r.children=rv(r.children),to=null,r}function lO(e,t){is(e,void 0,t,ov(e,e.children[0]))}function ov(e,t){const{children:n}=e;return n.length===1&&t.type===1&&!Zi(t)}function is(e,t,n,r=!1,i=!1){const{children:o}=e,s=[];for(let c=0;c<o.length;c++){const f=o[c];if(f.type===1&&f.tagType===0){const p=r?0:Ft(f,n);if(p>0){if(p>=2){f.codegenNode.patchFlag=-1,s.push(f);continue}}else{const d=f.codegenNode;if(d.type===13){const h=d.patchFlag;if((h===void 0||h===512||h===1)&&av(f,n)>=2){const x=lv(f);x&&(d.props=n.hoist(x))}d.dynamicProps&&(d.dynamicProps=n.hoist(d.dynamicProps))}}}else if(f.type===12&&(r?0:Ft(f,n))>=2){s.push(f);continue}if(f.type===1){const p=f.tagType===1;p&&n.scopes.vSlot++,is(f,e,n,!1,i),p&&n.scopes.vSlot--}else if(f.type===11)is(f,e,n,f.children.length===1,!0);else if(f.type===9)for(let p=0;p<f.branches.length;p++)is(f.branches[p],e,n,f.branches[p].children.length===1,i)}let a=!1;if(s.length===o.length&&e.type===1){if(e.tagType===0&&e.codegenNode&&e.codegenNode.type===13&&ve(e.codegenNode.children))e.codegenNode.children=l(zn(e.codegenNode.children)),a=!0;else if(e.tagType===1&&e.codegenNode&&e.codegenNode.type===13&&e.codegenNode.children&&!ve(e.codegenNode.children)&&e.codegenNode.children.type===15){const c=u(e.codegenNode,"default");c&&(c.returns=l(zn(c.returns)),a=!0)}else if(e.tagType===3&&t&&t.type===1&&t.tagType===1&&t.codegenNode&&t.codegenNode.type===13&&t.codegenNode.children&&!ve(t.codegenNode.children)&&t.codegenNode.children.type===15){const c=At(e,"slot",!0),f=c&&c.arg&&u(t.codegenNode,c.arg);f&&(f.returns=l(zn(f.returns)),a=!0)}}if(!a)for(const c of s)c.codegenNode=n.cache(c.codegenNode);function l(c){const f=n.cache(c);return i&&n.hmr&&(f.needArraySpread=!0),f}function u(c,f){if(c.children&&!ve(c.children)&&c.children.type===15){const p=c.children.properties.find(d=>d.key===f||d.key.content===f);return p&&p.value}}s.length&&n.transformHoist&&n.transformHoist(o,n,e)}function Ft(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(e.tagType!==0)return 0;const r=n.get(e);if(r!==void 0)return r;const i=e.codegenNode;if(i.type!==13||i.isBlock&&e.tag!=="svg"&&e.tag!=="foreignObject"&&e.tag!=="math")return 0;if(i.patchFlag===void 0){let s=3;const a=av(e,t);if(a===0)return n.set(e,0),0;a<s&&(s=a);for(let l=0;l<e.children.length;l++){const u=Ft(e.children[l],t);if(u===0)return n.set(e,0),0;u<s&&(s=u)}if(s>1)for(let l=0;l<e.props.length;l++){const u=e.props[l];if(u.type===7&&u.name==="bind"&&u.exp){const c=Ft(u.exp,t);if(c===0)return n.set(e,0),0;c<s&&(s=c)}}if(i.isBlock){for(let l=0;l<e.props.length;l++)if(e.props[l].type===7)return n.set(e,0),0;t.removeHelper(Yn),t.removeHelper(Sr(t.inSSR,i.isComponent)),i.isBlock=!1,t.helper(Er(t.inSSR,i.isComponent))}return n.set(e,s),s}else return n.set(e,0),0;case 2:case 3:return 3;case 9:case 11:case 10:return 0;case 5:case 12:return Ft(e.content,t);case 4:return e.constType;case 8:let o=3;for(let s=0;s<e.children.length;s++){const a=e.children[s];if(xe(a)||kt(a))continue;const l=Ft(a,t);if(l===0)return 0;l<o&&(o=l)}return o;case 20:return 2;default:return 0}}const uO=new Set([Ea,Sa,Zr,ui]);function sv(e,t){if(e.type===14&&!xe(e.callee)&&uO.has(e.callee)){const n=e.arguments[0];if(n.type===4)return Ft(n,t);if(n.type===14)return sv(n,t)}return 0}function av(e,t){let n=3;const r=lv(e);if(r&&r.type===15){const{properties:i}=r;for(let o=0;o<i.length;o++){const{key:s,value:a}=i[o],l=Ft(s,t);if(l===0)return l;l<n&&(n=l);let u;if(a.type===4?u=Ft(a,t):a.type===14?u=sv(a,t):u=0,u===0)return u;u<n&&(n=u)}}return n}function lv(e){const t=e.codegenNode;if(t.type===13)return t.props}function uv(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:r=!1,hmr:i=!1,cacheHandlers:o=!1,nodeTransforms:s=[],directiveTransforms:a={},transformHoist:l=null,isBuiltInComponent:u=ft,isCustomElement:c=ft,expressionPlugins:f=[],scopeId:p=null,slotted:d=!0,ssr:h=!1,inSSR:x=!1,ssrCssVars:j="",bindingMetadata:L=Oe,inline:A=!1,isTS:g=!1,onError:C=Iu,onWarn:b=H0,compatConfig:R}){const T=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),O={filename:t,selfName:T&&$n(He(T[1])),prefixIdentifiers:n,hoistStatic:r,hmr:i,cacheHandlers:o,nodeTransforms:s,directiveTransforms:a,transformHoist:l,isBuiltInComponent:u,isCustomElement:c,expressionPlugins:f,scopeId:p,slotted:d,ssr:h,inSSR:x,ssrCssVars:j,bindingMetadata:L,inline:A,isTS:g,onError:C,onWarn:b,compatConfig:R,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],cached:[],constantCache:new WeakMap,temps:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,grandParent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(y){const S=O.helpers.get(y)||0;return O.helpers.set(y,S+1),y},removeHelper(y){const S=O.helpers.get(y);if(S){const H=S-1;H?O.helpers.set(y,H):O.helpers.delete(y)}},helperString(y){return`_${Cr[O.helper(y)]}`},replaceNode(y){O.parent.children[O.childIndex]=O.currentNode=y},removeNode(y){const S=O.parent.children,H=y?S.indexOf(y):O.currentNode?O.childIndex:-1;!y||y===O.currentNode?(O.currentNode=null,O.onNodeRemoved()):O.childIndex>H&&(O.childIndex--,O.onNodeRemoved()),O.parent.children.splice(H,1)},onNodeRemoved:ft,addIdentifiers(y){},removeIdentifiers(y){},hoist(y){xe(y)&&(y=Ee(y)),O.hoists.push(y);const S=Ee(`_hoisted_${O.hoists.length}`,!1,y.loc,2);return S.hoisted=y,S},cache(y,S=!1,H=!1){const I=B0(O.cached.length,y,S,H);return O.cached.push(I),I}};return O.filters=new Set,O}function cv(e,t){const n=uv(e,t);Co(e,n),t.hoistStatic&&lO(e,n),t.ssr||cO(e,n),e.helpers=new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.transformed=!0,e.filters=[...n.filters]}function cO(e,t){const{helper:n}=t,{children:r}=e;if(r.length===1){const i=r[0];if(ov(e,i)&&i.codegenNode){const o=i.codegenNode;o.type===13&&Pa(o,t),e.codegenNode=o}else e.codegenNode=i}else if(r.length>1){let i=64;e.codegenNode=ei(t,n(Yr),void 0,e.children,i,void 0,void 0,!0,void 0,!1)}}function fO(e,t){let n=0;const r=()=>{n--};for(;n<e.children.length;n++){const i=e.children[n];xe(i)||(t.grandParent=t.parent,t.parent=e,t.childIndex=n,t.onNodeRemoved=r,Co(i,t))}}function Co(e,t){t.currentNode=e;const{nodeTransforms:n}=t,r=[];for(let o=0;o<n.length;o++){const s=n[o](e,t);if(s&&(ve(s)?r.push(...s):r.push(s)),t.currentNode)e=t.currentNode;else return}switch(e.type){case 3:t.ssr||t.helper(li);break;case 5:t.ssr||t.helper(yo);break;case 9:for(let o=0;o<e.branches.length;o++)Co(e.branches[o],t);break;case 10:case 11:case 1:case 0:fO(e,t);break}t.currentNode=e;let i=r.length;for(;i--;)r[i]()}function Lu(e,t){const n=xe(e)?r=>r===e:r=>e.test(r);return(r,i)=>{if(r.type===1){const{props:o}=r;if(r.tagType===3&&o.some(Nu))return;const s=[];for(let a=0;a<o.length;a++){const l=o[a];if(l.type===7&&n(l.name)){o.splice(a,1),a--;const u=t(r,l,i);u&&s.push(u)}}return s}}}const Aa="/*@__PURE__*/",fv=e=>`${Cr[e]}: _${Cr[e]}`;function dO(e,{mode:t="function",prefixIdentifiers:n=t==="module",sourceMap:r=!1,filename:i="template.vue.html",scopeId:o=null,optimizeImports:s=!1,runtimeGlobalName:a="Vue",runtimeModuleName:l="vue",ssrRuntimeModuleName:u="vue/server-renderer",ssr:c=!1,isTS:f=!1,inSSR:p=!1}){const d={mode:t,prefixIdentifiers:n,sourceMap:r,filename:i,scopeId:o,optimizeImports:s,runtimeGlobalName:a,runtimeModuleName:l,ssrRuntimeModuleName:u,ssr:c,isTS:f,inSSR:p,source:e.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper(x){return`_${Cr[x]}`},push(x,j=-2,L){d.code+=x},indent(){h(++d.indentLevel)},deindent(x=!1){x?--d.indentLevel:h(--d.indentLevel)},newline(){h(d.indentLevel)}};function h(x){d.push(`
`+"  ".repeat(x),0)}return d}function dv(e,t={}){const n=dO(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:r,push:i,prefixIdentifiers:o,indent:s,deindent:a,newline:l,scopeId:u,ssr:c}=n,f=Array.from(e.helpers),p=f.length>0,d=!o&&r!=="module";pO(e,n);const x=c?"ssrRender":"render",L=(c?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ");if(i(`function ${x}(${L}) {`),s(),d&&(i("with (_ctx) {"),s(),p&&(i(`const { ${f.map(fv).join(", ")} } = _Vue
`,-1),l())),e.components.length&&(Ja(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(Ja(e.directives,"directive",n),e.temps>0&&l()),e.filters&&e.filters.length&&(l(),Ja(e.filters,"filter",n),l()),e.temps>0){i("let ");for(let A=0;A<e.temps;A++)i(`${A>0?", ":""}_temp${A}`)}return(e.components.length||e.directives.length||e.temps)&&(i(`
`,0),l()),c||i("return "),e.codegenNode?St(e.codegenNode,n):i("null"),d&&(a(),i("}")),a(),i("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function pO(e,t){const{ssr:n,prefixIdentifiers:r,push:i,newline:o,runtimeModuleName:s,runtimeGlobalName:a,ssrRuntimeModuleName:l}=t,u=a,c=Array.from(e.helpers);if(c.length>0&&(i(`const _Vue = ${u}
`,-1),e.hoists.length)){const f=[ha,ga,li,ma,Tu].filter(p=>c.includes(p)).map(fv).join(", ");i(`const { ${f} } = _Vue
`,-1)}hO(e.hoists,t),o(),i("return ")}function Ja(e,t,{helper:n,push:r,newline:i,isTS:o}){const s=n(t==="filter"?_a:t==="component"?va:ba);for(let a=0;a<e.length;a++){let l=e[a];const u=l.endsWith("__self");u&&(l=l.slice(0,-6)),r(`const ${ri(l,t)} = ${s}(${JSON.stringify(l)}${u?", true":""})${o?"!":""}`),a<e.length-1&&i()}}function hO(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:r}=t;r();for(let i=0;i<e.length;i++){const o=e[i];o&&(n(`const _hoisted_${i+1} = `),St(o,t),r())}t.pure=!1}function Du(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),xo(e,t,n),n&&t.deindent(),t.push("]")}function xo(e,t,n=!1,r=!0){const{push:i,newline:o}=t;for(let s=0;s<e.length;s++){const a=e[s];xe(a)?i(a,-3):ve(a)?Du(a,t):St(a,t),s<e.length-1&&(n?(r&&i(","),o()):r&&i(", "))}}function St(e,t){if(xe(e)){t.push(e,-3);return}if(kt(e)){t.push(t.helper(e));return}switch(e.type){case 1:case 9:case 11:St(e.codegenNode,t);break;case 2:gO(e,t);break;case 4:pv(e,t);break;case 5:mO(e,t);break;case 12:St(e.codegenNode,t);break;case 8:hv(e,t);break;case 3:yO(e,t);break;case 13:bO(e,t);break;case 14:CO(e,t);break;case 15:xO(e,t);break;case 17:EO(e,t);break;case 18:SO(e,t);break;case 19:wO(e,t);break;case 20:TO(e,t);break;case 21:xo(e.body,t,!0,!1);break}}function gO(e,t){t.push(JSON.stringify(e.content),-3,e)}function pv(e,t){const{content:n,isStatic:r}=e;t.push(r?JSON.stringify(n):n,-3,e)}function mO(e,t){const{push:n,helper:r,pure:i}=t;i&&n(Aa),n(`${r(yo)}(`),St(e.content,t),n(")")}function hv(e,t){for(let n=0;n<e.children.length;n++){const r=e.children[n];xe(r)?t.push(r,-3):St(r,t)}}function vO(e,t){const{push:n}=t;if(e.type===8)n("["),hv(e,t),n("]");else if(e.isStatic){const r=bo(e.content)?e.content:JSON.stringify(e.content);n(r,-2,e)}else n(`[${e.content}]`,-3,e)}function yO(e,t){const{push:n,helper:r,pure:i}=t;i&&n(Aa),n(`${r(li)}(${JSON.stringify(e.content)})`,-3,e)}function bO(e,t){const{push:n,helper:r,pure:i}=t,{tag:o,props:s,children:a,patchFlag:l,dynamicProps:u,directives:c,isBlock:f,disableTracking:p,isComponent:d}=e;let h;l&&(h=String(l)),c&&n(r(Ca)+"("),f&&n(`(${r(Yn)}(${p?"true":""}), `),i&&n(Aa);const x=f?Sr(t.inSSR,d):Er(t.inSSR,d);n(r(x)+"(",-2,e),xo(_O([o,s,a,h,u]),t),n(")"),f&&n(")"),c&&(n(", "),St(c,t),n(")"))}function _O(e){let t=e.length;for(;t--&&e[t]==null;);return e.slice(0,t+1).map(n=>n||"null")}function CO(e,t){const{push:n,helper:r,pure:i}=t,o=xe(e.callee)?e.callee:r(e.callee);i&&n(Aa),n(o+"(",-2,e),xo(e.arguments,t),n(")")}function xO(e,t){const{push:n,indent:r,deindent:i,newline:o}=t,{properties:s}=e;if(!s.length){n("{}",-2,e);return}const a=s.length>1||!1;n(a?"{":"{ "),a&&r();for(let l=0;l<s.length;l++){const{key:u,value:c}=s[l];vO(u,t),n(": "),St(c,t),l<s.length-1&&(n(","),o())}a&&i(),n(a?"}":" }")}function EO(e,t){Du(e.elements,t)}function SO(e,t){const{push:n,indent:r,deindent:i}=t,{params:o,returns:s,body:a,newline:l,isSlot:u}=e;u&&n(`_${Cr[Ta]}(`),n("(",-2,e),ve(o)?xo(o,t):o&&St(o,t),n(") => "),(l||a)&&(n("{"),r()),s?(l&&n("return "),ve(s)?Du(s,t):St(s,t)):a&&St(a,t),(l||a)&&(i(),n("}")),u&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}function wO(e,t){const{test:n,consequent:r,alternate:i,newline:o}=e,{push:s,indent:a,deindent:l,newline:u}=t;if(n.type===4){const f=!bo(n.content);f&&s("("),pv(n,t),f&&s(")")}else s("("),St(n,t),s(")");o&&a(),t.indentLevel++,o||s(" "),s("? "),St(r,t),t.indentLevel--,o&&u(),o||s(" "),s(": ");const c=i.type===19;c||t.indentLevel++,St(i,t),c||t.indentLevel--,o&&l(!0)}function TO(e,t){const{push:n,helper:r,indent:i,deindent:o,newline:s}=t,{needPauseTracking:a,needArraySpread:l}=e;l&&n("[...("),n(`_cache[${e.index}] || (`),a&&(i(),n(`${r(Yi)}(-1`),e.inVOnce&&n(", true"),n("),"),s(),n("(")),n(`_cache[${e.index}] = `),St(e.value,t),a&&(n(`).cacheIndex = ${e.index},`),s(),n(`${r(Yi)}(1),`),s(),n(`_cache[${e.index}]`),o()),n(")"),l&&n(")]")}new RegExp("\\b"+"arguments,await,break,case,catch,class,const,continue,debugger,default,delete,do,else,export,extends,finally,for,function,if,import,let,new,return,super,switch,throw,try,var,void,while,with,yield".split(",").join("\\b|\\b")+"\\b");const OO=(e,t)=>{if(e.type===5)e.content=os(e.content,t);else if(e.type===1){const n=At(e,"memo");for(let r=0;r<e.props.length;r++){const i=e.props[r];if(i.type===7&&i.name!=="for"){const o=i.exp,s=i.arg;o&&o.type===4&&!(i.name==="on"&&s)&&!(n&&s&&s.type===4&&s.content==="key")&&(i.exp=os(o,t,i.name==="slot")),s&&s.type===4&&!s.isStatic&&(i.arg=os(s,t))}}}};function os(e,t,n=!1,r=!1,i=Object.create(t.identifiers)){return e}function gv(e){return xe(e)?e:e.type===4?e.content:e.children.map(gv).join("")}const PO=Lu(/^(if|else|else-if)$/,(e,t,n)=>mv(e,t,n,(r,i,o)=>{const s=n.parent.children;let a=s.indexOf(r),l=0;for(;a-->=0;){const u=s[a];u&&u.type===9&&(l+=u.branches.length)}return()=>{if(o)r.codegenNode=qp(i,l,n);else{const u=AO(r.codegenNode);u.alternate=qp(i,l+r.branches.length-1,n)}}}));function mv(e,t,n,r){if(t.name!=="else"&&(!t.exp||!t.exp.content.trim())){const i=t.exp?t.exp.loc:e.loc;n.onError(Xe(28,t.loc)),t.exp=Ee("true",!1,i)}if(t.name==="if"){const i=Kp(e,t),o={type:9,loc:oO(e.loc),branches:[i]};if(n.replaceNode(o),r)return r(o,i,!0)}else{const i=n.parent.children;let o=i.indexOf(e);for(;o-->=-1;){const s=i[o];if(s&&s.type===3){n.removeNode(s);continue}if(s&&s.type===2&&!s.content.trim().length){n.removeNode(s);continue}if(s&&s.type===9){t.name==="else-if"&&s.branches[s.branches.length-1].condition===void 0&&n.onError(Xe(30,e.loc)),n.removeNode();const a=Kp(e,t);s.branches.push(a);const l=r&&r(s,a,!1);Co(a,n),l&&l(),n.currentNode=null}else n.onError(Xe(30,e.loc));break}}}function Kp(e,t){const n=e.tagType===3;return{type:10,loc:e.loc,condition:t.name==="else"?void 0:t.exp,children:n&&!At(e,"for")?e.children:[e],userKey:_o(e,"key"),isTemplateIf:n}}function qp(e,t,n){return e.condition?Ts(e.condition,zp(e,t,n),nt(n.helper(li),['""',"true"])):zp(e,t,n)}function zp(e,t,n){const{helper:r}=n,i=Qe("key",Ee(`${t}`,!1,at,2)),{children:o}=e,s=o[0];if(o.length!==1||s.type!==1)if(o.length===1&&s.type===11){const l=s.codegenNode;return eo(l,i,n),l}else return ei(n,r(Yr),Gt([i]),o,64,void 0,void 0,!0,!1,!1,e.loc);else{const l=s.codegenNode,u=Z0(l);return u.type===13&&Pa(u,n),eo(u,i,n),l}}function AO(e){for(;;)if(e.type===19)if(e.alternate.type===19)e=e.alternate;else return e;else e.type===20&&(e=e.value)}const vv=(e,t,n)=>{const{modifiers:r,loc:i}=e,o=e.arg;let{exp:s}=e;if(s&&s.type===4&&!s.content.trim()&&(s=void 0),!s){if(o.type!==4||!o.isStatic)return n.onError(Xe(52,o.loc)),{props:[Qe(o,Ee("",!0,i))]};yv(e),s=e.exp}return o.type!==4?(o.children.unshift("("),o.children.push(') || ""')):o.isStatic||(o.content=`${o.content} || ""`),r.some(a=>a.content==="camel")&&(o.type===4?o.isStatic?o.content=He(o.content):o.content=`${n.helperString(Ss)}(${o.content})`:(o.children.unshift(`${n.helperString(Ss)}(`),o.children.push(")"))),n.inSSR||(r.some(a=>a.content==="prop")&&Jp(o,"."),r.some(a=>a.content==="attr")&&Jp(o,"^")),{props:[Qe(o,s)]}},yv=(e,t)=>{const n=e.arg,r=He(n.content);e.exp=Ee(r,!1,n.loc)},Jp=(e,t)=>{e.type===4?e.isStatic?e.content=t+e.content:e.content=`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},IO=Lu("for",(e,t,n)=>{const{helper:r,removeHelper:i}=n;return bv(e,t,n,o=>{const s=nt(r(xa),[o.source]),a=ni(e),l=At(e,"memo"),u=_o(e,"key",!1,!0);u&&u.type===7&&!u.exp&&yv(u);let f=u&&(u.type===6?u.value?Ee(u.value.content,!0):void 0:u.exp);const p=u&&f?Qe("key",f):null,d=o.source.type===4&&o.source.constType>0,h=d?64:u?128:256;return o.codegenNode=ei(n,r(Yr),void 0,s,h,void 0,void 0,!0,!d,!1,e.loc),()=>{let x;const{children:j}=o,L=j.length!==1||j[0].type!==1,A=Zi(e)?e:a&&e.children.length===1&&Zi(e.children[0])?e.children[0]:null;if(A?(x=A.codegenNode,a&&p&&eo(x,p,n)):L?x=ei(n,r(Yr),p?Gt([p]):void 0,e.children,64,void 0,void 0,!0,void 0,!1):(x=j[0].codegenNode,a&&p&&eo(x,p,n),x.isBlock!==!d&&(x.isBlock?(i(Yn),i(Sr(n.inSSR,x.isComponent))):i(Er(n.inSSR,x.isComponent))),x.isBlock=!d,x.isBlock?(r(Yn),r(Sr(n.inSSR,x.isComponent))):r(Er(n.inSSR,x.isComponent))),l){const g=xr(Ps(o.parseResult,[Ee("_cached")]));g.body=V0([Yt(["const _memo = (",l.exp,")"]),Yt(["if (_cached",...f?[" && _cached.key === ",f]:[],` && ${n.helperString(Au)}(_cached, _memo)) return _cached`]),Yt(["const _item = ",x]),Ee("_item.memo = _memo"),Ee("return _item")]),s.arguments.push(g,Ee("_cache"),Ee(String(n.cached.length))),n.cached.push(null)}else s.arguments.push(xr(Ps(o.parseResult),x,!0))}})});function bv(e,t,n,r){if(!t.exp){n.onError(Xe(31,t.loc));return}const i=t.forParseResult;if(!i){n.onError(Xe(32,t.loc));return}Fu(i);const{addIdentifiers:o,removeIdentifiers:s,scopes:a}=n,{source:l,value:u,key:c,index:f}=i,p={type:11,loc:t.loc,source:l,valueAlias:u,keyAlias:c,objectIndexAlias:f,parseResult:i,children:ni(e)?e.children:[e]};n.replaceNode(p),a.vFor++;const d=r&&r(p);return()=>{a.vFor--,d&&d()}}function Fu(e,t){e.finalized||(e.finalized=!0)}function Ps({value:e,key:t,index:n},r=[]){return kO([e,t,n,...r])}function kO(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map((n,r)=>n||Ee("_".repeat(r+1),!1))}const Qp=Ee("undefined",!1),_v=(e,t)=>{if(e.type===1&&(e.tagType===1||e.tagType===3)){const n=At(e,"slot");if(n)return n.exp,t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},$O=(e,t)=>{let n;if(ni(e)&&e.props.some(Nu)&&(n=At(e,"for"))){const r=n.forParseResult;if(r){Fu(r);const{value:i,key:o,index:s}=r,{addIdentifiers:a,removeIdentifiers:l}=t;return i&&a(i),o&&a(o),s&&a(s),()=>{i&&l(i),o&&l(o),s&&l(s)}}}},NO=(e,t,n,r)=>xr(e,n,!1,!0,n.length?n[0].loc:r);function Cv(e,t,n=NO){t.helper(Ta);const{children:r,loc:i}=e,o=[],s=[];let a=t.scopes.vSlot>0||t.scopes.vFor>0;const l=At(e,"slot",!0);if(l){const{arg:j,exp:L}=l;j&&!It(j)&&(a=!0),o.push(Qe(j||Ee("default",!0),n(L,void 0,r,i)))}let u=!1,c=!1;const f=[],p=new Set;let d=0;for(let j=0;j<r.length;j++){const L=r[j];let A;if(!ni(L)||!(A=At(L,"slot",!0))){L.type!==3&&f.push(L);continue}if(l){t.onError(Xe(37,A.loc));break}u=!0;const{children:g,loc:C}=L,{arg:b=Ee("default",!0),exp:R,loc:T}=A;let O;It(b)?O=b?b.content:"default":a=!0;const y=At(L,"for"),S=n(R,y,g,C);let H,I;if(H=At(L,"if"))a=!0,s.push(Ts(H.exp,zo(b,S,d++),Qp));else if(I=At(L,/^else(-if)?$/,!0)){let M=j,z;for(;M--&&(z=r[M],z.type===3););if(z&&ni(z)&&At(z,/^(else-)?if$/)){let Z=s[s.length-1];for(;Z.alternate.type===19;)Z=Z.alternate;Z.alternate=I.exp?Ts(I.exp,zo(b,S,d++),Qp):zo(b,S,d++)}else t.onError(Xe(30,I.loc))}else if(y){a=!0;const M=y.forParseResult;M?(Fu(M),s.push(nt(t.helper(xa),[M.source,xr(Ps(M),zo(b,S),!0)]))):t.onError(Xe(32,y.loc))}else{if(O){if(p.has(O)){t.onError(Xe(38,T));continue}p.add(O),O==="default"&&(c=!0)}o.push(Qe(b,S))}}if(!l){const j=(L,A)=>{const g=n(L,void 0,A,i);return t.compatConfig&&(g.isNonScopedSlot=!0),Qe("default",g)};u?f.length&&f.some(L=>xv(L))&&(c?t.onError(Xe(39,f[0].loc)):o.push(j(void 0,f))):o.push(j(void 0,r))}const h=a?2:ss(e.children)?3:1;let x=Gt(o.concat(Qe("_",Ee(h+"",!1))),i);return s.length&&(x=nt(t.helper(Pu),[x,zn(s)])),{slots:x,hasDynamicSlots:a}}function zo(e,t,n){const r=[Qe("name",e),Qe("fn",t)];return n!=null&&r.push(Qe("key",Ee(String(n),!0))),Gt(r)}function ss(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(n.tagType===2||ss(n.children))return!0;break;case 9:if(ss(n.branches))return!0;break;case 10:case 11:if(ss(n.children))return!0;break}}return!1}function xv(e){return e.type!==2&&e.type!==12?!0:e.type===2?!!e.content.trim():xv(e.content)}const Ev=new WeakMap,Sv=(e,t)=>function(){if(e=t.currentNode,!(e.type===1&&(e.tagType===0||e.tagType===1)))return;const{tag:r,props:i}=e,o=e.tagType===1;let s=o?wv(e,t):`"${r}"`;const a=Le(s)&&s.callee===ya;let l,u,c=0,f,p,d,h=a||s===Hr||s===pa||!o&&(r==="svg"||r==="foreignObject"||r==="math");if(i.length>0){const x=ju(e,t,void 0,o,a);l=x.props,c=x.patchFlag,p=x.dynamicPropNames;const j=x.directives;d=j&&j.length?zn(j.map(L=>Tv(L,t))):void 0,x.shouldUseBlock&&(h=!0)}if(e.children.length>0)if(s===Ji&&(h=!0,c|=1024),o&&s!==Hr&&s!==Ji){const{slots:j,hasDynamicSlots:L}=Cv(e,t);u=j,L&&(c|=1024)}else if(e.children.length===1&&s!==Hr){const j=e.children[0],L=j.type,A=L===5||L===8;A&&Ft(j,t)===0&&(c|=1),A||L===2?u=j:u=e.children}else u=e.children;p&&p.length&&(f=RO(p)),e.codegenNode=ei(t,s,l,u,c===0?void 0:c,f,d,!!h,!1,o,e.loc)};function wv(e,t,n=!1){let{tag:r}=e;const i=Ol(r),o=_o(e,"is",!1,!0);if(o)if(i||vr("COMPILER_IS_ON_ELEMENT",t)){let a;if(o.type===6?a=o.value&&Ee(o.value.content,!0):(a=o.exp,a||(a=Ee("is",!1,o.arg.loc))),a)return nt(t.helper(ya),[a])}else o.type===6&&o.value.content.startsWith("vue:")&&(r=o.value.content.slice(4));const s=ku(r)||t.isBuiltInComponent(r);return s?(n||t.helper(s),s):(t.helper(va),t.components.add(r),ri(r,"component"))}function ju(e,t,n=e.props,r,i,o=!1){const{tag:s,loc:a,children:l}=e;let u=[];const c=[],f=[],p=l.length>0;let d=!1,h=0,x=!1,j=!1,L=!1,A=!1,g=!1,C=!1;const b=[],R=S=>{u.length&&(c.push(Gt(Yp(u),a)),u=[]),S&&c.push(S)},T=()=>{t.scopes.vFor>0&&u.push(Qe(Ee("ref_for",!0),Ee("true")))},O=({key:S,value:H})=>{if(It(S)){const I=S.content,M=Zn(I);if(M&&(!r||i)&&I.toLowerCase()!=="onclick"&&I!=="onUpdate:modelValue"&&!wn(I)&&(A=!0),M&&wn(I)&&(C=!0),M&&H.type===14&&(H=H.arguments[0]),H.type===20||(H.type===4||H.type===8)&&Ft(H,t)>0)return;I==="ref"?x=!0:I==="class"?j=!0:I==="style"?L=!0:I!=="key"&&!b.includes(I)&&b.push(I),r&&(I==="class"||I==="style")&&!b.includes(I)&&b.push(I)}else g=!0};for(let S=0;S<n.length;S++){const H=n[S];if(H.type===6){const{loc:I,name:M,nameLoc:z,value:Z}=H;let J=!0;if(M==="ref"&&(x=!0,T()),M==="is"&&(Ol(s)||Z&&Z.content.startsWith("vue:")||vr("COMPILER_IS_ON_ELEMENT",t)))continue;u.push(Qe(Ee(M,!0,z),Ee(Z?Z.content:"",J,Z?Z.loc:I)))}else{const{name:I,arg:M,exp:z,loc:Z,modifiers:J}=H,P=I==="bind",W=I==="on";if(I==="slot"){r||t.onError(Xe(40,Z));continue}if(I==="once"||I==="memo"||I==="is"||P&&Wn(M,"is")&&(Ol(s)||vr("COMPILER_IS_ON_ELEMENT",t))||W&&o)continue;if((P&&Wn(M,"key")||W&&p&&Wn(M,"vue:before-update"))&&(d=!0),P&&Wn(M,"ref")&&T(),!M&&(P||W)){if(g=!0,z)if(P){if(T(),R(),vr("COMPILER_V_BIND_OBJECT_ORDER",t)){c.unshift(z);continue}c.push(z)}else R({type:14,loc:Z,callee:t.helper(wa),arguments:r?[z]:[z,"true"]});else t.onError(Xe(P?34:35,Z));continue}P&&J.some(V=>V.content==="prop")&&(h|=32);const D=t.directiveTransforms[I];if(D){const{props:V,needRuntime:X}=D(H,e,t);!o&&V.forEach(O),W&&M&&!It(M)?R(Gt(V,a)):u.push(...V),X&&(f.push(H),kt(X)&&Ev.set(H,X))}else ah(I)||(f.push(H),p&&(d=!0))}}let y;if(c.length?(R(),c.length>1?y=nt(t.helper(Qi),c,a):y=c[0]):u.length&&(y=Gt(Yp(u),a)),g?h|=16:(j&&!r&&(h|=2),L&&!r&&(h|=4),b.length&&(h|=8),A&&(h|=32)),!d&&(h===0||h===32)&&(x||C||f.length>0)&&(h|=512),!t.inSSR&&y)switch(y.type){case 15:let S=-1,H=-1,I=!1;for(let Z=0;Z<y.properties.length;Z++){const J=y.properties[Z].key;It(J)?J.content==="class"?S=Z:J.content==="style"&&(H=Z):J.isHandlerKey||(I=!0)}const M=y.properties[S],z=y.properties[H];I?y=nt(t.helper(Zr),[y]):(M&&!It(M.value)&&(M.value=nt(t.helper(Ea),[M.value])),z&&(L||z.value.type===4&&z.value.content.trim()[0]==="["||z.value.type===17)&&(z.value=nt(t.helper(Sa),[z.value])));break;case 14:break;default:y=nt(t.helper(Zr),[nt(t.helper(ui),[y])]);break}return{props:y,directives:f,patchFlag:h,dynamicPropNames:b,shouldUseBlock:d}}function Yp(e){const t=new Map,n=[];for(let r=0;r<e.length;r++){const i=e[r];if(i.key.type===8||!i.key.isStatic){n.push(i);continue}const o=i.key.content,s=t.get(o);s?(o==="style"||o==="class"||Zn(o))&&MO(s,i):(t.set(o,i),n.push(i))}return n}function MO(e,t){e.value.type===17?e.value.elements.push(t.value):e.value=zn([e.value,t.value],e.loc)}function Tv(e,t){const n=[],r=Ev.get(e);r?n.push(t.helperString(r)):(t.helper(ba),t.directives.add(e.name),n.push(ri(e.name,"directive")));const{loc:i}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const o=Ee("true",!1,i);n.push(Gt(e.modifiers.map(s=>Qe(s,o)),i))}return zn(n,e.loc)}function RO(e){let t="[";for(let n=0,r=e.length;n<r;n++)t+=JSON.stringify(e[n]),n<r-1&&(t+=", ");return t+"]"}function Ol(e){return e==="component"||e==="Component"}const LO=(e,t)=>{if(Zi(e)){const{children:n,loc:r}=e,{slotName:i,slotProps:o}=Ov(e,t),s=[t.prefixIdentifiers?"_ctx.$slots":"$slots",i,"{}","undefined","true"];let a=2;o&&(s[2]=o,a=3),n.length&&(s[3]=xr([],n,!1,!1,r),a=4),t.scopeId&&!t.slotted&&(a=5),s.splice(a),e.codegenNode=nt(t.helper(Ou),s,r)}};function Ov(e,t){let n='"default"',r;const i=[];for(let o=0;o<e.props.length;o++){const s=e.props[o];if(s.type===6)s.value&&(s.name==="name"?n=JSON.stringify(s.value.content):(s.name=He(s.name),i.push(s)));else if(s.name==="bind"&&Wn(s.arg,"name")){if(s.exp)n=s.exp;else if(s.arg&&s.arg.type===4){const a=He(s.arg.content);n=s.exp=Ee(a,!1,s.arg.loc)}}else s.name==="bind"&&s.arg&&It(s.arg)&&(s.arg.content=He(s.arg.content)),i.push(s)}if(i.length>0){const{props:o,directives:s}=ju(e,t,i,!1,!1);r=o,s.length&&t.onError(Xe(36,s[0].loc))}return{slotName:n,slotProps:r}}const Bu=(e,t,n,r)=>{const{loc:i,modifiers:o,arg:s}=e;!e.exp&&!o.length&&n.onError(Xe(35,i));let a;if(s.type===4)if(s.isStatic){let f=s.content;f.startsWith("vue:")&&(f=`vnode-${f.slice(4)}`);const p=t.tagType!==0||f.startsWith("vnode")||!/[A-Z]/.test(f)?Xn(He(f)):`on:${f}`;a=Ee(p,!0,s.loc)}else a=Yt([`${n.helperString(ws)}(`,s,")"]);else a=s,a.children.unshift(`${n.helperString(ws)}(`),a.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let u=n.cacheHandlers&&!l&&!n.inVOnce;if(l){const f=$u(l),p=!(f||z0(l)),d=l.content.includes(";");(p||u&&f)&&(l=Yt([`${p?"$event":"(...args)"} => ${d?"{":"("}`,l,d?"}":")"]))}let c={props:[Qe(a,l||Ee("() => {}",!1,i))]};return r&&(c=r(c)),u&&(c.props[0].value=n.cache(c.props[0].value)),c.props.forEach(f=>f.key.isHandlerKey=!0),c},DO=(e,t)=>{if(e.type===0||e.type===1||e.type===11||e.type===10)return()=>{const n=e.children;let r,i=!1;for(let o=0;o<n.length;o++){const s=n[o];if(ts(s)){i=!0;for(let a=o+1;a<n.length;a++){const l=n[a];if(ts(l))r||(r=n[o]=Yt([s],s.loc)),r.children.push(" + ",l),n.splice(a,1),a--;else{r=void 0;break}}}}if(!(!i||n.length===1&&(e.type===0||e.type===1&&e.tagType===0&&!e.props.find(o=>o.type===7&&!t.directiveTransforms[o.name])&&e.tag!=="template")))for(let o=0;o<n.length;o++){const s=n[o];if(ts(s)||s.type===8){const a=[];(s.type!==2||s.content!==" ")&&a.push(s),!t.ssr&&Ft(s,t)===0&&a.push("1"),n[o]={type:12,content:s,loc:s.loc,codegenNode:nt(t.helper(ma),a)}}}}},Zp=new WeakSet,FO=(e,t)=>{if(e.type===1&&At(e,"once",!0))return Zp.has(e)||t.inVOnce||t.inSSR?void 0:(Zp.add(e),t.inVOnce=!0,t.helper(Yi),()=>{t.inVOnce=!1;const n=t.currentNode;n.codegenNode&&(n.codegenNode=t.cache(n.codegenNode,!0,!0))})},Vu=(e,t,n)=>{const{exp:r,arg:i}=e;if(!r)return n.onError(Xe(41,e.loc)),Jo();const o=r.loc.source.trim(),s=r.type===4?r.content:o,a=n.bindingMetadata[o];if(a==="props"||a==="props-aliased")return n.onError(Xe(44,r.loc)),Jo();if(!s.trim()||!$u(r))return n.onError(Xe(42,r.loc)),Jo();const l=i||Ee("modelValue",!0),u=i?It(i)?`onUpdate:${He(i.content)}`:Yt(['"onUpdate:" + ',i]):"onUpdate:modelValue";let c;const f=n.isTS?"($event: any)":"$event";c=Yt([`${f} => ((`,r,") = $event)"]);const p=[Qe(l,e.exp),Qe(u,c)];if(e.modifiers.length&&t.tagType===1){const d=e.modifiers.map(x=>x.content).map(x=>(bo(x)?x:JSON.stringify(x))+": true").join(", "),h=i?It(i)?`${i.content}Modifiers`:Yt([i,' + "Modifiers"']):"modelModifiers";p.push(Qe(h,Ee(`{ ${d} }`,!1,e.loc,2)))}return Jo(p)};function Jo(e=[]){return{props:e}}const jO=/[\w).+\-_$\]]/,BO=(e,t)=>{vr("COMPILER_FILTERS",t)&&(e.type===5?As(e.content,t):e.type===1&&e.props.forEach(n=>{n.type===7&&n.name!=="for"&&n.exp&&As(n.exp,t)}))};function As(e,t){if(e.type===4)eh(e,t);else for(let n=0;n<e.children.length;n++){const r=e.children[n];typeof r=="object"&&(r.type===4?eh(r,t):r.type===8?As(e,t):r.type===5&&As(r.content,t))}}function eh(e,t){const n=e.content;let r=!1,i=!1,o=!1,s=!1,a=0,l=0,u=0,c=0,f,p,d,h,x=[];for(d=0;d<n.length;d++)if(p=f,f=n.charCodeAt(d),r)f===39&&p!==92&&(r=!1);else if(i)f===34&&p!==92&&(i=!1);else if(o)f===96&&p!==92&&(o=!1);else if(s)f===47&&p!==92&&(s=!1);else if(f===124&&n.charCodeAt(d+1)!==124&&n.charCodeAt(d-1)!==124&&!a&&!l&&!u)h===void 0?(c=d+1,h=n.slice(0,d).trim()):j();else{switch(f){case 34:i=!0;break;case 39:r=!0;break;case 96:o=!0;break;case 40:u++;break;case 41:u--;break;case 91:l++;break;case 93:l--;break;case 123:a++;break;case 125:a--;break}if(f===47){let L=d-1,A;for(;L>=0&&(A=n.charAt(L),A===" ");L--);(!A||!jO.test(A))&&(s=!0)}}h===void 0?h=n.slice(0,d).trim():c!==0&&j();function j(){x.push(n.slice(c,d).trim()),c=d+1}if(x.length){for(d=0;d<x.length;d++)h=VO(h,x[d],t);e.content=h,e.ast=void 0}}function VO(e,t,n){n.helper(_a);const r=t.indexOf("(");if(r<0)return n.filters.add(t),`${ri(t,"filter")}(${e})`;{const i=t.slice(0,r),o=t.slice(r+1);return n.filters.add(i),`${ri(i,"filter")}(${e}${o!==")"?","+o:o}`}}const th=new WeakSet,HO=(e,t)=>{if(e.type===1){const n=At(e,"memo");return!n||th.has(e)?void 0:(th.add(e),()=>{const r=e.codegenNode||t.currentNode.codegenNode;r&&r.type===13&&(e.tagType!==1&&Pa(r,t),e.codegenNode=nt(t.helper(Oa),[n.exp,xr(void 0,r),"_cache",String(t.cached.length)]),t.cached.push(null))})}};function Pv(e){return[[FO,PO,HO,IO,BO,LO,Sv,_v,DO],{on:Bu,bind:vv,model:Vu}]}function Av(e,t={}){const n=t.onError||Iu,r=t.mode==="module";t.prefixIdentifiers===!0?n(Xe(47)):r&&n(Xe(48));const i=!1;t.cacheHandlers&&n(Xe(49)),t.scopeId&&!r&&n(Xe(50));const o=Pe({},t,{prefixIdentifiers:i}),s=xe(e)?Ru(e,o):e,[a,l]=Pv();return cv(s,Pe({},o,{nodeTransforms:[...a,...t.nodeTransforms||[]],directiveTransforms:Pe({},l,t.directiveTransforms||{})})),dv(s,o)}const GO={DATA:"data",PROPS:"props",PROPS_ALIASED:"props-aliased",SETUP_LET:"setup-let",SETUP_CONST:"setup-const",SETUP_REACTIVE_CONST:"setup-reactive-const",SETUP_MAYBE_REF:"setup-maybe-ref",SETUP_REF:"setup-ref",OPTIONS:"options",LITERAL_CONST:"literal-const"},Iv=()=>({props:[]});/**
* @vue/compiler-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Hu=Symbol(""),Gu=Symbol(""),Uu=Symbol(""),Wu=Symbol(""),Is=Symbol(""),Xu=Symbol(""),Ku=Symbol(""),qu=Symbol(""),zu=Symbol(""),Ju=Symbol("");F0({[Hu]:"vModelRadio",[Gu]:"vModelCheckbox",[Uu]:"vModelText",[Wu]:"vModelSelect",[Is]:"vModelDynamic",[Xu]:"withModifiers",[Ku]:"withKeys",[qu]:"vShow",[zu]:"Transition",[Ju]:"TransitionGroup"});let $r;function UO(e,t=!1){return $r||($r=document.createElement("div")),t?($r.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,$r.children[0].getAttribute("foo")):($r.innerHTML=e,$r.textContent)}const Qu={parseMode:"html",isVoidTag:dh,isNativeTag:e=>uh(e)||ch(e)||fh(e),isPreTag:e=>e==="pre",isIgnoreNewlineTag:e=>e==="pre"||e==="textarea",decodeEntities:UO,isBuiltInComponent:e=>{if(e==="Transition"||e==="transition")return zu;if(e==="TransitionGroup"||e==="transition-group")return Ju},getNamespace(e,t,n){let r=t?t.ns:n;if(t&&r===2)if(t.tag==="annotation-xml"){if(e==="svg")return 1;t.props.some(i=>i.type===6&&i.name==="encoding"&&i.value!=null&&(i.value.content==="text/html"||i.value.content==="application/xhtml+xml"))&&(r=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&e!=="mglyph"&&e!=="malignmark"&&(r=0);else t&&r===1&&(t.tag==="foreignObject"||t.tag==="desc"||t.tag==="title")&&(r=0);if(r===0){if(e==="svg")return 1;if(e==="math")return 2}return r}},kv=e=>{e.type===1&&e.props.forEach((t,n)=>{t.type===6&&t.name==="style"&&t.value&&(e.props[n]={type:7,name:"bind",arg:Ee("style",!0,t.loc),exp:WO(t.value.content,t.loc),modifiers:[],loc:t.loc})})},WO=(e,t)=>{const n=$l(e);return Ee(JSON.stringify(n),!1,t,3)};function An(e,t){return Xe(e,t)}const XO={X_V_HTML_NO_EXPRESSION:53,53:"X_V_HTML_NO_EXPRESSION",X_V_HTML_WITH_CHILDREN:54,54:"X_V_HTML_WITH_CHILDREN",X_V_TEXT_NO_EXPRESSION:55,55:"X_V_TEXT_NO_EXPRESSION",X_V_TEXT_WITH_CHILDREN:56,56:"X_V_TEXT_WITH_CHILDREN",X_V_MODEL_ON_INVALID_ELEMENT:57,57:"X_V_MODEL_ON_INVALID_ELEMENT",X_V_MODEL_ARG_ON_ELEMENT:58,58:"X_V_MODEL_ARG_ON_ELEMENT",X_V_MODEL_ON_FILE_INPUT_ELEMENT:59,59:"X_V_MODEL_ON_FILE_INPUT_ELEMENT",X_V_MODEL_UNNECESSARY_VALUE:60,60:"X_V_MODEL_UNNECESSARY_VALUE",X_V_SHOW_NO_EXPRESSION:61,61:"X_V_SHOW_NO_EXPRESSION",X_TRANSITION_INVALID_CHILDREN:62,62:"X_TRANSITION_INVALID_CHILDREN",X_IGNORED_SIDE_EFFECT_TAG:63,63:"X_IGNORED_SIDE_EFFECT_TAG",__EXTEND_POINT__:64,64:"__EXTEND_POINT__"},KO={53:"v-html is missing expression.",54:"v-html will override element children.",55:"v-text is missing expression.",56:"v-text will override element children.",57:"v-model can only be used on <input>, <textarea> and <select> elements.",58:"v-model argument is not supported on plain elements.",59:"v-model cannot be used on file inputs since they are read-only. Use a v-on:change listener instead.",60:"Unnecessary value binding used alongside v-model. It will interfere with v-model's behavior.",61:"v-show is missing expression.",62:"<Transition> expects exactly one child element or component.",63:"Tags with side effect (<script> and <style>) are ignored in client component templates."},qO=(e,t,n)=>{const{exp:r,loc:i}=e;return r||n.onError(An(53,i)),t.children.length&&(n.onError(An(54,i)),t.children.length=0),{props:[Qe(Ee("innerHTML",!0,i),r||Ee("",!0))]}},zO=(e,t,n)=>{const{exp:r,loc:i}=e;return r||n.onError(An(55,i)),t.children.length&&(n.onError(An(56,i)),t.children.length=0),{props:[Qe(Ee("textContent",!0),r?Ft(r,n)>0?r:nt(n.helperString(yo),[r],i):Ee("",!0))]}},JO=(e,t,n)=>{const r=Vu(e,t,n);if(!r.props.length||t.tagType===1)return r;e.arg&&n.onError(An(58,e.arg.loc));const{tag:i}=t,o=n.isCustomElement(i);if(i==="input"||i==="textarea"||i==="select"||o){let s=Uu,a=!1;if(i==="input"||o){const l=_o(t,"type");if(l){if(l.type===7)s=Is;else if(l.value)switch(l.value.content){case"radio":s=Hu;break;case"checkbox":s=Gu;break;case"file":a=!0,n.onError(An(59,e.loc));break}}else Q0(t)&&(s=Is)}else i==="select"&&(s=Wu);a||(r.needRuntime=n.helper(s))}else n.onError(An(57,e.loc));return r.props=r.props.filter(s=>!(s.key.type===4&&s.key.content==="modelValue")),r},QO=dt("passive,once,capture"),YO=dt("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),ZO=dt("left,right"),$v=dt("onkeyup,onkeydown,onkeypress"),eP=(e,t,n,r)=>{const i=[],o=[],s=[];for(let a=0;a<t.length;a++){const l=t[a].content;l==="native"&&ti("COMPILER_V_ON_NATIVE",n)||QO(l)?s.push(l):ZO(l)?It(e)?$v(e.content.toLowerCase())?i.push(l):o.push(l):(i.push(l),o.push(l)):YO(l)?o.push(l):i.push(l)}return{keyModifiers:i,nonKeyModifiers:o,eventOptionModifiers:s}},nh=(e,t)=>It(e)&&e.content.toLowerCase()==="onclick"?Ee(t,!0):e.type!==4?Yt(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,tP=(e,t,n)=>Bu(e,t,n,r=>{const{modifiers:i}=e;if(!i.length)return r;let{key:o,value:s}=r.props[0];const{keyModifiers:a,nonKeyModifiers:l,eventOptionModifiers:u}=eP(o,i,n,e.loc);if(l.includes("right")&&(o=nh(o,"onContextmenu")),l.includes("middle")&&(o=nh(o,"onMouseup")),l.length&&(s=nt(n.helper(Xu),[s,JSON.stringify(l)])),a.length&&(!It(o)||$v(o.content.toLowerCase()))&&(s=nt(n.helper(Ku),[s,JSON.stringify(a)])),u.length){const c=u.map($n).join("");o=It(o)?Ee(`${o.content}${c}`,!0):Yt(["(",o,`) + "${c}"`])}return{props:[Qe(o,s)]}}),nP=(e,t,n)=>{const{exp:r,loc:i}=e;return r||n.onError(An(61,i)),{props:[],needRuntime:n.helper(qu)}},rP=(e,t)=>{e.type===1&&e.tagType===0&&(e.tag==="script"||e.tag==="style")&&t.removeNode()},Nv=[kv],Mv={cloak:Iv,html:qO,text:zO,model:JO,on:tP,show:nP};function iP(e,t={}){return Av(e,Pe({},Qu,t,{nodeTransforms:[rP,...Nv,...t.nodeTransforms||[]],directiveTransforms:Pe({},Mv,t.directiveTransforms||{}),transformHoist:null}))}function oP(e,t={}){return Ru(e,Pe({},Qu,t))}const sP=Object.freeze(Object.defineProperty({__proto__:null,BASE_TRANSITION:Eu,BindingTypes:GO,CAMELIZE:Ss,CAPITALIZE:N0,CREATE_BLOCK:Su,CREATE_COMMENT:li,CREATE_ELEMENT_BLOCK:wu,CREATE_ELEMENT_VNODE:ga,CREATE_SLOTS:Pu,CREATE_STATIC:Tu,CREATE_TEXT:ma,CREATE_VNODE:ha,CompilerDeprecationTypes:wT,ConstantTypes:vT,DOMDirectiveTransforms:Mv,DOMErrorCodes:XO,DOMErrorMessages:KO,DOMNodeTransforms:Nv,ElementTypes:mT,ErrorCodes:PT,FRAGMENT:Yr,GUARD_REACTIVE_PROPS:ui,IS_MEMO_SAME:Au,IS_REF:D0,KEEP_ALIVE:Ji,MERGE_PROPS:Qi,NORMALIZE_CLASS:Ea,NORMALIZE_PROPS:Zr,NORMALIZE_STYLE:Sa,Namespaces:hT,NodeTypes:gT,OPEN_BLOCK:Yn,POP_SCOPE_ID:R0,PUSH_SCOPE_ID:M0,RENDER_LIST:xa,RENDER_SLOT:Ou,RESOLVE_COMPONENT:va,RESOLVE_DIRECTIVE:ba,RESOLVE_DYNAMIC_COMPONENT:ya,RESOLVE_FILTER:_a,SET_BLOCK_TRACKING:Yi,SUSPENSE:pa,TELEPORT:Hr,TO_DISPLAY_STRING:yo,TO_HANDLERS:wa,TO_HANDLER_KEY:ws,TRANSITION:zu,TRANSITION_GROUP:Ju,TS_NODE_TYPES:U0,UNREF:L0,V_MODEL_CHECKBOX:Gu,V_MODEL_DYNAMIC:Is,V_MODEL_RADIO:Hu,V_MODEL_SELECT:Wu,V_MODEL_TEXT:Uu,V_ON_WITH_KEYS:Ku,V_ON_WITH_MODIFIERS:Xu,V_SHOW:qu,WITH_CTX:Ta,WITH_DIRECTIVES:Ca,WITH_MEMO:Oa,advancePositionWithClone:KT,advancePositionWithMutation:J0,assert:qT,baseCompile:Av,baseParse:Ru,buildDirectiveArgs:Tv,buildProps:ju,buildSlots:Cv,checkCompatEnabled:ti,compile:iP,convertToBlock:Pa,createArrayExpression:zn,createAssignmentExpression:CT,createBlockStatement:V0,createCacheExpression:B0,createCallExpression:nt,createCompilerError:Xe,createCompoundExpression:Yt,createConditionalExpression:Ts,createDOMCompilerError:An,createForLoopParams:Ps,createFunctionExpression:xr,createIfStatement:_T,createInterpolation:yT,createObjectExpression:Gt,createObjectProperty:Qe,createReturnStatement:ET,createRoot:j0,createSequenceExpression:xT,createSimpleExpression:Ee,createStructuralDirectiveTransform:Lu,createTemplateLiteral:bT,createTransformContext:uv,createVNodeCall:ei,errorMessages:AT,extractIdentifiers:Sn,findDir:At,findProp:_o,forAliasRE:ev,generate:dv,generateCodeFrame:lh,getBaseTransformPreset:Pv,getConstantType:Ft,getMemoedVNodeCall:Z0,getVNodeBlockHelper:Sr,getVNodeHelper:Er,hasDynamicKeyVBind:Q0,hasScopeRef:Zt,helperNameMap:Cr,injectProp:eo,isCoreComponent:ku,isFnExpression:z0,isFnExpressionBrowser:q0,isFnExpressionNode:XT,isFunctionType:FT,isInDestructureAssignment:$T,isInNewExpression:NT,isMemberExpression:$u,isMemberExpressionBrowser:K0,isMemberExpressionNode:UT,isReferencedIdentifier:kT,isSimpleIdentifier:bo,isSlotOutlet:Zi,isStaticArgOf:Wn,isStaticExp:It,isStaticProperty:G0,isStaticPropertyKey:jT,isTemplateNode:ni,isText:ts,isVSlot:Nu,locStub:at,noopDirectiveTransform:Iv,parse:oP,parserOptions:Qu,processExpression:os,processFor:bv,processIf:mv,processSlotOutlet:Ov,registerRuntimeHelpers:F0,resolveComponentType:wv,stringifyExpression:gv,toValidAssetId:ri,trackSlotScopes:_v,trackVForSlotScopes:$O,transform:cv,transformBind:vv,transformElement:Sv,transformExpression:OO,transformModel:Vu,transformOn:Bu,transformStyle:kv,traverseNode:Co,unwrapTSNode:W0,walkBlockDeclarations:RT,walkFunctionParams:MT,walkIdentifiers:IT,warnDeprecation:OT},Symbol.toStringTag,{value:"Module"})),aP=Pl(sP),lP=Pl(_1),uP=Pl(my);/**
* vue v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/var rh;function cP(){return rh||(rh=1,function(e){Object.defineProperty(e,"__esModule",{value:!0});var t=aP,n=lP,r=uP;function i(l){var u=Object.create(null);if(l)for(var c in l)u[c]=l[c];return u.default=l,Object.freeze(u)}var o=i(n);const s=Object.create(null);function a(l,u){if(!r.isString(l))if(l.nodeType)l=l.innerHTML;else return r.NOOP;const c=r.genCacheKey(l,u),f=s[c];if(f)return f;if(l[0]==="#"){const x=document.querySelector(l);l=x?x.innerHTML:""}const p=r.extend({hoistStatic:!0,onError:void 0,onWarn:r.NOOP},u);!p.isCustomElement&&typeof customElements<"u"&&(p.isCustomElement=x=>!!customElements.get(x));const{code:d}=t.compile(l,p),h=new Function("Vue",d)(o);return h._rc=!0,s[c]=h}n.registerRuntimeCompiler(a),e.compile=a,Object.keys(n).forEach(function(l){l!=="default"&&!Object.prototype.hasOwnProperty.call(e,l)&&(e[l]=n[l])})}(za)),za}var ih;function kP(){return ih||(ih=1,qa.exports=cP()),qa.exports}export{Js as $,ae as A,ze as B,Ve as C,tr as D,on as E,lu as F,kg as G,Wr as H,Ks as I,tg as J,Re as K,ql as L,na as M,ft as N,ct as O,Se as P,ee as Q,fu as R,vP as S,km as T,$t as U,Mn as V,$g as W,H_ as X,Um as Y,oi as Z,ke as _,ye as a,co as a0,nn as a1,On as a2,et as a3,fn as a4,la as a5,og as a6,Hl as a7,CP as a8,ii as a9,yh as aA,k_ as aB,no as aC,Cg as aD,Xn as aE,TP as aF,mu as aG,SP as aH,PP as aI,OP as aJ,xP as aK,_s as aL,xt as aM,Vs as aN,mP as aO,__ as aP,m_ as aQ,hu as aR,si as aS,gP as aT,hP as aU,IP as aV,$0 as aW,pP as aX,Qs as aa,Wm as ab,xg as ac,qe as ad,bP as ae,Ya as af,Nl as ag,su as ah,pn as ai,Ae as aj,ra as ak,Bh as al,ia as am,ai as an,bg as ao,$n as ap,Zs as aq,Ns as ar,Wi as as,EP as at,Eg as au,Ds as av,_P as aw,yP as ax,Vm as ay,Gs as az,xe as b,Ce as c,ve as d,Le as e,aa as f,gt as g,rt as h,zt as i,$e as j,_e as k,He as l,Ne as m,AP as n,wP as o,jr as p,we as q,kP as r,ao as s,Ue as t,v as u,Qt as v,_m as w,iu as x,de as y,fe as z};
