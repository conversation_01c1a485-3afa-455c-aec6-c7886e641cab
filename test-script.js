// 测试脚本：验证项目搜索和VTable滚动修复
console.log('🧪 开始测试修复效果...');

// 测试1：项目搜索功能
function testProjectSearch() {
    console.log('\n📋 测试1：项目搜索功能');
    
    // 检查页面是否有项目选择器
    const projectSelect = document.querySelector('.project-select');
    if (projectSelect) {
        console.log('✅ 项目选择器存在');
        
        // 检查是否有项目选项
        const options = document.querySelectorAll('.el-select-dropdown__item');
        if (options.length > 0) {
            console.log(`✅ 找到 ${options.length} 个项目选项`);
        } else {
            console.log('⚠️ 未找到项目选项，可能需要点击下拉框');
        }
    } else {
        console.log('❌ 未找到项目选择器');
    }
}

// 测试2：VTable滚动功能
function testVTableScroll() {
    console.log('\n📊 测试2：VTable滚动功能');
    
    // 查找VTable容器
    const vtableContainers = document.querySelectorAll('.table-container');
    console.log(`找到 ${vtableContainers.length} 个表格容器`);
    
    vtableContainers.forEach((container, index) => {
        console.log(`\n检查表格容器 ${index + 1}:`);
        
        // 检查是否有滚动条
        const hasHorizontalScroll = container.scrollWidth > container.clientWidth;
        const hasVerticalScroll = container.scrollHeight > container.clientHeight;
        
        console.log(`  - 水平滚动: ${hasHorizontalScroll ? '✅ 需要' : '⚪ 不需要'}`);
        console.log(`  - 垂直滚动: ${hasVerticalScroll ? '✅ 需要' : '⚪ 不需要'}`);
        console.log(`  - 容器尺寸: ${container.clientWidth}x${container.clientHeight}`);
        console.log(`  - 内容尺寸: ${container.scrollWidth}x${container.scrollHeight}`);
        
        // 检查VTable实例
        const vtableElement = container.querySelector('canvas');
        if (vtableElement) {
            console.log('  - ✅ VTable canvas元素存在');
        } else {
            console.log('  - ❌ VTable canvas元素不存在');
        }
    });
}

// 测试3：控制台日志检查
function testConsoleOutput() {
    console.log('\n📝 测试3：控制台日志检查');
    
    // 检查是否有相关的日志输出
    const originalLog = console.log;
    const logs = [];
    
    console.log = function(...args) {
        logs.push(args.join(' '));
        originalLog.apply(console, args);
    };
    
    // 恢复原始console.log
    setTimeout(() => {
        console.log = originalLog;
        
        const relevantLogs = logs.filter(log => 
            log.includes('搜索项目') || 
            log.includes('VTable') || 
            log.includes('项目列表') ||
            log.includes('滚动')
        );
        
        if (relevantLogs.length > 0) {
            console.log('✅ 找到相关日志输出:');
            relevantLogs.forEach(log => console.log(`  - ${log}`));
        } else {
            console.log('⚠️ 未找到相关日志输出');
        }
    }, 2000);
}

// 测试4：功能交互测试
function testInteractions() {
    console.log('\n🖱️ 测试4：功能交互测试');
    
    // 模拟点击项目选择器
    const projectSelect = document.querySelector('.el-select');
    if (projectSelect) {
        console.log('尝试触发项目选择器...');
        projectSelect.click();
        
        setTimeout(() => {
            const dropdown = document.querySelector('.el-select-dropdown');
            if (dropdown && dropdown.style.display !== 'none') {
                console.log('✅ 项目下拉框已打开');
                
                // 检查搜索输入框
                const searchInput = dropdown.querySelector('input');
                if (searchInput) {
                    console.log('✅ 搜索输入框存在');
                    
                    // 模拟输入搜索关键词
                    searchInput.value = '智慧';
                    searchInput.dispatchEvent(new Event('input'));
                    console.log('✅ 已输入搜索关键词');
                } else {
                    console.log('❌ 搜索输入框不存在');
                }
            } else {
                console.log('❌ 项目下拉框未打开');
            }
        }, 500);
    } else {
        console.log('❌ 未找到项目选择器');
    }
}

// 主测试函数
function runTests() {
    console.log('🚀 开始执行修复验证测试...\n');
    
    // 等待页面完全加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runAllTests, 1000);
        });
    } else {
        setTimeout(runAllTests, 1000);
    }
}

function runAllTests() {
    testProjectSearch();
    testVTableScroll();
    testConsoleOutput();
    testInteractions();
    
    console.log('\n✨ 测试完成！请检查上述输出结果。');
    console.log('\n📋 手动测试建议：');
    console.log('1. 尝试在项目选择框中输入关键词搜索');
    console.log('2. 选择一个项目后，切换到不同的明细表格标签');
    console.log('3. 在表格中尝试水平和垂直滚动');
    console.log('4. 检查滚动条是否能滚动到最右侧和最底部');
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    // 添加到全局作用域，方便在控制台调用
    window.testFixes = runTests;
    
    // 自动运行测试
    runTests();
} else {
    console.log('此脚本需要在浏览器环境中运行');
}

// 导出测试函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testProjectSearch,
        testVTableScroll,
        testConsoleOutput,
        testInteractions,
        runTests
    };
}
