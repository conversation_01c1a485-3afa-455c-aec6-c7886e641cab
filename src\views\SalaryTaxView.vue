<template>
  <div class="salary-tax-view">
    <!-- First row: Action buttons -->
    <div class="action-buttons">
      <button @click="importSalaryInfo" class="action-button import">导入薪酬信息</button>
      <button @click="importSpecialDeduction" class="action-button import">导入专项附加扣除表</button>
      <button @click="matchIdAndProject" class="action-button match">匹配身份证号及项目</button>
      <button @click="calculateTax" class="action-button tax">计算个税</button>
      <button @click="generateDeclaration" class="action-button declaration">生成申报表</button>
      <button @click="pushDeclaration" class="action-button push">推送申报表</button>
      <button @click="buildFinanceTemplate" class="action-button template">构建财务一体化计提发放模板</button>
      <button @click="generateStandardPayment" class="action-button payment">按模版生成标准支付表</button>
    </div>

    <!-- Second row: Tab navigation -->
    <div class="tabs-container">
      <div class="tabs">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          :class="['tab', { active: activeTabIndex === index }]"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </div>
      </div>
    </div>
    
    <!-- Third row: Search button -->
    <div class="search-container">
      <button @click="toggleSearchPanel" class="search-button">
        <span>查询</span>
        <span class="search-icon">{{ searchPanelVisible ? '▲' : '▼' }}</span>
      </button>
      
      <!-- Search panel (hidden by default) -->
      <div v-if="searchPanelVisible" class="search-panel">
        <div class="search-form">
          <div class="form-group">
            <label>日期范围:</label>
            <input type="date" v-model="searchParams.startDate">
            <span>至</span>
            <input type="date" v-model="searchParams.endDate">
          </div>
          <div class="form-group">
            <label>关键字:</label>
            <input type="text" v-model="searchParams.keyword" placeholder="请输入关键字">
          </div>
          <div class="form-buttons">
            <button @click="search" class="search-action">搜索</button>
            <button @click="resetSearch" class="search-action reset">重置</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- VTable container -->
    <div class="vtable-container">
      <VTableComponent
        v-if="activeTabIndex === 0"
        :data="taxDeclarationData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
      />
      <VTableComponent
        v-else-if="activeTabIndex === 1"
        :data="taxCalculationData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
      />
      <VTableComponent
        v-else-if="activeTabIndex === 2"
        :data="specialDeductionData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
      />
      <VTableComponent
        v-else-if="activeTabIndex === 3"
        :data="remoteTaxData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
      />
      <VTableComponent
        v-else-if="activeTabIndex === 4"
        :data="idCardData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
      />
      <VTableComponent
        v-else-if="activeTabIndex === 5"
        :data="projectMappingData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import VTableComponent from '@/components/VTableComponent.vue';

// Tab definitions
const tabs = [
  { name: '个税申报表', key: 'taxDeclaration' },
  { name: '算税底稿', key: 'taxCalculation' },
  { name: '累计专项附加扣除表', key: 'specialDeduction' },
  { name: '异地纳税', key: 'remoteTax' },
  { name: '身份证号表', key: 'idCard' },
  { name: '项目匹配表', key: 'projectMapping' }
];

// State variables
const activeTabIndex = ref(0);
const searchPanelVisible = ref(false);
const searchParams = reactive({
  startDate: '',
  endDate: '',
  keyword: ''
});

// Table configuration
const tableWidth = ref(1200);
const tableHeight = ref(500);

// Table data for salary tax management
// (1) 个税申报表
const taxDeclarationData = ref([
  [
    '工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
    '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
    '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
    '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
    '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
    '准予扣除的捐赠额', '减免税额', '备注'
  ],
  [
    'E001', '张三', '身份证', '110101199001011234', 15000, 0,
    800, 240, 80, 960, 1000, 0, 1000, 0, 2000, 0, 0, 0, 0, 0, 0, 0, 0, ''
  ],
  [
    'E002', '李四', '身份证', '110101199102022345', 12000, 0,
    750, 200, 60, 900, 1000, 400, 0, 1500, 2000, 0, 0, 0, 0, 0, 0, 0, 0, ''
  ],
  [
    'E003', '王五', '身份证', '110101199203033456', 18000, 0,
    900, 300, 90, 1080, 2000, 0, 1000, 0, 2000, 1000, 0, 0, 0, 0, 0, 0, 0, ''
  ]
]);

// (2) 算税底稿
const taxCalculationData = ref([
  [
    '月份', '缴纳地点', '身份证号', '姓名', '成本所属项目', '薪酬类别',
    '本期收入', '基本养老保险费', '住房公积金', '基本医疗保险费',
    '失业保险费', '企业(职业)年金', '其它扣款', '调整收入', '调整扣除',
    '调整累计个税', '累计社保', '累计专项附加', '累计法定扣除',
    '累计调整扣除', '累计收入', '累计扣除', '累计应扣税款',
    '累计上次税款', '本次税款', '本次达到税率', '一次性年终奖校验'
  ],
  [
    '2025-01', '北京市', '110101199001011234', '张三', '研发项目A', '工资',
    15000, 800, 960, 240, 80, 0, 0, 0, 0, 0, 2080, 4000, 5000, 0,
    15000, 11080, 595, 0, 595, '10%', '否'
  ],
  [
    '2025-01', '北京市', '110101199102022345', '李四', '市场项目B', '工资',
    12000, 750, 900, 200, 60, 0, 0, 0, 0, 0, 1910, 4900, 5000, 0,
    12000, 11810, 19, 0, 19, '3%', '否'
  ]
]);

// (3) 累计专项附加扣除表
const specialDeductionData = ref([
  [
    '工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止',
    '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息',
    '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金'
  ],
  [
    'E001', '张三', '身份证', '110101199001011234', '2025-01-01', '2025-01-31',
    15000, 1000, 0, 1000, 0, 2000, 0, 0
  ],
  [
    'E002', '李四', '身份证', '110101199102022345', '2025-01-01', '2025-01-31',
    12000, 1000, 400, 0, 1500, 2000, 0, 0
  ],
  [
    'E003', '王五', '身份证', '110101199203033456', '2025-01-01', '2025-01-31',
    18000, 2000, 0, 1000, 0, 2000, 1000, 0
  ]
]);

// (4) 异地纳税
const remoteTaxData = ref([
  [
    '预留', '身份证号', '姓名', '一月', '二月', '三月', '四月', '五月', '六月',
    '七月', '八月', '九月', '十月', '十一月', '十二月'
  ],
  [
    '', '110101199001011234', '张三', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
  ],
  [
    '', '110101199102022345', '李四', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
  ],
  [
    '', '110101199203033456', '王五', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
  ]
]);

// (5) 身份证号表
const idCardData = ref([
  ['姓名', '身份证号'],
  ['张三', '110101199001011234'],
  ['李四', '110101199102022345'],
  ['王五', '110101199203033456'],
  ['赵六', '110101199304044567']
]);

// (6) 项目匹配表
const projectMappingData = ref([
  ['人资项目名称', '标准项目名称', '标准项目编码'],
  ['基本工资', '工资薪金所得', 'SALARY_001'],
  ['绩效工资', '工资薪金所得', 'SALARY_002'],
  ['年终奖', '全年一次性奖金', 'BONUS_001'],
  ['加班费', '工资薪金所得', 'SALARY_003'],
  ['津贴补贴', '工资薪金所得', 'SALARY_004'],
  ['社保个人部分', '基本养老保险费', 'SOCIAL_001'],
  ['公积金个人部分', '住房公积金', 'HOUSING_001']
]);

// Methods for salary tax management

// UI Methods
const switchTab = (index) => {
  activeTabIndex.value = index;
};

const toggleSearchPanel = () => {
  searchPanelVisible.value = !searchPanelVisible.value;
};

const search = () => {
  console.log('搜索参数:', searchParams);
  // 实际应用中这里会根据搜索参数过滤表格数据
};

const resetSearch = () => {
  searchParams.startDate = '';
  searchParams.endDate = '';
  searchParams.keyword = '';
};

// 运算函数 (Business Logic Functions)
// (1) 导入薪酬信息
const importSalaryInfo = () => {
  console.log('导入薪酬信息');
  // 实际应用中这里会导入薪酬数据，更新个税申报表和算税底稿
};

// (2) 导入专项附加扣除表
const importSpecialDeduction = () => {
  console.log('导入专项附加扣除表');
  // 实际应用中这里会导入专项附加扣除数据
};

// (3) 匹配身份证号及项目
const matchIdAndProject = () => {
  console.log('匹配身份证号及项目');
  // 实际应用中这里会根据身份证号表和项目匹配表进行数据匹配
};

// (4) 计算个税
const calculateTax = () => {
  console.log('计算个税');
  // 实际应用中这里会根据算税底稿计算个人所得税
};

// (5) 生成申报表
const generateDeclaration = () => {
  console.log('生成申报表');
  // 实际应用中这里会生成个税申报表
};

// (6) 推送申报表
const pushDeclaration = () => {
  console.log('推送申报表');
  // 实际应用中这里会将申报表推送到税务系统
};

// (7) 构建财务一体化计提发放模板
const buildFinanceTemplate = () => {
  console.log('构建财务一体化计提发放模板');
  // 实际应用中这里会构建财务模板
};

// (8) 按模版生成标准支付表
const generateStandardPayment = () => {
  console.log('按模版生成标准支付表');
  // 实际应用中这里会根据模板生成标准支付表
};

// 表格数据变化处理
const handleDataChange = (newData) => {
  console.log('表格数据发生变化:', newData);

  // 根据当前活动的标签页更新对应的数据
  switch (activeTabIndex.value) {
    case 0:
      taxDeclarationData.value = newData;
      break;
    case 1:
      taxCalculationData.value = newData;
      break;
    case 2:
      specialDeductionData.value = newData;
      break;
    case 3:
      remoteTaxData.value = newData;
      break;
    case 4:
      idCardData.value = newData;
      break;
    case 5:
      projectMappingData.value = newData;
      break;
  }

  // 这里可以添加数据验证、自动保存等逻辑
  console.log('数据已更新到对应的表格');
};

// 单元格编辑处理
const handleCellEdit = (editInfo) => {
  console.log('单元格编辑:', editInfo);

  // 这里可以添加编辑验证、格式化等逻辑
  // 例如：数字格式验证、日期格式验证等
};
</script>

<style scoped>
.salary-tax-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f0f2f5;
  padding: 16px;
  box-sizing: border-box;
}

/* Action buttons */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  color: white;
  transition: all 0.3s;
}

.action-button:hover {
  opacity: 0.85;
}

.action-button.import {
  background-color: #1890ff;
}

.action-button.match {
  background-color: #52c41a;
}

.action-button.tax {
  background-color: #722ed1;
}

.action-button.declaration {
  background-color: #fa8c16;
}

.action-button.push {
  background-color: #13c2c2;
}

.action-button.template {
  background-color: #eb2f96;
}

.action-button.payment {
  background-color: #faad14;
}

/* Tabs */
.tabs-container {
  margin-bottom: 16px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  overflow-x: auto;
}

.tab {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  font-size: 14px;
  color: #595959;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  color: #1890ff;
}

.tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
  font-weight: 500;
}

/* Search */
.search-container {
  margin-bottom: 16px;
  position: relative;
}

.search-button {
  width: 100%;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #595959;
}

.search-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.search-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  margin-top: 4px;
  padding: 16px;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group label {
  width: 80px;
  text-align: right;
  font-size: 14px;
  color: #595959;
}

.form-group input {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.form-group input:focus {
  border-color: #1890ff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.search-action {
  padding: 6px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: white;
  background-color: #1890ff;
}

.search-action.reset {
  background-color: #d9d9d9;
  color: #595959;
}

/* VTable container */
.vtable-container {
  flex: 1;
  min-height: 400px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
</style>