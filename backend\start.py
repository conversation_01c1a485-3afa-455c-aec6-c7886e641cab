#!/usr/bin/env python3
"""
财务台账查询系统启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        import duckdb
        import pandas
        import pydantic
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def init_database():
    """初始化数据库"""
    try:
        from init_data import init_sample_data
        print("正在初始化数据库...")
        init_sample_data()
        print("✓ 数据库初始化完成")
        return True
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        return False

def start_server():
    """启动服务器"""
    try:
        print("正在启动FastAPI服务器...")
        print("服务器地址: http://localhost:8000")
        print("API文档: http://localhost:8000/docs")
        print("按 Ctrl+C 停止服务器")
        
        import uvicorn
        uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"✗ 服务器启动失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("财务台账查询系统")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 初始化数据库
    if not init_database():
        sys.exit(1)
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
