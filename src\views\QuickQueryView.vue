<template>
  <div class="quick-query-view">
    <!-- 卡片列表视图 -->
    <div v-if="currentView === 'cards'">
      <h1>财务台账查询</h1>
      <div class="cards-container">
        <div
          v-for="(card, index) in cards"
          :key="index"
          class="card"
          @click="handleCardClick(card)"
        >
          <div class="card-icon">
            <component :is="card.icon" />
          </div>
          <div class="card-content">
            <h3>{{ card.title }}</h3>
            <p>{{ card.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 各种查询组件视图 -->
    <div v-else-if="currentView === 'integrated-contract'">
      <IntegratedContractQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'special-reserve'">
      <SpecialReserveQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'master-data'">
      <MasterDataQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'payment-ledger'">
      <PaymentLedgerQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'guarantee-ledger'">
      <GuaranteeLedgerQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'internal-bank-query'">
      <InternalBankQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'internal-reconciliation'">
      <InternalReconciliationQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'subcontractor-settlement'">
      <SubcontractorSettlementQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'external-confirmation'">
      <ExternalConfirmationQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'payable-by-supplier'">
      <PayableBySupplierQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'payable-by-contract'">
      <PayableByContractQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'general-ledger'">
      <GeneralLedgerQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'cost-ledger'">
      <CostLedgerQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'receipt-ledger'">
      <ReceiptLedgerQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'fund-management'">
      <FundManagementQueryComponent @back="goBack" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import IntegratedContractQueryComponent from '@/components/IntegratedContractQueryComponent.vue'
import SpecialReserveQueryComponent from '@/components/SpecialReserveQueryComponent.vue'
import MasterDataQueryComponent from '@/components/MasterDataQueryComponent.vue'
import PaymentLedgerQueryComponent from '@/components/PaymentLedgerQueryComponent.vue'
import GuaranteeLedgerQueryComponent from '@/components/GuaranteeLedgerQueryComponent.vue'
import InternalBankQueryComponent from '@/components/InternalBankQueryComponent.vue'
import InternalReconciliationQueryComponent from '@/components/InternalReconciliationQueryComponent.vue'
import SubcontractorSettlementQueryComponent from '@/components/SubcontractorSettlementQueryComponent.vue'
import ExternalConfirmationQueryComponent from '@/components/ExternalConfirmationQueryComponent.vue'
import PayableBySupplierQueryComponent from '@/components/PayableBySupplierQueryComponent.vue'
import PayableByContractQueryComponent from '@/components/PayableByContractQueryComponent.vue'
import GeneralLedgerQueryComponent from '@/components/GeneralLedgerQueryComponent.vue'
import CostLedgerQueryComponent from '@/components/CostLedgerQueryComponent.vue'
import ReceiptLedgerQueryComponent from '@/components/ReceiptLedgerQueryComponent.vue'
import FundManagementQueryComponent from '@/components/FundManagementQueryComponent.vue'
import {
  User,
  Document,
  Money,
  List,
  TrendCharts,
  DataAnalysis,
  CreditCard,
  Checked,
  Warning,
  Select,
  Tickets
} from '@element-plus/icons-vue'

const router = useRouter()

// 当前视图状态
const currentView = ref('cards')

// 卡片功能数据 - 根据实际表结构重构，包含所有19个表
const cards = [
  {
    title: '一体化合同台账',
    description: '合同全生命周期管理台账',
    icon: Document,
    type: 'integrated-contract'
  },
  {
    title: '专项储备',
    description: '安全生产费专项储备管理',
    icon: Warning,
    type: 'special-reserve'
  },
  {
    title: '主数据',
    description: '项目基础主数据查询',
    icon: DataAnalysis,
    type: 'master-data'
  },
  {
    title: '付款台账',
    description: '详细付款记录台账',
    icon: Money,
    type: 'payment-ledger'
  },
  {
    title: '保证金台账',
    description: '保证金收取与回收管理',
    icon: CreditCard,
    type: 'guarantee-ledger'
  },
  {
    title: '内行查询',
    description: '内部银行资金查询',
    icon: Select,
    type: 'internal-bank-query'
  },
  {
    title: '内部对账',
    description: '内部往来对账记录',
    icon: Checked,
    type: 'internal-reconciliation'
  },
  {
    title: '分供结算台账',
    description: '分包商结算记录管理',
    icon: List,
    type: 'subcontractor-settlement'
  },
  {
    title: '外部确权台账',
    description: '外部确权收入记录',
    icon: User,
    type: 'external-confirmation'
  },
  {
    title: '应付汇总按供应商',
    description: '供应商维度应付汇总',
    icon: TrendCharts,
    type: 'payable-by-supplier'
  },
  {
    title: '应付汇总按合同',
    description: '合同维度应付汇总',
    icon: Tickets,
    type: 'payable-by-contract'
  },
  {
    title: '总台账',
    description: '项目综合财务台账',
    icon: DataAnalysis,
    type: 'general-ledger'
  },
  {
    title: '成本表',
    description: '项目成本明细记录',
    icon: TrendCharts,
    type: 'cost-ledger'
  },
  {
    title: '收款台账',
    description: '详细收款记录管理',
    icon: CreditCard,
    type: 'receipt-ledger'
  },
  {
    title: '资金整理',
    description: '资金流水整理分析',
    icon: Money,
    type: 'fund-management'
  }
]

// 处理卡片点击
function handleCardClick(card) {
  // 所有卡片都切换到对应的查询组件，不再跳转路由
  currentView.value = card.type
}

// 返回卡片列表
function goBack() {
  currentView.value = 'cards'
}
</script>

<style>
body {
  background: #f8f9fb;
}

.quick-query-view {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

h1 {
  font-size: 2.2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #222;
  letter-spacing: 2px;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  width: 100%;
  max-width: 1200px;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  background: #ecf5ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: #409eff;
}

.card-icon i {
  font-size: 28px;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.card-content p {
  font-size: 14px;
  color: #666;
  margin: 0;
}
</style>