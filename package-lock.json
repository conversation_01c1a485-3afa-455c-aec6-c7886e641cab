{"name": "mytable", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "mytable", "version": "0.0.0", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.18", "@kjgl77/datav-vue3": "^1.7.4", "@univerjs/core": "^0.6.10", "@univerjs/design": "^0.6.10", "@univerjs/presets": "^0.6.10", "@univerjs/sheets": "^0.6.10", "@univerjs/sheets-ui": "^0.6.10", "@univerjs/ui": "^0.6.10", "@visactor/vchart": "^1.13.10", "@visactor/vtable": "^1.18.4", "@visactor/vtable-editors": "^1.18.2", "@visactor/vtable-export": "^1.18.4", "@visactor/vue-vtable": "^1.18.2", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.11", "exceljs": "^4.4.0", "luckysheet": "^2.1.5", "react": "^19.1.0", "react-dom": "^19.1.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "rollup-plugin-visualizer": "^6.0.0", "sass-embedded": "^1.89.0", "terser": "^5.42.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@antfu/utils": {"version": "0.7.10", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.27.5", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.27.4", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.4", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/traverse": "^7.27.4", "@babel/types": "^7.27.3", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/generator": {"version": "7.27.5", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.27.3", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.6", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.27.5", "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-proposal-decorators": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-decorators": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typescript": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.4", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.27.6", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@braintree/sanitize-url": {"version": "6.0.4", "license": "MIT"}, "node_modules/@bufbuild/protobuf": {"version": "2.5.2", "dev": true, "license": "(Apache-2.0 AND BSD-3-<PERSON><PERSON>)"}, "node_modules/@ctrl/tinycolor": {"version": "3.6.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@element-plus/icons-vue": {"version": "2.3.1", "license": "MIT", "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.5", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@fast-csv/format": {"version": "4.3.5", "license": "MIT", "dependencies": {"@types/node": "^14.0.1", "lodash.escaperegexp": "^4.1.2", "lodash.isboolean": "^3.0.3", "lodash.isequal": "^4.5.0", "lodash.isfunction": "^3.0.9", "lodash.isnil": "^4.0.0"}}, "node_modules/@fast-csv/format/node_modules/@types/node": {"version": "14.18.63", "license": "MIT"}, "node_modules/@fast-csv/parse": {"version": "4.3.6", "license": "MIT", "dependencies": {"@types/node": "^14.0.1", "lodash.escaperegexp": "^4.1.2", "lodash.groupby": "^4.6.0", "lodash.isfunction": "^3.0.9", "lodash.isnil": "^4.0.0", "lodash.isundefined": "^3.0.1", "lodash.uniq": "^4.5.0"}}, "node_modules/@fast-csv/parse/node_modules/@types/node": {"version": "14.18.63", "license": "MIT"}, "node_modules/@flatten-js/interval-tree": {"version": "1.1.3", "license": "MIT"}, "node_modules/@floating-ui/core": {"version": "1.7.1", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/dom": {"version": "1.7.1", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.7.1", "@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/react-dom": {"version": "2.1.3", "license": "MIT", "dependencies": {"@floating-ui/dom": "^1.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@floating-ui/utils": {"version": "0.2.9", "license": "MIT"}, "node_modules/@grpc/grpc-js": {"version": "1.13.4", "license": "Apache-2.0", "peer": true, "dependencies": {"@grpc/proto-loader": "^0.7.13", "@js-sdsl/ordered-map": "^4.4.2"}, "engines": {"node": ">=12.10.0"}}, "node_modules/@grpc/proto-loader": {"version": "0.7.15", "license": "Apache-2.0", "peer": true, "dependencies": {"lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2"}, "bin": {"proto-loader-gen-types": "build/bin/proto-loader-gen-types.js"}, "engines": {"node": ">=6"}}, "node_modules/@jiaminghi/bezier-curve": {"version": "0.0.9", "license": "MIT", "dependencies": {"@babel/runtime": "^7.5.5"}}, "node_modules/@jiaminghi/c-render": {"version": "0.4.3", "license": "MIT", "dependencies": {"@babel/runtime": "^7.5.5", "@jiaminghi/bezier-curve": "*", "@jiaminghi/color": "*", "@jiaminghi/transition": "*"}}, "node_modules/@jiaminghi/charts": {"version": "0.2.18", "license": "MIT", "dependencies": {"@babel/runtime": "^7.5.5", "@jiaminghi/c-render": "^0.4.3"}}, "node_modules/@jiaminghi/color": {"version": "1.1.3", "license": "MIT"}, "node_modules/@jiaminghi/transition": {"version": "1.1.11", "license": "MIT", "dependencies": {"@babel/runtime": "^7.5.5"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.6", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@js-sdsl/ordered-map": {"version": "4.4.2", "license": "MIT", "peer": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/js-sdsl"}}, "node_modules/@kangc/v-md-editor": {"version": "2.3.18", "license": "MIT", "dependencies": {"@babel/runtime": "^7.14.0", "@vuepress/markdown": "^1.8.2", "codemirror": "^5.61.1", "copy-to-clipboard": "^3.3.1", "highlight.js": "^10.7.2", "insert-text-at-cursor": "^0.3.0", "katex": "^0.13.11", "markdown-it": "^12.3.2", "markdown-it-attrs": "^4.0.0", "markdown-it-container": "^3.0.0", "mermaid": "^10.6.1", "prismjs": "^1.23.0", "resize-observer-polyfill": "^1.5.1", "vant": "^3.1.3", "xss": "^1.0.9"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.0", "vue": "^3.0.0"}}, "node_modules/@kjgl77/datav-vue3": {"version": "1.7.4", "license": "MIT", "dependencies": {"@jiaminghi/c-render": "^0.4.3", "@jiaminghi/charts": "^0.2.18", "@jiaminghi/color": "^1.1.3", "@vueuse/core": "^10.11.1"}}, "node_modules/@mrmlnc/readdir-enhanced": {"version": "2.2.1", "license": "MIT", "dependencies": {"call-me-maybe": "^1.0.1", "glob-to-regexp": "^0.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@noble/ed25519": {"version": "2.2.3", "license": "MIT", "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/hashes": {"version": "1.7.1", "license": "MIT", "engines": {"node": "^14.21.3 || >=16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@nodelib/fs.stat": {"version": "1.1.3", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/@polka/url": {"version": "1.0.0-next.29", "dev": true, "license": "MIT"}, "node_modules/@popperjs/core": {"name": "@sxzz/popperjs-es", "version": "2.11.7", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>", "peer": true, "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@protobufjs/path": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@radix-ui/primitive": {"version": "1.1.2", "license": "MIT"}, "node_modules/@radix-ui/react-arrow": {"version": "1.1.7", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.1.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-collection": {"version": "1.1.7", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-slot": "1.2.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-compose-refs": {"version": "1.1.2", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-context": {"version": "1.1.2", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-direction": {"version": "1.1.1", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-dismissable-layer": {"version": "1.1.10", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-escape-keydown": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-dropdown-menu": {"version": "2.1.15", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-menu": "2.1.15", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-controllable-state": "1.2.2"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-focus-guards": {"version": "1.1.2", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-focus-scope": {"version": "1.1.7", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-callback-ref": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-id": {"version": "1.1.1", "license": "MIT", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-menu": {"version": "2.1.15", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.7", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-focus-guards": "1.1.2", "@radix-ui/react-focus-scope": "1.1.7", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-popper": "1.2.7", "@radix-ui/react-portal": "1.1.9", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-roving-focus": "1.1.10", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-use-callback-ref": "1.1.1", "aria-hidden": "^1.2.4", "react-remove-scroll": "^2.6.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-popover": {"version": "1.1.14", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-focus-guards": "1.1.2", "@radix-ui/react-focus-scope": "1.1.7", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-popper": "1.2.7", "@radix-ui/react-portal": "1.1.9", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-use-controllable-state": "1.2.2", "aria-hidden": "^1.2.4", "react-remove-scroll": "^2.6.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-popper": {"version": "1.2.7", "license": "MIT", "dependencies": {"@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-arrow": "1.1.7", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/rect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-portal": {"version": "1.1.9", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-presence": {"version": "1.1.4", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-primitive": {"version": "2.1.3", "license": "MIT", "dependencies": {"@radix-ui/react-slot": "1.2.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-roving-focus": {"version": "1.1.10", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.7", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-separator": {"version": "1.1.7", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.1.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-slot": {"version": "1.2.3", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-tooltip": {"version": "1.2.7", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-popper": "1.2.7", "@radix-ui/react-portal": "1.1.9", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-visually-hidden": "1.2.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-use-callback-ref": {"version": "1.1.1", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-controllable-state": {"version": "1.2.2", "license": "MIT", "dependencies": {"@radix-ui/react-use-effect-event": "0.0.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-effect-event": {"version": "0.0.2", "license": "MIT", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-escape-keydown": {"version": "1.1.1", "license": "MIT", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-layout-effect": {"version": "1.1.1", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-rect": {"version": "1.1.1", "license": "MIT", "dependencies": {"@radix-ui/rect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-size": {"version": "1.1.1", "license": "MIT", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-visually-hidden": {"version": "1.2.3", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.1.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/rect": {"version": "1.1.1", "license": "MIT"}, "node_modules/@rc-component/portal": {"version": "1.1.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.0", "classnames": "^2.3.2", "rc-util": "^5.24.4"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/trigger": {"version": "2.2.6", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.2", "@rc-component/portal": "^1.1.0", "classnames": "^2.3.2", "rc-motion": "^2.0.0", "rc-resize-observer": "^1.3.1", "rc-util": "^5.44.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@resvg/resvg-js": {"version": "2.4.1", "license": "MPL-2.0", "engines": {"node": ">= 10"}, "optionalDependencies": {"@resvg/resvg-js-android-arm-eabi": "2.4.1", "@resvg/resvg-js-android-arm64": "2.4.1", "@resvg/resvg-js-darwin-arm64": "2.4.1", "@resvg/resvg-js-darwin-x64": "2.4.1", "@resvg/resvg-js-linux-arm-gnueabihf": "2.4.1", "@resvg/resvg-js-linux-arm64-gnu": "2.4.1", "@resvg/resvg-js-linux-arm64-musl": "2.4.1", "@resvg/resvg-js-linux-x64-gnu": "2.4.1", "@resvg/resvg-js-linux-x64-musl": "2.4.1", "@resvg/resvg-js-win32-arm64-msvc": "2.4.1", "@resvg/resvg-js-win32-ia32-msvc": "2.4.1", "@resvg/resvg-js-win32-x64-msvc": "2.4.1"}}, "node_modules/@resvg/resvg-js-win32-x64-msvc": {"version": "2.4.1", "cpu": ["x64"], "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-android-arm-eabi": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-android-arm-eabi/-/resvg-js-android-arm-eabi-2.4.1.tgz", "integrity": "sha512-AA6f7hS0FAPpvQMhBCf6f1oD1LdlqNXKCxAAPpKh6tR11kqV0YIB9zOlIYgITM14mq2YooLFl6XIbbvmY+jwUw==", "cpu": ["arm"], "license": "MPL-2.0", "optional": true, "os": ["android"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-android-arm64": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-android-arm64/-/resvg-js-android-arm64-2.4.1.tgz", "integrity": "sha512-/QleoRdPfsEuH9jUjilYcDtKK/BkmWcK+1LXM8L2nsnf/CI8EnFyv7ZzCj4xAIvZGAy9dTYr/5NZBcTwxG2HQg==", "cpu": ["arm64"], "license": "MPL-2.0", "optional": true, "os": ["android"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-darwin-arm64": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-darwin-arm64/-/resvg-js-darwin-arm64-2.4.1.tgz", "integrity": "sha512-U1oMNhea+kAXgiEXgzo7EbFGCD1Edq5aSlQoe6LMly6UjHzgx2W3N5kEXCwU/CgN5FiQhZr7PlSJSlcr7mdhfg==", "cpu": ["arm64"], "license": "MPL-2.0", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-darwin-x64": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-darwin-x64/-/resvg-js-darwin-x64-2.4.1.tgz", "integrity": "sha512-avyVh6DpebBfHHtTQTZYSr6NG1Ur6TEilk1+H0n7V+g4F7x7WPOo8zL00ZhQCeRQ5H4f8WXNWIEKL8fwqcOkYw==", "cpu": ["x64"], "license": "MPL-2.0", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-linux-arm-gnueabihf": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-arm-gnueabihf/-/resvg-js-linux-arm-gnueabihf-2.4.1.tgz", "integrity": "sha512-isY/mdKoBWH4VB5v621co+8l101jxxYjuTkwOLsbW+5RK9EbLciPlCB02M99ThAHzI2MYxIUjXNmNgOW8btXvw==", "cpu": ["arm"], "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-linux-arm64-gnu": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-arm64-gnu/-/resvg-js-linux-arm64-gnu-2.4.1.tgz", "integrity": "sha512-uY5voSCrFI8TH95vIYBm5blpkOtltLxLRODyhKJhGfskOI7XkRw5/t1u0sWAGYD8rRSNX+CA+np86otKjubrNg==", "cpu": ["arm64"], "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-linux-arm64-musl": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-arm64-musl/-/resvg-js-linux-arm64-musl-2.4.1.tgz", "integrity": "sha512-6mT0+JBCsermKMdi/O2mMk3m7SqOjwi9TKAwSngRZ/nQoL3Z0Z5zV+572ztgbWr0GODB422uD8e9R9zzz38dRQ==", "cpu": ["arm64"], "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-linux-x64-gnu": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-x64-gnu/-/resvg-js-linux-x64-gnu-2.4.1.tgz", "integrity": "sha512-60KnrscLj6VGhkYOJEmmzPlqqfcw1keDh6U+vMcNDjPhV3B5vRSkpP/D/a8sfokyeh4VEacPSYkWGezvzS2/mg==", "cpu": ["x64"], "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-linux-x64-musl": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-x64-musl/-/resvg-js-linux-x64-musl-2.4.1.tgz", "integrity": "sha512-0AMyZSICC1D7ge115cOZQW8Pcad6PjWuZkBFF3FJuSxC6Dgok0MQnLTs2MfMdKBlAcwO9dXsf3bv9tJZj8pATA==", "cpu": ["x64"], "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-win32-arm64-msvc": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-win32-arm64-msvc/-/resvg-js-win32-arm64-msvc-2.4.1.tgz", "integrity": "sha512-76XDFOFSa3d0QotmcNyChh2xHwk+JTFiEQBVxMlHpHMeq7hNrQJ1IpE1zcHSQvrckvkdfLboKRrlGB86B10Qjw==", "cpu": ["arm64"], "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js/node_modules/@resvg/resvg-js-win32-ia32-msvc": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-win32-ia32-msvc/-/resvg-js-win32-ia32-msvc-2.4.1.tgz", "integrity": "sha512-odyVFGrEWZIzzJ89KdaFtiYWaIJh9hJRW/frcEcG3agJ464VXkN/2oEVF5ulD+5mpGlug9qJg7htzHcKxDN8sg==", "cpu": ["ia32"], "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@rollup/pluginutils": {"version": "5.1.4", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^4.0.2"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.43.0", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@sec-ant/readable-stream": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/@sindresorhus/merge-streams": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@turf/boolean-clockwise": {"version": "6.5.0", "license": "MIT", "dependencies": {"@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/clone": {"version": "6.5.0", "license": "MIT", "dependencies": {"@turf/helpers": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/flatten": {"version": "6.5.0", "license": "MIT", "dependencies": {"@turf/helpers": "^6.5.0", "@turf/meta": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/helpers": {"version": "6.5.0", "license": "MIT", "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/invariant": {"version": "6.5.0", "license": "MIT", "dependencies": {"@turf/helpers": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/meta": {"version": "6.5.0", "license": "MIT", "dependencies": {"@turf/helpers": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/rewind": {"version": "6.5.0", "license": "MIT", "dependencies": {"@turf/boolean-clockwise": "^6.5.0", "@turf/clone": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0", "@turf/meta": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@types/d3-scale": {"version": "4.0.9", "license": "MIT", "dependencies": {"@types/d3-time": "*"}}, "node_modules/@types/d3-scale-chromatic": {"version": "3.1.0", "license": "MIT"}, "node_modules/@types/d3-time": {"version": "3.0.4", "license": "MIT"}, "node_modules/@types/debug": {"version": "4.1.12", "license": "MIT", "dependencies": {"@types/ms": "*"}}, "node_modules/@types/estree": {"version": "1.0.7", "dev": true, "license": "MIT"}, "node_modules/@types/file-saver": {"version": "2.0.7", "license": "MIT"}, "node_modules/@types/glob": {"version": "7.2.0", "license": "MIT", "dependencies": {"@types/minimatch": "*", "@types/node": "*"}}, "node_modules/@types/hoist-non-react-statics": {"version": "3.3.6", "license": "MIT", "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}}, "node_modules/@types/lodash": {"version": "4.17.17", "license": "MIT"}, "node_modules/@types/lodash-es": {"version": "4.17.12", "license": "MIT", "dependencies": {"@types/lodash": "*"}}, "node_modules/@types/mdast": {"version": "3.0.15", "license": "MIT", "dependencies": {"@types/unist": "^2"}}, "node_modules/@types/minimatch": {"version": "5.1.2", "license": "MIT"}, "node_modules/@types/ms": {"version": "2.1.0", "license": "MIT"}, "node_modules/@types/node": {"version": "24.0.1", "license": "MIT", "dependencies": {"undici-types": "~7.8.0"}}, "node_modules/@types/react": {"version": "19.1.8", "license": "MIT", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/react-redux": {"version": "7.1.34", "license": "MIT", "dependencies": {"@types/hoist-non-react-statics": "^3.3.0", "@types/react": "*", "hoist-non-react-statics": "^3.3.0", "redux": "^4.0.0"}}, "node_modules/@types/unist": {"version": "2.0.11", "license": "MIT"}, "node_modules/@types/web-bluetooth": {"version": "0.0.20", "license": "MIT"}, "node_modules/@univerjs-pro/collaboration": {"version": "0.6.10", "dependencies": {"@univerjs-pro/license": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/data-validation": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/protocol": "0.1.46-alpha.3", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-conditional-formatting": "0.6.10", "@univerjs/sheets-drawing": "0.6.10", "@univerjs/sheets-filter": "0.6.10", "@univerjs/sheets-hyper-link": "0.6.10", "@univerjs/thread-comment": "0.6.10", "uuid": "^11.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/collaboration-client": {"version": "0.6.10", "dependencies": {"@univerjs-pro/collaboration": "0.6.10", "@univerjs-pro/license": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/network": "0.6.10", "@univerjs/protocol": "0.1.46-alpha.3", "@univerjs/sheets": "0.6.10", "@univerjs/telemetry": "0.6.10", "crypto-js": "4.2.0", "uuid": "^11.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/collaboration-client-ui": {"version": "0.6.10", "dependencies": {"@univerjs-pro/collaboration": "0.6.10", "@univerjs-pro/collaboration-client": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/network": "0.6.10", "@univerjs/protocol": "0.1.46-alpha.3", "@univerjs/rpc": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10", "crypto-js": "4.2.0", "uuid": "^11.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/collaboration-client-ui/node_modules/@univerjs/protocol": {"version": "0.1.46-alpha.3", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/collaboration-client/node_modules/@univerjs/protocol": {"version": "0.1.46-alpha.3", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/collaboration/node_modules/@univerjs/protocol": {"version": "0.1.46-alpha.3", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/edit-history-loader": {"version": "0.6.10", "dependencies": {"@univerjs-pro/collaboration": "0.6.10", "@univerjs-pro/collaboration-client": "0.6.10", "@univerjs-pro/edit-history-viewer": "0.6.10", "@univerjs-pro/sheets-pivot": "0.6.10", "@univerjs-pro/sheets-sparkline": "0.6.10", "@univerjs-pro/sheets-sparkline-ui": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/data-validation": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/drawing-ui": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/network": "0.6.10", "@univerjs/rpc": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-conditional-formatting": "0.6.10", "@univerjs/sheets-conditional-formatting-ui": "0.6.10", "@univerjs/sheets-data-validation": "0.6.10", "@univerjs/sheets-data-validation-ui": "0.6.10", "@univerjs/sheets-drawing": "0.6.10", "@univerjs/sheets-drawing-ui": "0.6.10", "@univerjs/sheets-filter": "0.6.10", "@univerjs/sheets-filter-ui": "0.6.10", "@univerjs/sheets-formula": "0.6.10", "@univerjs/sheets-formula-ui": "0.6.10", "@univerjs/sheets-hyper-link": "0.6.10", "@univerjs/sheets-hyper-link-ui": "0.6.10", "@univerjs/sheets-numfmt": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/edit-history-viewer": {"version": "0.6.10", "dependencies": {"@univerjs-pro/collaboration": "0.6.10", "@univerjs-pro/collaboration-client": "0.6.10", "@univerjs-pro/collaboration-client-ui": "0.6.10", "@univerjs-pro/sheets-sparkline": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/data-validation": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/network": "0.6.10", "@univerjs/protocol": "0.1.46-alpha.3", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-conditional-formatting": "0.6.10", "@univerjs/sheets-filter": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/edit-history-viewer/node_modules/@univerjs/protocol": {"version": "0.1.46-alpha.3", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/engine-chart": {"version": "0.6.10", "dependencies": {"@univerjs/core": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/engine-formula": {"version": "0.6.10", "dependencies": {"@univerjs-pro/license": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/engine-pivot": {"version": "0.6.10", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/exchange-client": {"version": "0.6.10", "dependencies": {"@univerjs-pro/collaboration": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/network": "0.6.10", "@univerjs/protocol": "0.1.46-alpha.3", "@univerjs/ui": "0.6.10", "pako": "^2.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/exchange-client/node_modules/@univerjs/protocol": {"version": "0.1.46-alpha.3", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/license": {"version": "0.6.10", "dependencies": {"@noble/ed25519": "2.2.3", "@noble/hashes": "1.7.1", "@univerjs/core": "0.6.10", "@univerjs/engine-render": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/print": {"version": "0.6.10", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/sheets-chart": {"version": "0.6.10", "dependencies": {"@univerjs-pro/engine-chart": "0.6.10", "@univerjs-pro/license": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/sheets": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-chart-ui": {"version": "0.6.10", "dependencies": {"@univerjs-pro/engine-chart": "0.6.10", "@univerjs-pro/sheets-chart": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-drawing-ui": "0.6.10", "@univerjs/sheets-formula-ui": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "rc-collapse": "3.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-exchange-client": {"version": "0.6.10", "dependencies": {"@univerjs-pro/exchange-client": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/sheets-pivot": {"version": "0.6.10", "dependencies": {"@univerjs-pro/engine-pivot": "0.6.10", "@univerjs-pro/license": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/rpc": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-filter": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-pivot-ui": {"version": "0.6.10", "dependencies": {"@univerjs-pro/engine-pivot": "0.6.10", "@univerjs-pro/sheets-pivot": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-formula-ui": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10", "react-beautiful-dnd": "^13.1.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-pivot-ui/node_modules/memoize-one": {"version": "5.2.1", "license": "MIT"}, "node_modules/@univerjs-pro/sheets-pivot-ui/node_modules/react-beautiful-dnd": {"version": "13.1.1", "license": "Apache-2.0", "dependencies": {"@babel/runtime": "^7.9.2", "css-box-model": "^1.2.0", "memoize-one": "^5.1.1", "raf-schd": "^4.0.2", "react-redux": "^7.2.0", "redux": "^4.0.4", "use-memo-one": "^1.1.1"}, "peerDependencies": {"react": "^16.8.5 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.5 || ^17.0.0 || ^18.0.0"}}, "node_modules/@univerjs-pro/sheets-pivot-ui/node_modules/react-beautiful-dnd/node_modules/react-redux": {"version": "7.2.9", "license": "MIT", "dependencies": {"@babel/runtime": "^7.15.4", "@types/react-redux": "^7.1.20", "hoist-non-react-statics": "^3.3.2", "loose-envify": "^1.4.0", "prop-types": "^15.7.2", "react-is": "^17.0.2"}, "peerDependencies": {"react": "^16.8.3 || ^17 || ^18"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "node_modules/@univerjs-pro/sheets-pivot-ui/node_modules/react-beautiful-dnd/node_modules/use-memo-one": {"version": "1.1.3", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/@univerjs-pro/sheets-pivot-ui/node_modules/react-is": {"version": "17.0.2", "license": "MIT"}, "node_modules/@univerjs-pro/sheets-print": {"version": "0.6.10", "dependencies": {"@univerjs-pro/license": "0.6.10", "@univerjs-pro/print": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/network": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10", "crypto-js": "^4.2.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "optionalDependencies": {"@univerjs-pro/collaboration-client": "0.6.10"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-sparkline": {"version": "0.6.10", "dependencies": {"@univerjs-pro/license": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/sheets": "0.6.10"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-sparkline-ui": {"version": "0.6.10", "dependencies": {"@univerjs-pro/sheets-sparkline": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-formula-ui": "0.6.10", "@univerjs/sheets-graphics": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/thread-comment-datasource": {"version": "0.6.10", "dependencies": {"@univerjs-pro/collaboration-client": "0.6.10", "@univerjs-pro/license": "0.6.10", "@univerjs/core": "0.6.10", "@univerjs/network": "0.6.10", "@univerjs/protocol": "0.1.46-alpha.3", "@univerjs/thread-comment": "0.6.10", "@univerjs/thread-comment-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/thread-comment-datasource/node_modules/@univerjs/protocol": {"version": "0.1.46-alpha.3", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs/core": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/protocol": "0.1.45", "@wendellhu/redi": "0.17.1", "async-lock": "^1.4.1", "dayjs": "^1.11.13", "fast-diff": "1.3.0", "kdbush": "^4.0.2", "lodash-es": "^4.17.21", "nanoid": "5.1.5", "numeral": "^2.0.6", "numfmt": "^2.5.2", "ot-json1": "^1.0.2", "rbush": "^4.0.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"@wendellhu/redi": "0.17.1", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/data-validation": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/design": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.0", "@rc-component/trigger": "^2.2.6", "@univerjs/icons": "^0.3.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "rc-dialog": "^9.6.0", "rc-dropdown": "^4.2.1", "rc-menu": "^9.16.0", "rc-picker": "^4.9.0", "rc-select": "^14.16.4", "rc-virtual-list": "^3.16.1", "react-draggable": "^4.4.6", "react-grid-layout": "^1.5.1", "react-transition-group": "^4.4.5", "tailwind-merge": "^3.2.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}}, "node_modules/@univerjs/docs": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-render": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/docs-drawing": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/drawing": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/docs-drawing-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-drawing": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/drawing-ui": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/docs-hyper-link": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/docs-hyper-link-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-hyper-link": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/docs-thread-comment-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/thread-comment": "0.6.10", "@univerjs/thread-comment-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/docs-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/drawing": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "ot-json1": "^1.0.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/drawing-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/engine-formula": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@flatten-js/interval-tree": "^1.1.3", "@univerjs/core": "0.6.10", "@univerjs/engine-numfmt": "0.6.10", "@univerjs/rpc": "0.6.10", "decimal.js": "^10.5.0", "numfmt": "^2.5.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/engine-numfmt": {"version": "0.6.10", "license": "Apache-2.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/engine-render": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@floating-ui/dom": "^1.6.13", "@floating-ui/utils": "^0.2.9", "@univerjs/core": "0.6.10", "cjk-regex": "^3.3.0", "franc-min": "^6.2.0", "opentype.js": "^1.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/find-replace": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/icons": {"version": "0.3.24", "license": "MIT", "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/@univerjs/network": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-collaboration": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs-pro/collaboration": "0.6.10", "@univerjs-pro/collaboration-client": "0.6.10", "@univerjs-pro/collaboration-client-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-core": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/network": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-drawing": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/docs-drawing": "0.6.10", "@univerjs/docs-drawing-ui": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/drawing-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-hyper-link": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/docs-hyper-link": "0.6.10", "@univerjs/docs-hyper-link-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-thread-comment": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/docs-thread-comment-ui": "0.6.10", "@univerjs/thread-comment-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-advanced": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs-pro/engine-chart": "0.6.10", "@univerjs-pro/engine-formula": "0.6.10", "@univerjs-pro/exchange-client": "0.6.10", "@univerjs-pro/license": "0.6.10", "@univerjs-pro/sheets-chart": "0.6.10", "@univerjs-pro/sheets-chart-ui": "0.6.10", "@univerjs-pro/sheets-exchange-client": "0.6.10", "@univerjs-pro/sheets-pivot": "0.6.10", "@univerjs-pro/sheets-pivot-ui": "0.6.10", "@univerjs-pro/sheets-print": "0.6.10", "@univerjs-pro/sheets-sparkline": "0.6.10", "@univerjs-pro/sheets-sparkline-ui": "0.6.10", "@univerjs/sheets-graphics": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-collaboration": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs-pro/collaboration": "0.6.10", "@univerjs-pro/collaboration-client": "0.6.10", "@univerjs-pro/collaboration-client-ui": "0.6.10", "@univerjs-pro/edit-history-loader": "0.6.10", "@univerjs-pro/edit-history-viewer": "0.6.10", "@univerjs-pro/thread-comment-datasource": "0.6.10", "@univerjs/preset-sheets-advanced": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-conditional-formatting": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/sheets-conditional-formatting": "0.6.10", "@univerjs/sheets-conditional-formatting-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-core": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/network": "0.6.10", "@univerjs/rpc": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-formula": "0.6.10", "@univerjs/sheets-formula-ui": "0.6.10", "@univerjs/sheets-numfmt": "0.6.10", "@univerjs/sheets-numfmt-ui": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-data-validation": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/data-validation": "0.6.10", "@univerjs/sheets-data-validation": "0.6.10", "@univerjs/sheets-data-validation-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-drawing": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/docs-drawing": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/drawing-ui": "0.6.10", "@univerjs/sheets-drawing": "0.6.10", "@univerjs/sheets-drawing-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-filter": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/sheets-filter": "0.6.10", "@univerjs/sheets-filter-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-find-replace": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/find-replace": "0.6.10", "@univerjs/sheets-find-replace": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-hyper-link": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/sheets-hyper-link": "0.6.10", "@univerjs/sheets-hyper-link-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-node-core": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/docs": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/rpc-node": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-data-validation": "0.6.10", "@univerjs/sheets-drawing": "0.6.10", "@univerjs/sheets-filter": "0.6.10", "@univerjs/sheets-formula": "0.6.10", "@univerjs/sheets-hyper-link": "0.6.10", "@univerjs/sheets-numfmt": "0.6.10", "@univerjs/sheets-sort": "0.6.10", "@univerjs/sheets-thread-comment": "0.6.10", "@univerjs/thread-comment": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-sort": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/sheets-sort": "0.6.10", "@univerjs/sheets-sort-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-thread-comment": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/sheets-thread-comment": "0.6.10", "@univerjs/sheets-thread-comment-ui": "0.6.10", "@univerjs/thread-comment": "0.6.10", "@univerjs/thread-comment-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/presets": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/preset-docs-collaboration": "0.6.10", "@univerjs/preset-docs-core": "0.6.10", "@univerjs/preset-docs-drawing": "0.6.10", "@univerjs/preset-docs-hyper-link": "0.6.10", "@univerjs/preset-docs-thread-comment": "0.6.10", "@univerjs/preset-sheets-advanced": "0.6.10", "@univerjs/preset-sheets-collaboration": "0.6.10", "@univerjs/preset-sheets-conditional-formatting": "0.6.10", "@univerjs/preset-sheets-core": "0.6.10", "@univerjs/preset-sheets-data-validation": "0.6.10", "@univerjs/preset-sheets-drawing": "0.6.10", "@univerjs/preset-sheets-filter": "0.6.10", "@univerjs/preset-sheets-find-replace": "0.6.10", "@univerjs/preset-sheets-hyper-link": "0.6.10", "@univerjs/preset-sheets-node-core": "0.6.10", "@univerjs/preset-sheets-sort": "0.6.10", "@univerjs/preset-sheets-thread-comment": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/protocol": {"version": "0.1.45", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs/rpc": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/rpc-node": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/rpc": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-numfmt": "0.6.10", "@univerjs/protocol": "0.1.45", "@univerjs/rpc": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-conditional-formatting": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/sheets": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-conditional-formatting-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-conditional-formatting": "0.6.10", "@univerjs/sheets-formula-ui": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10", "react-grid-layout": "^1.5.1", "react-resizable": "^3.0.5"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-data-validation": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/data-validation": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/protocol": "0.1.45", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-formula": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-data-validation-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@flatten-js/interval-tree": "^1.1.3", "@univerjs/core": "0.6.10", "@univerjs/data-validation": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-data-validation": "0.6.10", "@univerjs/sheets-formula-ui": "0.6.10", "@univerjs/sheets-numfmt": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-drawing": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/drawing": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/sheets-drawing-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs-drawing": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/drawing": "0.6.10", "@univerjs/drawing-ui": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-drawing": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-filter": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/rpc": "0.6.10", "@univerjs/sheets": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-filter-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/rpc": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-filter": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-find-replace": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/find-replace": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-formula": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/rpc": "0.6.10", "@univerjs/sheets": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-formula-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-formula": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-graphics": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/sheets-ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/sheets-hyper-link": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/protocol": "0.1.45", "@univerjs/sheets": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-hyper-link-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-data-validation": "0.6.10", "@univerjs/sheets-formula-ui": "0.6.10", "@univerjs/sheets-hyper-link": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-numfmt": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-numfmt": "0.6.10", "@univerjs/sheets": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-numfmt-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-numfmt": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-numfmt": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-sort": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/sheets": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/sheets-sort-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-sort": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-thread-comment": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/sheets": "0.6.10", "@univerjs/thread-comment": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-thread-comment-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/sheets": "0.6.10", "@univerjs/sheets-thread-comment": "0.6.10", "@univerjs/sheets-ui": "0.6.10", "@univerjs/thread-comment": "0.6.10", "@univerjs/thread-comment-ui": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/engine-formula": "0.6.10", "@univerjs/engine-numfmt": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/protocol": "0.1.45", "@univerjs/sheets": "0.6.10", "@univerjs/telemetry": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/telemetry": {"version": "0.6.10", "dependencies": {"@univerjs/core": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/thread-comment": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/protocol": "0.1.45"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/thread-comment-ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/docs-ui": "0.6.10", "@univerjs/icons": "^0.3.2", "@univerjs/thread-comment": "0.6.10", "@univerjs/ui": "0.6.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/ui": {"version": "0.6.10", "license": "Apache-2.0", "dependencies": {"@univerjs/core": "0.6.10", "@univerjs/design": "0.6.10", "@univerjs/engine-render": "0.6.10", "@univerjs/icons": "^0.3.2", "@wendellhu/redi": "0.17.1", "localforage": "^1.10.0", "rc-notification": "^5.6.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "optionalDependencies": {"vue": ">=3.0.0"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@vant/icons": {"version": "1.8.0", "license": "MIT"}, "node_modules/@vant/popperjs": {"version": "1.3.0", "license": "MIT"}, "node_modules/@vant/use": {"version": "1.6.0", "license": "MIT", "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/@visactor/vchart": {"version": "1.13.12", "license": "MIT", "dependencies": {"@visactor/vdataset": "~0.19.5", "@visactor/vgrammar-core": "0.16.9", "@visactor/vgrammar-hierarchy": "0.16.9", "@visactor/vgrammar-projection": "0.16.9", "@visactor/vgrammar-sankey": "0.16.9", "@visactor/vgrammar-util": "0.16.9", "@visactor/vgrammar-venn": "0.16.9", "@visactor/vgrammar-wordcloud": "0.16.9", "@visactor/vgrammar-wordcloud-shape": "0.16.9", "@visactor/vrender-components": "0.22.14", "@visactor/vrender-core": "0.22.14", "@visactor/vrender-kits": "0.22.14", "@visactor/vscale": "~0.19.5", "@visactor/vutils": "~0.19.5", "@visactor/vutils-extension": "1.13.12"}}, "node_modules/@visactor/vdataset": {"version": "0.19.6", "license": "MIT", "dependencies": {"@turf/flatten": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/rewind": "^6.5.0", "@visactor/vutils": "0.19.6", "d3-dsv": "^2.0.0", "d3-geo": "^1.12.1", "d3-hexbin": "^0.2.2", "d3-hierarchy": "^3.1.1", "eventemitter3": "^4.0.7", "geobuf": "^3.0.1", "geojson-dissolve": "^3.1.0", "path-browserify": "^1.0.1", "pbf": "^3.2.1", "point-at-length": "^1.1.0", "simple-statistics": "^7.7.3", "simplify-geojson": "^1.0.4", "topojson-client": "^3.1.0"}}, "node_modules/@visactor/vgrammar-coordinate": {"version": "0.16.9", "license": "MIT", "dependencies": {"@visactor/vgrammar-util": "0.16.9", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vgrammar-core": {"version": "0.16.9", "license": "MIT", "dependencies": {"@visactor/vdataset": "~0.19.5", "@visactor/vgrammar-coordinate": "0.16.9", "@visactor/vgrammar-util": "0.16.9", "@visactor/vrender-components": "0.22.14", "@visactor/vrender-core": "0.22.14", "@visactor/vrender-kits": "0.22.14", "@visactor/vscale": "~0.19.5", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vgrammar-hierarchy": {"version": "0.16.9", "license": "MIT", "dependencies": {"@visactor/vgrammar-core": "0.16.9", "@visactor/vgrammar-util": "0.16.9", "@visactor/vrender-core": "0.22.14", "@visactor/vrender-kits": "0.22.14", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vgrammar-projection": {"version": "0.16.9", "license": "MIT", "dependencies": {"@visactor/vgrammar-core": "0.16.9", "@visactor/vgrammar-util": "0.16.9", "@visactor/vutils": "~0.19.5", "d3-geo": "^1.12.1"}}, "node_modules/@visactor/vgrammar-sankey": {"version": "0.16.9", "license": "MIT", "dependencies": {"@visactor/vgrammar-core": "0.16.9", "@visactor/vgrammar-util": "0.16.9", "@visactor/vrender-core": "0.22.14", "@visactor/vrender-kits": "0.22.14", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vgrammar-util": {"version": "0.16.9", "license": "MIT", "dependencies": {"@visactor/vrender-core": "0.22.14", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vgrammar-venn": {"version": "0.16.9", "license": "MIT", "dependencies": {"@visactor/vgrammar-core": "0.16.9", "@visactor/vgrammar-util": "0.16.9", "@visactor/vrender-core": "0.22.14", "@visactor/vrender-kits": "0.22.14", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vgrammar-wordcloud": {"version": "0.16.9", "license": "MIT", "dependencies": {"@visactor/vgrammar-core": "0.16.9", "@visactor/vgrammar-util": "0.16.9", "@visactor/vrender-core": "0.22.14", "@visactor/vrender-kits": "0.22.14", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vgrammar-wordcloud-shape": {"version": "0.16.9", "license": "MIT", "dependencies": {"@visactor/vgrammar-core": "0.16.9", "@visactor/vgrammar-util": "0.16.9", "@visactor/vrender-core": "0.22.14", "@visactor/vrender-kits": "0.22.14", "@visactor/vscale": "~0.19.5", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vrender-components": {"version": "0.22.14", "license": "MIT", "dependencies": {"@visactor/vrender-core": "0.22.14", "@visactor/vrender-kits": "0.22.14", "@visactor/vscale": "~0.19.5", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vrender-core": {"version": "0.22.14", "license": "MIT", "dependencies": {"@visactor/vutils": "~0.19.5", "color-convert": "2.0.1"}}, "node_modules/@visactor/vrender-kits": {"version": "0.22.14", "license": "MIT", "dependencies": {"@resvg/resvg-js": "2.4.1", "@visactor/vrender-core": "0.22.14", "@visactor/vutils": "~0.19.5", "gifuct-js": "2.1.2", "lottie-web": "^5.12.2", "roughjs": "4.5.2"}}, "node_modules/@visactor/vscale": {"version": "0.19.6", "license": "MIT", "dependencies": {"@visactor/vutils": "0.19.6"}}, "node_modules/@visactor/vtable": {"version": "1.18.5", "license": "MIT", "dependencies": {"@visactor/vdataset": "~0.18.1", "@visactor/vrender-components": "0.22.11", "@visactor/vrender-core": "0.22.11", "@visactor/vrender-kits": "0.22.11", "@visactor/vscale": "~0.18.1", "@visactor/vtable-editors": "1.18.5", "@visactor/vutils": "~0.19.1", "@visactor/vutils-extension": "~1.11.5", "cssfontparser": "^1.2.1", "gifuct-js": "2.1.2", "lodash": "4.17.21"}}, "node_modules/@visactor/vtable-editors": {"version": "1.18.5", "license": "MIT"}, "node_modules/@visactor/vtable-export": {"version": "1.18.5", "license": "MIT", "dependencies": {"@types/file-saver": "2.0.7", "@visactor/vtable": "1.18.5", "@visactor/vutils": "~0.19.1", "exceljs": "4.4.0", "file-saver": "2.0.5"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vdataset": {"version": "0.18.18", "license": "MIT", "dependencies": {"@turf/flatten": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/rewind": "^6.5.0", "@visactor/vutils": "0.18.18", "d3-dsv": "^2.0.0", "d3-geo": "^1.12.1", "d3-hexbin": "^0.2.2", "d3-hierarchy": "^3.1.1", "eventemitter3": "^4.0.7", "geobuf": "^3.0.1", "geojson-dissolve": "^3.1.0", "path-browserify": "^1.0.1", "pbf": "^3.2.1", "point-at-length": "^1.1.0", "simple-statistics": "^7.7.3", "simplify-geojson": "^1.0.4", "topojson-client": "^3.1.0"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vdataset/node_modules/@visactor/vutils": {"version": "0.18.18", "license": "MIT", "dependencies": {"@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0", "eventemitter3": "^4.0.7"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vrender-components": {"version": "0.22.11", "license": "MIT", "dependencies": {"@visactor/vrender-core": "0.22.11", "@visactor/vrender-kits": "0.22.11", "@visactor/vscale": "~0.19.5", "@visactor/vutils": "~0.19.5"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vrender-components/node_modules/@visactor/vscale": {"version": "0.19.6", "license": "MIT", "dependencies": {"@visactor/vutils": "0.19.6"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vrender-core": {"version": "0.22.11", "license": "MIT", "dependencies": {"@visactor/vutils": "~0.19.5", "color-convert": "2.0.1"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vrender-kits": {"version": "0.22.11", "license": "MIT", "dependencies": {"@resvg/resvg-js": "2.4.1", "@visactor/vrender-core": "0.22.11", "@visactor/vutils": "~0.19.5", "gifuct-js": "2.1.2", "lottie-web": "^5.12.2", "roughjs": "4.5.2"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vscale": {"version": "0.18.18", "license": "MIT", "dependencies": {"@visactor/vutils": "0.18.18"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vscale/node_modules/@visactor/vutils": {"version": "0.18.18", "license": "MIT", "dependencies": {"@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0", "eventemitter3": "^4.0.7"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vutils-extension": {"version": "1.11.14", "license": "MIT", "dependencies": {"@visactor/vdataset": "~0.18.10", "@visactor/vutils": "~0.18.10"}}, "node_modules/@visactor/vtable/node_modules/@visactor/vutils-extension/node_modules/@visactor/vutils": {"version": "0.18.18", "license": "MIT", "dependencies": {"@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0", "eventemitter3": "^4.0.7"}}, "node_modules/@visactor/vue-vtable": {"version": "1.18.5", "license": "MIT", "dependencies": {"@visactor/vtable": "1.18.5", "@visactor/vutils": "~0.19.1"}}, "node_modules/@visactor/vutils": {"version": "0.19.6", "license": "MIT", "dependencies": {"@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0", "eventemitter3": "^4.0.7"}}, "node_modules/@visactor/vutils-extension": {"version": "1.13.12", "license": "MIT", "dependencies": {"@visactor/vdataset": "~0.19.5", "@visactor/vutils": "~0.19.5"}}, "node_modules/@vitejs/plugin-vue": {"version": "5.2.4", "dev": true, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0", "vue": "^3.2.25"}}, "node_modules/@vue/babel-helper-vue-transform-on": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/@vue/babel-plugin-jsx": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.9", "@babel/types": "^7.26.9", "@vue/babel-helper-vue-transform-on": "1.4.0", "@vue/babel-plugin-resolve-type": "1.4.0", "@vue/shared": "^3.5.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}}}, "node_modules/@vue/babel-plugin-resolve-type": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/parser": "^7.26.9", "@vue/compiler-sfc": "^3.5.13"}, "funding": {"url": "https://github.com/sponsors/sxzz"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/compiler-core": {"version": "3.5.16", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.2", "@vue/shared": "3.5.16", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.16", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.16", "@vue/shared": "3.5.16"}}, "node_modules/@vue/compiler-sfc": {"version": "3.5.16", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.2", "@vue/compiler-core": "3.5.16", "@vue/compiler-dom": "3.5.16", "@vue/compiler-ssr": "3.5.16", "@vue/shared": "3.5.16", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.3", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.16", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.16", "@vue/shared": "3.5.16"}}, "node_modules/@vue/devtools-api": {"version": "6.6.4", "license": "MIT"}, "node_modules/@vue/devtools-core": {"version": "7.7.6", "dev": true, "license": "MIT", "dependencies": {"@vue/devtools-kit": "^7.7.6", "@vue/devtools-shared": "^7.7.6", "mitt": "^3.0.1", "nanoid": "^5.1.0", "pathe": "^2.0.3", "vite-hot-client": "^2.0.4"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/@vue/devtools-kit": {"version": "7.7.6", "dev": true, "license": "MIT", "dependencies": {"@vue/devtools-shared": "^7.7.6", "birpc": "^2.3.0", "hookable": "^5.5.3", "mitt": "^3.0.1", "perfect-debounce": "^1.0.0", "speakingurl": "^14.0.1", "superjson": "^2.2.2"}}, "node_modules/@vue/devtools-shared": {"version": "7.7.6", "dev": true, "license": "MIT", "dependencies": {"rfdc": "^1.4.1"}}, "node_modules/@vue/reactivity": {"version": "3.5.16", "license": "MIT", "dependencies": {"@vue/shared": "3.5.16"}}, "node_modules/@vue/runtime-core": {"version": "3.5.16", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.16", "@vue/shared": "3.5.16"}}, "node_modules/@vue/runtime-dom": {"version": "3.5.16", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.16", "@vue/runtime-core": "3.5.16", "@vue/shared": "3.5.16", "csstype": "^3.1.3"}}, "node_modules/@vue/server-renderer": {"version": "3.5.16", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.5.16", "@vue/shared": "3.5.16"}, "peerDependencies": {"vue": "3.5.16"}}, "node_modules/@vue/shared": {"version": "3.5.16", "license": "MIT"}, "node_modules/@vuepress/markdown": {"version": "1.9.10", "license": "MIT", "dependencies": {"@vuepress/shared-utils": "1.9.10", "markdown-it": "^8.4.1", "markdown-it-anchor": "^5.0.2", "markdown-it-chain": "^1.3.0", "markdown-it-emoji": "^1.4.0", "markdown-it-table-of-contents": "^0.4.0", "prismjs": "^1.13.0"}}, "node_modules/@vuepress/markdown/node_modules/entities": {"version": "1.1.2", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@vuepress/markdown/node_modules/linkify-it": {"version": "2.2.0", "license": "MIT", "dependencies": {"uc.micro": "^1.0.1"}}, "node_modules/@vuepress/markdown/node_modules/markdown-it": {"version": "8.4.2", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "entities": "~1.1.1", "linkify-it": "^2.0.0", "mdurl": "^1.0.1", "uc.micro": "^1.0.5"}, "bin": {"markdown-it": "bin/markdown-it.js"}}, "node_modules/@vuepress/shared-utils": {"version": "1.9.10", "license": "MIT", "dependencies": {"chalk": "^2.3.2", "escape-html": "^1.0.3", "fs-extra": "^7.0.1", "globby": "^9.2.0", "gray-matter": "^4.0.1", "hash-sum": "^1.0.2", "semver": "^6.0.0", "toml": "^3.0.0", "upath": "^1.1.0"}}, "node_modules/@vueuse/core": {"version": "10.11.1", "license": "MIT", "dependencies": {"@types/web-bluetooth": "^0.0.20", "@vueuse/metadata": "10.11.1", "@vueuse/shared": "10.11.1", "vue-demi": ">=0.14.8"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/core/node_modules/vue-demi": {"version": "0.14.10", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vueuse/metadata": {"version": "10.11.1", "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared": {"version": "10.11.1", "license": "MIT", "dependencies": {"vue-demi": ">=0.14.8"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared/node_modules/vue-demi": {"version": "0.14.10", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@wendellhu/redi": {"version": "0.17.1", "license": "MIT"}, "node_modules/abs-svg-path": {"version": "0.1.1", "license": "MIT"}, "node_modules/acorn": {"version": "8.15.0", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/ansi-styles/node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/ansi-styles/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/archiver": {"version": "5.3.2", "license": "MIT", "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "engines": {"node": ">= 10"}}, "node_modules/archiver-utils": {"version": "2.1.0", "license": "MIT", "dependencies": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "engines": {"node": ">= 6"}}, "node_modules/archiver-utils/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/archiver-utils/node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/archiver-utils/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/archiver-utils/node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/argparse": {"version": "1.0.10", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/aria-hidden": {"version": "1.2.6", "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/arr-diff": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-flatten": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-union": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-source": {"version": "0.0.4", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/array-union": {"version": "1.0.2", "license": "MIT", "dependencies": {"array-uniq": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-uniq": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-unique": {"version": "0.3.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/assign-symbols": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/async": {"version": "3.2.6", "license": "MIT"}, "node_modules/async-lock": {"version": "1.4.1", "license": "MIT"}, "node_modules/async-validator": {"version": "4.2.5", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/atob": {"version": "2.1.2", "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/axios": {"version": "1.9.0", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/base": {"version": "0.11.2", "license": "MIT", "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/big-integer": {"version": "1.6.52", "license": "Unlicense", "engines": {"node": ">=0.6"}}, "node_modules/binary": {"version": "0.3.0", "license": "MIT", "dependencies": {"buffers": "~0.1.1", "chainsaw": "~0.1.0"}}, "node_modules/birpc": {"version": "2.3.0", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/bl": {"version": "4.1.0", "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/bluebird": {"version": "3.4.7", "license": "MIT"}, "node_modules/brace-expansion": {"version": "1.1.12", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "2.3.2", "license": "MIT", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/braces/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/braces/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/browserslist": {"version": "4.25.0", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-builder": {"version": "0.2.0", "dev": true, "license": "MIT/X11"}, "node_modules/buffer-crc32": {"version": "0.2.13", "license": "MIT", "engines": {"node": "*"}}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/buffer-indexof-polyfill": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/buffers": {"version": "0.1.1", "engines": {"node": ">=0.2.0"}}, "node_modules/bundle-name": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"run-applescript": "^7.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cache-base": {"version": "1.0.1", "license": "MIT", "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-me-maybe": {"version": "1.0.2", "license": "MIT"}, "node_modules/caniuse-lite": {"version": "1.0.30001723", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chainsaw": {"version": "0.1.0", "license": "MIT/X11", "dependencies": {"traverse": ">=0.3.0 <0.4"}}, "node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/character-entities": {"version": "2.0.2", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/cjk-regex": {"version": "3.3.0", "license": "MIT", "dependencies": {"regexp-util": "^2.0.1", "unicode-regex": "^4.1.0"}, "engines": {"node": ">=16"}}, "node_modules/class-utils": {"version": "0.3.6", "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/class-variance-authority": {"version": "0.7.1", "license": "Apache-2.0", "dependencies": {"clsx": "^2.1.1"}, "funding": {"url": "https://polar.sh/cva"}}, "node_modules/classnames": {"version": "2.5.1", "license": "MIT"}, "node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/clsx": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/codemirror": {"version": "5.65.19", "license": "MIT"}, "node_modules/collapse-white-space": {"version": "2.1.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/collection-visit": {"version": "1.0.0", "license": "MIT", "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/colorjs.io": {"version": "0.5.2", "dev": true, "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "node_modules/component-emitter": {"version": "1.3.1", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/compress-commons": {"version": "4.1.2", "license": "MIT", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.2", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 10"}}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/concat-stream": {"version": "2.0.0", "engines": ["node >= 6.0"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.0.2", "typedarray": "^0.0.6"}}, "node_modules/convert-source-map": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/copy-anything": {"version": "3.0.5", "dev": true, "license": "MIT", "dependencies": {"is-what": "^4.1.8"}, "engines": {"node": ">=12.13"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/copy-descriptor": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/copy-to-clipboard": {"version": "3.3.3", "license": "MIT", "dependencies": {"toggle-selection": "^1.0.6"}}, "node_modules/core-util-is": {"version": "1.0.3", "license": "MIT"}, "node_modules/cose-base": {"version": "1.0.3", "license": "MIT", "dependencies": {"layout-base": "^1.0.0"}}, "node_modules/crc-32": {"version": "1.2.2", "license": "Apache-2.0", "bin": {"crc32": "bin/crc32.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/crc32-stream": {"version": "4.0.3", "license": "MIT", "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "engines": {"node": ">= 10"}}, "node_modules/cross-spawn": {"version": "7.0.6", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypto-js": {"version": "4.2.0", "license": "MIT"}, "node_modules/css-box-model": {"version": "1.2.1", "license": "MIT", "dependencies": {"tiny-invariant": "^1.0.6"}}, "node_modules/cssfilter": {"version": "0.0.10", "license": "MIT"}, "node_modules/cssfontparser": {"version": "1.2.1", "license": "MIT"}, "node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "node_modules/cytoscape": {"version": "3.32.0", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/cytoscape-cose-bilkent": {"version": "4.1.0", "license": "MIT", "dependencies": {"cose-base": "^1.0.0"}, "peerDependencies": {"cytoscape": "^3.2.0"}}, "node_modules/d3": {"version": "7.9.0", "license": "ISC", "dependencies": {"d3-array": "3", "d3-axis": "3", "d3-brush": "3", "d3-chord": "3", "d3-color": "3", "d3-contour": "4", "d3-delaunay": "6", "d3-dispatch": "3", "d3-drag": "3", "d3-dsv": "3", "d3-ease": "3", "d3-fetch": "3", "d3-force": "3", "d3-format": "3", "d3-geo": "3", "d3-hierarchy": "3", "d3-interpolate": "3", "d3-path": "3", "d3-polygon": "3", "d3-quadtree": "3", "d3-random": "3", "d3-scale": "4", "d3-scale-chromatic": "3", "d3-selection": "3", "d3-shape": "3", "d3-time": "3", "d3-time-format": "4", "d3-timer": "3", "d3-transition": "3", "d3-zoom": "3"}, "engines": {"node": ">=12"}}, "node_modules/d3-array": {"version": "1.2.4", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-axis": {"version": "3.0.0", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-brush": {"version": "3.0.0", "license": "ISC", "dependencies": {"d3-dispatch": "1 - 3", "d3-drag": "2 - 3", "d3-interpolate": "1 - 3", "d3-selection": "3", "d3-transition": "3"}, "engines": {"node": ">=12"}}, "node_modules/d3-chord": {"version": "3.0.1", "license": "ISC", "dependencies": {"d3-path": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-color": {"version": "3.1.0", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-contour": {"version": "4.0.2", "license": "ISC", "dependencies": {"d3-array": "^3.2.0"}, "engines": {"node": ">=12"}}, "node_modules/d3-contour/node_modules/d3-array": {"version": "3.2.4", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/d3-delaunay": {"version": "6.0.4", "license": "ISC", "dependencies": {"delaunator": "5"}, "engines": {"node": ">=12"}}, "node_modules/d3-dispatch": {"version": "3.0.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-drag": {"version": "3.0.0", "license": "ISC", "dependencies": {"d3-dispatch": "1 - 3", "d3-selection": "3"}, "engines": {"node": ">=12"}}, "node_modules/d3-dsv": {"version": "2.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"commander": "2", "iconv-lite": "0.4", "rw": "1"}, "bin": {"csv2json": "bin/dsv2json", "csv2tsv": "bin/dsv2dsv", "dsv2dsv": "bin/dsv2dsv", "dsv2json": "bin/dsv2json", "json2csv": "bin/json2dsv", "json2dsv": "bin/json2dsv", "json2tsv": "bin/json2dsv", "tsv2csv": "bin/dsv2dsv", "tsv2json": "bin/dsv2json"}}, "node_modules/d3-ease": {"version": "3.0.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/d3-fetch": {"version": "3.0.1", "license": "ISC", "dependencies": {"d3-dsv": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-force": {"version": "3.0.0", "license": "ISC", "dependencies": {"d3-dispatch": "1 - 3", "d3-quadtree": "1 - 3", "d3-timer": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-format": {"version": "3.1.0", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-geo": {"version": "1.12.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "1"}}, "node_modules/d3-hexbin": {"version": "0.2.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-hierarchy": {"version": "3.1.2", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-interpolate": {"version": "3.0.1", "license": "ISC", "dependencies": {"d3-color": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-path": {"version": "3.1.0", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-polygon": {"version": "3.0.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-quadtree": {"version": "3.0.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-random": {"version": "3.0.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-sankey": {"version": "0.12.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "1 - 2", "d3-shape": "^1.2.0"}}, "node_modules/d3-sankey/node_modules/d3-path": {"version": "1.0.9", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-sankey/node_modules/d3-shape": {"version": "1.3.7", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-path": "1"}}, "node_modules/d3-scale": {"version": "4.0.2", "license": "ISC", "dependencies": {"d3-array": "2.10.0 - 3", "d3-format": "1 - 3", "d3-interpolate": "1.2.0 - 3", "d3-time": "2.1.1 - 3", "d3-time-format": "2 - 4"}, "engines": {"node": ">=12"}}, "node_modules/d3-scale-chromatic": {"version": "3.1.0", "license": "ISC", "dependencies": {"d3-color": "1 - 3", "d3-interpolate": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-scale/node_modules/d3-array": {"version": "3.2.4", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/d3-selection": {"version": "3.0.0", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-shape": {"version": "3.2.0", "license": "ISC", "dependencies": {"d3-path": "^3.1.0"}, "engines": {"node": ">=12"}}, "node_modules/d3-time": {"version": "3.1.0", "license": "ISC", "dependencies": {"d3-array": "2 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-time-format": {"version": "4.1.0", "license": "ISC", "dependencies": {"d3-time": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-time/node_modules/d3-array": {"version": "3.2.4", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/d3-timer": {"version": "3.0.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-transition": {"version": "3.0.1", "license": "ISC", "dependencies": {"d3-color": "1 - 3", "d3-dispatch": "1 - 3", "d3-ease": "1 - 3", "d3-interpolate": "1 - 3", "d3-timer": "1 - 3"}, "engines": {"node": ">=12"}, "peerDependencies": {"d3-selection": "2 - 3"}}, "node_modules/d3-zoom": {"version": "3.0.0", "license": "ISC", "dependencies": {"d3-dispatch": "1 - 3", "d3-drag": "2 - 3", "d3-interpolate": "1 - 3", "d3-selection": "2 - 3", "d3-transition": "2 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3/node_modules/commander": {"version": "7.2.0", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/d3/node_modules/d3-array": {"version": "3.2.4", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/d3/node_modules/d3-dsv": {"version": "3.0.1", "license": "ISC", "dependencies": {"commander": "7", "iconv-lite": "0.6", "rw": "1"}, "bin": {"csv2json": "bin/dsv2json.js", "csv2tsv": "bin/dsv2dsv.js", "dsv2dsv": "bin/dsv2dsv.js", "dsv2json": "bin/dsv2json.js", "json2csv": "bin/json2dsv.js", "json2dsv": "bin/json2dsv.js", "json2tsv": "bin/json2dsv.js", "tsv2csv": "bin/dsv2dsv.js", "tsv2json": "bin/dsv2json.js"}, "engines": {"node": ">=12"}}, "node_modules/d3/node_modules/d3-geo": {"version": "3.1.1", "license": "ISC", "dependencies": {"d3-array": "2.5.0 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3/node_modules/iconv-lite": {"version": "0.6.3", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/dagre-d3-es": {"version": "7.0.10", "license": "MIT", "dependencies": {"d3": "^7.8.2", "lodash-es": "^4.17.21"}}, "node_modules/dayjs": {"version": "1.11.13", "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js": {"version": "10.5.0", "license": "MIT"}, "node_modules/decode-named-character-reference": {"version": "1.1.0", "license": "MIT", "dependencies": {"character-entities": "^2.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/decode-uri-component": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/deepmerge": {"version": "1.5.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/default-browser": {"version": "5.2.1", "dev": true, "license": "MIT", "dependencies": {"bundle-name": "^4.1.0", "default-browser-id": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser-id": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/delaunator": {"version": "5.0.1", "license": "ISC", "dependencies": {"robust-predicates": "^3.0.2"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/dequal": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/detect-node-es": {"version": "1.1.0", "license": "MIT"}, "node_modules/diff": {"version": "5.2.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/dir-glob": {"version": "2.2.2", "license": "MIT", "dependencies": {"path-type": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/dom-helpers": {"version": "5.2.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/dompurify": {"version": "3.1.6", "license": "(MPL-2.0 OR Apache-2.0)"}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/duplexer2": {"version": "0.1.4", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"readable-stream": "^2.0.2"}}, "node_modules/duplexer2/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/duplexer2/node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/duplexer2/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/duplexer2/node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/echarts": {"version": "5.6.0", "license": "Apache-2.0", "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}}, "node_modules/echarts-wordcloud": {"version": "2.1.0", "license": "ISC", "peerDependencies": {"echarts": "^5.0.1"}}, "node_modules/echarts/node_modules/tslib": {"version": "2.3.0", "license": "0BSD"}, "node_modules/electron-to-chromium": {"version": "1.5.167", "dev": true, "license": "ISC"}, "node_modules/element-plus": {"version": "2.10.1", "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.4.1", "@element-plus/icons-vue": "^2.3.1", "@floating-ui/dom": "^1.0.1", "@popperjs/core": "npm:@sxzz/popperjs-es@^2.11.7", "@types/lodash": "^4.14.182", "@types/lodash-es": "^4.17.6", "@vueuse/core": "^9.1.0", "async-validator": "^4.2.5", "dayjs": "^1.11.13", "escape-html": "^1.0.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "memoize-one": "^6.0.0", "normalize-wheel-es": "^1.2.0"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/element-plus/node_modules/@types/web-bluetooth": {"version": "0.0.16", "license": "MIT"}, "node_modules/element-plus/node_modules/@vueuse/core": {"version": "9.13.0", "license": "MIT", "dependencies": {"@types/web-bluetooth": "^0.0.16", "@vueuse/metadata": "9.13.0", "@vueuse/shared": "9.13.0", "vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/element-plus/node_modules/@vueuse/metadata": {"version": "9.13.0", "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/element-plus/node_modules/@vueuse/shared": {"version": "9.13.0", "license": "MIT", "dependencies": {"vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/elkjs": {"version": "0.9.3", "license": "EPL-2.0"}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/end-of-stream": {"version": "1.4.4", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/error-stack-parser-es": {"version": "0.1.5", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/esbuild": {"version": "0.25.5", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "node_modules/esbuild/node_modules/@esbuild/aix-ppc64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz", "integrity": "sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/android-arm": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.5.tgz", "integrity": "sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/android-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz", "integrity": "sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/android-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.5.tgz", "integrity": "sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/darwin-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz", "integrity": "sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/darwin-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz", "integrity": "sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/freebsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz", "integrity": "sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/freebsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz", "integrity": "sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-arm": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz", "integrity": "sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz", "integrity": "sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-ia32": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz", "integrity": "sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-loong64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz", "integrity": "sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-mips64el": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz", "integrity": "sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==", "cpu": ["mips64el"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-ppc64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz", "integrity": "sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-riscv64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz", "integrity": "sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-s390x": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz", "integrity": "sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz", "integrity": "sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/netbsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz", "integrity": "sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/netbsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz", "integrity": "sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/openbsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz", "integrity": "sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/openbsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz", "integrity": "sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/sunos-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz", "integrity": "sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/win32-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz", "integrity": "sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/win32-ia32": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz", "integrity": "sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "node_modules/eventemitter3": {"version": "4.0.7", "license": "MIT"}, "node_modules/exceljs": {"version": "4.4.0", "license": "MIT", "dependencies": {"archiver": "^5.0.0", "dayjs": "^1.8.34", "fast-csv": "^4.3.1", "jszip": "^3.10.1", "readable-stream": "^3.6.0", "saxes": "^5.0.1", "tmp": "^0.2.0", "unzipper": "^0.10.11", "uuid": "^8.3.0"}, "engines": {"node": ">=8.3.0"}}, "node_modules/exceljs/node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/execa": {"version": "9.6.0", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/merge-streams": "^4.0.0", "cross-spawn": "^7.0.6", "figures": "^6.1.0", "get-stream": "^9.0.0", "human-signals": "^8.0.1", "is-plain-obj": "^4.1.0", "is-stream": "^4.0.1", "npm-run-path": "^6.0.0", "pretty-ms": "^9.2.0", "signal-exit": "^4.1.0", "strip-final-newline": "^4.0.0", "yoctocolors": "^2.1.1"}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/expand-brackets": {"version": "2.1.4", "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/expand-brackets/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob": {"version": "2.0.4", "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/fast-csv": {"version": "4.3.6", "license": "MIT", "dependencies": {"@fast-csv/format": "4.3.5", "@fast-csv/parse": "4.3.6"}, "engines": {"node": ">=10.0.0"}}, "node_modules/fast-diff": {"version": "1.3.0", "license": "Apache-2.0"}, "node_modules/fast-equals": {"version": "4.0.3", "license": "MIT"}, "node_modules/fast-glob": {"version": "2.2.7", "license": "MIT", "dependencies": {"@mrmlnc/readdir-enhanced": "^2.2.1", "@nodelib/fs.stat": "^1.1.2", "glob-parent": "^3.1.0", "is-glob": "^4.0.0", "merge2": "^1.2.3", "micromatch": "^3.1.10"}, "engines": {"node": ">=4.0.0"}}, "node_modules/fdir": {"version": "6.4.6", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/figures": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"is-unicode-supported": "^2.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/file-saver": {"version": "2.0.5", "license": "MIT"}, "node_modules/file-source": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"stream-source": "0.3"}}, "node_modules/fill-range": {"version": "4.0.0", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fill-range/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fill-range/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/flatpickr": {"version": "4.6.13", "license": "MIT"}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-in": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/form-data": {"version": "4.0.3", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/fragment-cache": {"version": "0.2.1", "license": "MIT", "dependencies": {"map-cache": "^0.2.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/franc-min": {"version": "6.2.0", "license": "MIT", "dependencies": {"trigram-utils": "^2.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/fs-constants": {"version": "1.0.0", "license": "MIT"}, "node_modules/fs-extra": {"version": "7.0.1", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/fstream": {"version": "1.0.12", "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "inherits": "~2.0.0", "mkdirp": ">=0.5 0", "rimraf": "2"}, "engines": {"node": ">=0.6"}}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/geobuf": {"version": "3.0.2", "license": "ISC", "dependencies": {"concat-stream": "^2.0.0", "pbf": "^3.2.1", "shapefile": "~0.6.6"}, "bin": {"geobuf2json": "bin/geobuf2json", "json2geobuf": "bin/json2geobuf", "shp2geobuf": "bin/shp2geobuf"}}, "node_modules/geojson-dissolve": {"version": "3.1.0", "license": "ISC", "dependencies": {"@turf/meta": "^3.7.5", "geojson-flatten": "^0.2.1", "geojson-linestring-dissolve": "0.0.1", "topojson-client": "^3.0.0", "topojson-server": "^3.0.0"}}, "node_modules/geojson-dissolve/node_modules/@turf/meta": {"version": "3.14.0", "license": "MIT"}, "node_modules/geojson-flatten": {"version": "0.2.4", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"get-stdin": "^6.0.0", "minimist": "1.2.0"}, "bin": {"geojson-flatten": "geo<PERSON><PERSON>-flatten"}}, "node_modules/geojson-linestring-dissolve": {"version": "0.0.1", "license": "ISC"}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-nonce": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stdin": {"version": "6.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/get-stream": {"version": "9.0.1", "dev": true, "license": "MIT", "dependencies": {"@sec-ant/readable-stream": "^0.4.1", "is-stream": "^4.0.1"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-value": {"version": "2.0.6", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/gifuct-js": {"version": "2.1.2", "license": "MIT", "dependencies": {"js-binary-schema-parser": "^2.0.3"}}, "node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "3.1.0", "license": "ISC", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/glob-parent/node_modules/is-glob": {"version": "3.1.0", "license": "MIT", "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-to-regexp": {"version": "0.3.0", "license": "BSD"}, "node_modules/globals": {"version": "11.12.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/globby": {"version": "9.2.0", "license": "MIT", "dependencies": {"@types/glob": "^7.1.1", "array-union": "^1.0.2", "dir-glob": "^2.2.2", "fast-glob": "^2.2.6", "glob": "^7.1.3", "ignore": "^4.0.3", "pify": "^4.0.1", "slash": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/gray-matter": {"version": "4.0.3", "license": "MIT", "dependencies": {"js-yaml": "^3.13.1", "kind-of": "^6.0.2", "section-matter": "^1.0.0", "strip-bom-string": "^1.0.0"}, "engines": {"node": ">=6.0"}}, "node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-value": {"version": "1.0.0", "license": "MIT", "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/kind-of": {"version": "4.0.0", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hash-sum": {"version": "1.0.2", "license": "MIT"}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/highlight.js": {"version": "10.7.3", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/hoist-non-react-statics/node_modules/react-is": {"version": "16.13.1", "license": "MIT"}, "node_modules/hookable": {"version": "5.5.3", "dev": true, "license": "MIT"}, "node_modules/human-signals": {"version": "8.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "4.0.6", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/immediate": {"version": "3.0.6", "license": "MIT"}, "node_modules/immutable": {"version": "5.1.3", "dev": true, "license": "MIT"}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/insert-text-at-cursor": {"version": "0.3.0", "license": "MIT"}, "node_modules/internmap": {"version": "2.0.3", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/is-accessor-descriptor": {"version": "1.0.1", "license": "MIT", "dependencies": {"hasown": "^2.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/is-buffer": {"version": "1.1.6", "license": "MIT"}, "node_modules/is-data-descriptor": {"version": "1.0.1", "license": "MIT", "dependencies": {"hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-descriptor": {"version": "1.0.3", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-docker": {"version": "2.2.1", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-inside-container": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^3.0.0"}, "bin": {"is-inside-container": "cli.js"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-inside-container/node_modules/is-docker": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-number": {"version": "3.0.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-obj": {"version": "4.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-plain-object": {"version": "2.0.4", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-stream": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-unicode-supported": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-what": {"version": "4.1.16", "dev": true, "license": "MIT", "engines": {"node": ">=12.13"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/is-windows": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-wsl": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "0.0.1", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/javascript-stringify": {"version": "1.6.0", "license": "MIT"}, "node_modules/jquery": {"version": "2.2.4", "license": "MIT"}, "node_modules/js-binary-schema-parser": {"version": "2.0.3", "license": "MIT"}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "3.1.0", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json5": {"version": "2.2.3", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "4.0.0", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jszip": {"version": "3.10.1", "license": "(MIT OR GPL-3.0-or-later)", "dependencies": {"lie": "~3.3.0", "pako": "~1.0.2", "readable-stream": "~2.3.6", "setimmediate": "^1.0.5"}}, "node_modules/jszip/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/jszip/node_modules/pako": {"version": "1.0.11", "license": "(MIT AND Zlib)"}, "node_modules/jszip/node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/jszip/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/jszip/node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/katex": {"version": "0.13.24", "funding": ["https://opencollective.com/katex", "https://github.com/sponsors/katex"], "license": "MIT", "dependencies": {"commander": "^8.0.0"}, "bin": {"katex": "cli.js"}}, "node_modules/katex/node_modules/commander": {"version": "8.3.0", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/kdbush": {"version": "4.0.2", "license": "ISC"}, "node_modules/khroma": {"version": "2.1.0"}, "node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/kleur": {"version": "4.1.5", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/kolorist": {"version": "1.8.0", "dev": true, "license": "MIT"}, "node_modules/layout-base": {"version": "1.0.2", "license": "MIT"}, "node_modules/lazystream": {"version": "1.0.1", "license": "MIT", "dependencies": {"readable-stream": "^2.0.5"}, "engines": {"node": ">= 0.6.3"}}, "node_modules/lazystream/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/lazystream/node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/lazystream/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/lazystream/node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/lie": {"version": "3.3.0", "license": "MIT", "dependencies": {"immediate": "~3.0.5"}}, "node_modules/linkify-it": {"version": "3.0.3", "license": "MIT", "dependencies": {"uc.micro": "^1.0.1"}}, "node_modules/listenercount": {"version": "1.0.1", "license": "ISC"}, "node_modules/localforage": {"version": "1.10.0", "license": "Apache-2.0", "dependencies": {"lie": "3.1.1"}}, "node_modules/localforage/node_modules/lie": {"version": "3.1.1", "license": "MIT", "dependencies": {"immediate": "~3.0.5"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash-unified": {"version": "1.0.3", "license": "MIT", "peerDependencies": {"@types/lodash-es": "*", "lodash": "*", "lodash-es": "*"}}, "node_modules/lodash.camelcase": {"version": "4.3.0", "license": "MIT", "peer": true}, "node_modules/lodash.defaults": {"version": "4.2.0", "license": "MIT"}, "node_modules/lodash.difference": {"version": "4.5.0", "license": "MIT"}, "node_modules/lodash.escaperegexp": {"version": "4.1.2", "license": "MIT"}, "node_modules/lodash.flatten": {"version": "4.4.0", "license": "MIT"}, "node_modules/lodash.groupby": {"version": "4.6.0", "license": "MIT"}, "node_modules/lodash.isboolean": {"version": "3.0.3", "license": "MIT"}, "node_modules/lodash.isequal": {"version": "4.5.0", "license": "MIT"}, "node_modules/lodash.isfunction": {"version": "3.0.9", "license": "MIT"}, "node_modules/lodash.isnil": {"version": "4.0.0", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "license": "MIT"}, "node_modules/lodash.isundefined": {"version": "3.0.1", "license": "MIT"}, "node_modules/lodash.union": {"version": "4.6.0", "license": "MIT"}, "node_modules/lodash.uniq": {"version": "4.5.0", "license": "MIT"}, "node_modules/long": {"version": "5.3.2", "license": "Apache-2.0", "peer": true}, "node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lottie-web": {"version": "5.13.0", "license": "MIT"}, "node_modules/lru-cache": {"version": "5.1.1", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/luckysheet": {"version": "2.1.13", "dependencies": {"@babel/runtime": "^7.12.1", "dayjs": "^1.9.6", "flatpickr": "^4.6.6", "jquery": "^2.2.4", "numeral": "^2.0.6", "pako": "^1.0.11"}}, "node_modules/luckysheet/node_modules/pako": {"version": "1.0.11", "license": "(MIT AND Zlib)"}, "node_modules/magic-string": {"version": "0.30.17", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/map-cache": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/map-visit": {"version": "1.0.0", "license": "MIT", "dependencies": {"object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/markdown-it": {"version": "12.3.2", "license": "MIT", "dependencies": {"argparse": "^2.0.1", "entities": "~2.1.0", "linkify-it": "^3.0.1", "mdurl": "^1.0.1", "uc.micro": "^1.0.5"}, "bin": {"markdown-it": "bin/markdown-it.js"}}, "node_modules/markdown-it-anchor": {"version": "5.3.0", "license": "Unlicense", "peerDependencies": {"markdown-it": "*"}}, "node_modules/markdown-it-attrs": {"version": "4.3.1", "license": "MIT", "engines": {"node": ">=6"}, "peerDependencies": {"markdown-it": ">= 9.0.0"}}, "node_modules/markdown-it-chain": {"version": "1.3.0", "license": "MIT", "dependencies": {"webpack-chain": "^4.9.0"}, "engines": {"node": ">=6.9"}, "peerDependencies": {"markdown-it": ">=5.0.0"}}, "node_modules/markdown-it-container": {"version": "3.0.0", "license": "MIT"}, "node_modules/markdown-it-emoji": {"version": "1.4.0", "license": "MIT"}, "node_modules/markdown-it-table-of-contents": {"version": "0.4.4", "license": "MIT", "engines": {"node": ">6.4.0"}}, "node_modules/markdown-it/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/markdown-it/node_modules/entities": {"version": "2.1.0", "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/mdast-util-from-markdown": {"version": "1.3.1", "license": "MIT", "dependencies": {"@types/mdast": "^3.0.0", "@types/unist": "^2.0.0", "decode-named-character-reference": "^1.0.0", "mdast-util-to-string": "^3.1.0", "micromark": "^3.0.0", "micromark-util-decode-numeric-character-reference": "^1.0.0", "micromark-util-decode-string": "^1.0.0", "micromark-util-normalize-identifier": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0", "unist-util-stringify-position": "^3.0.0", "uvu": "^0.5.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdast-util-to-string": {"version": "3.2.0", "license": "MIT", "dependencies": {"@types/mdast": "^3.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/mdurl": {"version": "1.0.1", "license": "MIT"}, "node_modules/memoize-one": {"version": "6.0.0", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/mermaid": {"version": "10.9.3", "license": "MIT", "dependencies": {"@braintree/sanitize-url": "^6.0.1", "@types/d3-scale": "^4.0.3", "@types/d3-scale-chromatic": "^3.0.0", "cytoscape": "^3.28.1", "cytoscape-cose-bilkent": "^4.1.0", "d3": "^7.4.0", "d3-sankey": "^0.12.3", "dagre-d3-es": "7.0.10", "dayjs": "^1.11.7", "dompurify": "^3.0.5 <3.1.7", "elkjs": "^0.9.0", "katex": "^0.16.9", "khroma": "^2.0.0", "lodash-es": "^4.17.21", "mdast-util-from-markdown": "^1.3.0", "non-layered-tidy-tree-layout": "^2.0.2", "stylis": "^4.1.3", "ts-dedent": "^2.2.0", "uuid": "^9.0.0", "web-worker": "^1.2.0"}}, "node_modules/mermaid/node_modules/commander": {"version": "8.3.0", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/mermaid/node_modules/katex": {"version": "0.16.22", "funding": ["https://opencollective.com/katex", "https://github.com/sponsors/katex"], "license": "MIT", "dependencies": {"commander": "^8.3.0"}, "bin": {"katex": "cli.js"}}, "node_modules/mermaid/node_modules/uuid": {"version": "9.0.1", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/micromark": {"version": "3.2.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"@types/debug": "^4.0.0", "debug": "^4.0.0", "decode-named-character-reference": "^1.0.0", "micromark-core-commonmark": "^1.0.1", "micromark-factory-space": "^1.0.0", "micromark-util-character": "^1.0.0", "micromark-util-chunked": "^1.0.0", "micromark-util-combine-extensions": "^1.0.0", "micromark-util-decode-numeric-character-reference": "^1.0.0", "micromark-util-encode": "^1.0.0", "micromark-util-normalize-identifier": "^1.0.0", "micromark-util-resolve-all": "^1.0.0", "micromark-util-sanitize-uri": "^1.0.0", "micromark-util-subtokenize": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.1", "uvu": "^0.5.0"}}, "node_modules/micromark-core-commonmark": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"decode-named-character-reference": "^1.0.0", "micromark-factory-destination": "^1.0.0", "micromark-factory-label": "^1.0.0", "micromark-factory-space": "^1.0.0", "micromark-factory-title": "^1.0.0", "micromark-factory-whitespace": "^1.0.0", "micromark-util-character": "^1.0.0", "micromark-util-chunked": "^1.0.0", "micromark-util-classify-character": "^1.0.0", "micromark-util-html-tag-name": "^1.0.0", "micromark-util-normalize-identifier": "^1.0.0", "micromark-util-resolve-all": "^1.0.0", "micromark-util-subtokenize": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.1", "uvu": "^0.5.0"}}, "node_modules/micromark-factory-destination": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-character": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0"}}, "node_modules/micromark-factory-label": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-character": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0", "uvu": "^0.5.0"}}, "node_modules/micromark-factory-space": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-character": "^1.0.0", "micromark-util-types": "^1.0.0"}}, "node_modules/micromark-factory-title": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-factory-space": "^1.0.0", "micromark-util-character": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0"}}, "node_modules/micromark-factory-whitespace": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-factory-space": "^1.0.0", "micromark-util-character": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0"}}, "node_modules/micromark-util-character": {"version": "1.2.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0"}}, "node_modules/micromark-util-chunked": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-symbol": "^1.0.0"}}, "node_modules/micromark-util-classify-character": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-character": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0"}}, "node_modules/micromark-util-combine-extensions": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-chunked": "^1.0.0", "micromark-util-types": "^1.0.0"}}, "node_modules/micromark-util-decode-numeric-character-reference": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-symbol": "^1.0.0"}}, "node_modules/micromark-util-decode-string": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"decode-named-character-reference": "^1.0.0", "micromark-util-character": "^1.0.0", "micromark-util-decode-numeric-character-reference": "^1.0.0", "micromark-util-symbol": "^1.0.0"}}, "node_modules/micromark-util-encode": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT"}, "node_modules/micromark-util-html-tag-name": {"version": "1.2.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT"}, "node_modules/micromark-util-normalize-identifier": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-symbol": "^1.0.0"}}, "node_modules/micromark-util-resolve-all": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-types": "^1.0.0"}}, "node_modules/micromark-util-sanitize-uri": {"version": "1.2.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-character": "^1.0.0", "micromark-util-encode": "^1.0.0", "micromark-util-symbol": "^1.0.0"}}, "node_modules/micromark-util-subtokenize": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT", "dependencies": {"micromark-util-chunked": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0", "uvu": "^0.5.0"}}, "node_modules/micromark-util-symbol": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT"}, "node_modules/micromark-util-types": {"version": "1.1.0", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "license": "MIT"}, "node_modules/micromatch": {"version": "3.1.10", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.0", "license": "MIT"}, "node_modules/mitt": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/mixin-deep": {"version": "1.3.2", "license": "MIT", "dependencies": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mkdirp": {"version": "0.5.6", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mkdirp/node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mri": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/mrmime": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/n-gram": {"version": "2.0.2", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/nanoid": {"version": "5.1.5", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.js"}, "engines": {"node": "^18 || >=20"}}, "node_modules/nanomatch": {"version": "1.2.13", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/node-releases": {"version": "2.0.19", "dev": true, "license": "MIT"}, "node_modules/non-layered-tidy-tree-layout": {"version": "2.0.2", "license": "MIT"}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-wheel-es": {"version": "1.2.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/npm-run-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"path-key": "^4.0.0", "unicorn-magic": "^0.3.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm-run-path/node_modules/path-key": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/numeral": {"version": "2.0.6", "license": "MIT", "engines": {"node": "*"}}, "node_modules/numfmt": {"version": "2.5.2", "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy": {"version": "0.1.0", "license": "MIT", "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/object-copy/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-visit": {"version": "1.0.1", "license": "MIT", "dependencies": {"isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.pick": {"version": "1.3.0", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/open": {"version": "8.4.2", "dev": true, "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/opentype.js": {"version": "1.3.4", "license": "MIT", "dependencies": {"string.prototype.codepointat": "^0.2.1", "tiny-inflate": "^1.0.3"}, "bin": {"ot": "bin/ot"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/ot-json1": {"version": "1.0.2", "license": "ISC", "dependencies": {"ot-text-unicode": "4"}}, "node_modules/ot-text-unicode": {"version": "4.0.0", "license": "ISC", "dependencies": {"unicount": "1.1"}}, "node_modules/pako": {"version": "2.1.0", "license": "(MIT AND Zlib)"}, "node_modules/parse-ms": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse-svg-path": {"version": "0.1.2", "license": "MIT"}, "node_modules/pascalcase": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-browserify": {"version": "1.0.1", "license": "MIT"}, "node_modules/path-data-parser": {"version": "0.1.0", "license": "MIT"}, "node_modules/path-dirname": {"version": "1.0.2", "license": "MIT"}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-source": {"version": "0.1.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"array-source": "0.0", "file-source": "0.6"}}, "node_modules/path-type": {"version": "3.0.0", "license": "MIT", "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/path-type/node_modules/pify": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/pathe": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/pbf": {"version": "3.3.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"ieee754": "^1.1.12", "resolve-protobuf-schema": "^2.1.0"}, "bin": {"pbf": "bin/pbf"}}, "node_modules/perfect-debounce": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "4.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/point-at-length": {"version": "1.1.0", "license": "MIT", "dependencies": {"abs-svg-path": "~0.1.1", "isarray": "~0.0.1", "parse-svg-path": "~0.1.1"}}, "node_modules/points-on-curve": {"version": "0.2.0", "license": "MIT"}, "node_modules/points-on-path": {"version": "0.2.1", "license": "MIT", "dependencies": {"path-data-parser": "0.1.0", "points-on-curve": "0.2.0"}}, "node_modules/posix-character-classes": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/postcss": {"version": "8.5.5", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss/node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/pretty-ms": {"version": "9.2.0", "dev": true, "license": "MIT", "dependencies": {"parse-ms": "^4.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/prismjs": {"version": "1.30.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "license": "MIT"}, "node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/prop-types/node_modules/react-is": {"version": "16.13.1", "license": "MIT"}, "node_modules/protobufjs": {"version": "7.5.3", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/node": ">=13.7.0", "long": "^5.0.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/protocol-buffers-schema": {"version": "3.6.0", "license": "MIT"}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/quickselect": {"version": "3.0.0", "license": "ISC"}, "node_modules/raf-schd": {"version": "4.0.3", "license": "MIT"}, "node_modules/rbush": {"version": "4.0.1", "license": "MIT", "dependencies": {"quickselect": "^3.0.0"}}, "node_modules/rc-collapse": {"version": "3.9.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.3.4", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-dialog": {"version": "9.6.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/portal": "^1.0.0-8", "classnames": "^2.2.6", "rc-motion": "^2.3.0", "rc-util": "^5.21.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-dropdown": {"version": "4.2.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.6", "rc-util": "^5.44.1"}, "peerDependencies": {"react": ">=16.11.0", "react-dom": ">=16.11.0"}}, "node_modules/rc-menu": {"version": "9.16.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/trigger": "^2.0.0", "classnames": "2.x", "rc-motion": "^2.4.3", "rc-overflow": "^1.3.1", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-motion": {"version": "2.9.5", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-util": "^5.44.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-notification": {"version": "5.6.4", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.9.0", "rc-util": "^5.20.1"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-overflow": {"version": "1.4.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-resize-observer": "^1.0.0", "rc-util": "^5.37.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-picker": {"version": "4.11.3", "license": "MIT", "dependencies": {"@babel/runtime": "^7.24.7", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.1", "rc-overflow": "^1.3.2", "rc-resize-observer": "^1.4.0", "rc-util": "^5.43.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"date-fns": ">= 2.x", "dayjs": ">= 1.x", "luxon": ">= 3.x", "moment": ">= 2.x", "react": ">=16.9.0", "react-dom": ">=16.9.0"}, "peerDependenciesMeta": {"date-fns": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}}}, "node_modules/rc-resize-observer": {"version": "1.4.3", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.7", "classnames": "^2.2.1", "rc-util": "^5.44.1", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-select": {"version": "14.16.8", "license": "MIT", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/trigger": "^2.1.1", "classnames": "2.x", "rc-motion": "^2.0.1", "rc-overflow": "^1.3.1", "rc-util": "^5.16.1", "rc-virtual-list": "^3.5.2"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/rc-util": {"version": "5.44.4", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "react-is": "^18.2.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-virtual-list": {"version": "3.18.6", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.0", "classnames": "^2.2.6", "rc-resize-observer": "^1.0.0", "rc-util": "^5.36.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/react": {"version": "19.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "19.1.0", "license": "MIT", "dependencies": {"scheduler": "^0.26.0"}, "peerDependencies": {"react": "^19.1.0"}}, "node_modules/react-draggable": {"version": "4.4.6", "license": "MIT", "dependencies": {"clsx": "^1.1.1", "prop-types": "^15.8.1"}, "peerDependencies": {"react": ">= 16.3.0", "react-dom": ">= 16.3.0"}}, "node_modules/react-draggable/node_modules/clsx": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/react-grid-layout": {"version": "1.5.1", "license": "MIT", "dependencies": {"clsx": "^2.0.0", "fast-equals": "^4.0.3", "prop-types": "^15.8.1", "react-draggable": "^4.4.5", "react-resizable": "^3.0.5", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"react": ">= 16.3.0", "react-dom": ">= 16.3.0"}}, "node_modules/react-is": {"version": "18.3.1", "license": "MIT"}, "node_modules/react-remove-scroll": {"version": "2.7.1", "license": "MIT", "dependencies": {"react-remove-scroll-bar": "^2.3.7", "react-style-singleton": "^2.2.3", "tslib": "^2.1.0", "use-callback-ref": "^1.3.3", "use-sidecar": "^1.1.3"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-remove-scroll-bar": {"version": "2.3.8", "license": "MIT", "dependencies": {"react-style-singleton": "^2.2.2", "tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-resizable": {"version": "3.0.5", "license": "MIT", "dependencies": {"prop-types": "15.x", "react-draggable": "^4.0.3"}, "peerDependencies": {"react": ">= 16.3"}}, "node_modules/react-style-singleton": {"version": "2.2.3", "license": "MIT", "dependencies": {"get-nonce": "^1.0.0", "tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-transition-group": {"version": "4.4.5", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdir-glob": {"version": "1.1.3", "license": "Apache-2.0", "dependencies": {"minimatch": "^5.1.0"}}, "node_modules/readdir-glob/node_modules/brace-expansion": {"version": "2.0.2", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/readdir-glob/node_modules/minimatch": {"version": "5.1.6", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/redux": {"version": "4.2.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.9.2"}}, "node_modules/regex-not": {"version": "1.0.2", "license": "MIT", "dependencies": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regexp-util": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/repeat-element": {"version": "1.1.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-string": {"version": "1.6.1", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/resize-observer-polyfill": {"version": "1.5.1", "license": "MIT"}, "node_modules/resolve-protobuf-schema": {"version": "2.1.0", "license": "MIT", "dependencies": {"protocol-buffers-schema": "^3.3.1"}}, "node_modules/resolve-url": {"version": "0.2.1", "license": "MIT"}, "node_modules/ret": {"version": "0.1.15", "license": "MIT", "engines": {"node": ">=0.12"}}, "node_modules/rfdc": {"version": "1.4.1", "dev": true, "license": "MIT"}, "node_modules/rimraf": {"version": "2.7.1", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/robust-predicates": {"version": "3.0.2", "license": "Unlicense"}, "node_modules/rollup": {"version": "4.43.0", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.7"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.43.0", "@rollup/rollup-android-arm64": "4.43.0", "@rollup/rollup-darwin-arm64": "4.43.0", "@rollup/rollup-darwin-x64": "4.43.0", "@rollup/rollup-freebsd-arm64": "4.43.0", "@rollup/rollup-freebsd-x64": "4.43.0", "@rollup/rollup-linux-arm-gnueabihf": "4.43.0", "@rollup/rollup-linux-arm-musleabihf": "4.43.0", "@rollup/rollup-linux-arm64-gnu": "4.43.0", "@rollup/rollup-linux-arm64-musl": "4.43.0", "@rollup/rollup-linux-loongarch64-gnu": "4.43.0", "@rollup/rollup-linux-powerpc64le-gnu": "4.43.0", "@rollup/rollup-linux-riscv64-gnu": "4.43.0", "@rollup/rollup-linux-riscv64-musl": "4.43.0", "@rollup/rollup-linux-s390x-gnu": "4.43.0", "@rollup/rollup-linux-x64-gnu": "4.43.0", "@rollup/rollup-linux-x64-musl": "4.43.0", "@rollup/rollup-win32-arm64-msvc": "4.43.0", "@rollup/rollup-win32-ia32-msvc": "4.43.0", "@rollup/rollup-win32-x64-msvc": "4.43.0", "fsevents": "~2.3.2"}}, "node_modules/rollup-plugin-visualizer": {"version": "6.0.3", "dev": true, "license": "MIT", "dependencies": {"open": "^8.0.0", "picomatch": "^4.0.2", "source-map": "^0.7.4", "yargs": "^17.5.1"}, "bin": {"rollup-plugin-visualizer": "dist/bin/cli.js"}, "engines": {"node": ">=18"}, "peerDependencies": {"rolldown": "1.x || ^1.0.0-beta", "rollup": "2.x || 3.x || 4.x"}, "peerDependenciesMeta": {"rolldown": {"optional": true}, "rollup": {"optional": true}}}, "node_modules/rollup/node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.43.0.tgz", "integrity": "sha512-<PERSON>rjy9awJl6rKbruhQDgivNbD1WuLb8xAclM4IR4cN5pHGAs2oIMMQJEiC3IC/9TZJ+QZkmZhlMO/6MBGxPidpw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/rollup/node_modules/@rollup/rollup-android-arm64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.43.0.tgz", "integrity": "sha512-ss4YJwRt5I63454Rpj+mXCXicakdFmKnUNxr1dLK+5rv5FJgAxnN7s31a5VchRYxCFWdmnDWKd0wbAdTr0J5EA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/rollup/node_modules/@rollup/rollup-darwin-arm64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.43.0.tgz", "integrity": "sha512-eKoL8ykZ7zz8MjgBenEF2OoTNFAPFz1/lyJ5UmmFSz5jW+7XbH1+MAgCVHy72aG59rbuQLcJeiMrP8qP5d/N0A==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/rollup/node_modules/@rollup/rollup-darwin-x64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.43.0.tgz", "integrity": "sha512-SYwXJgaBYW33Wi/q4ubN+ldWC4DzQY62S4Ll2dgfr/dbPoF50dlQwEaEHSKrQdSjC6oIe1WgzosoaNoHCdNuMg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/rollup/node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.43.0.tgz", "integrity": "sha512-SV+U5sSo0yujrjzBF7/YidieK2iF6E7MdF6EbYxNz94lA+R0wKl3SiixGyG/9Klab6uNBIqsN7j4Y/Fya7wAjQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/rollup/node_modules/@rollup/rollup-freebsd-x64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.43.0.tgz", "integrity": "sha512-J7uCsiV13L/VOeHJBo5SjasKiGxJ0g+nQTrBkAsmQBIdil3KhPnSE9GnRon4ejX1XDdsmK/l30IYLiAaQEO0Cg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.43.0.tgz", "integrity": "sha512-gTJ/JnnjCMc15uwB10TTATBEhK9meBIY+gXP4s0sHD1zHOaIh4Dmy1X9wup18IiY9tTNk5gJc4yx9ctj/fjrIw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.43.0.tgz", "integrity": "sha512-ZJ3gZynL1LDSIvRfz0qXtTNs56n5DI2Mq+WACWZ7yGHFUEirHBRt7fyIk0NsCKhmRhn7WAcjgSkSVVxKlPNFFw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.43.0.tgz", "integrity": "sha512-8FnkipasmOOSSlfucGYEu58U8cxEdhziKjPD2FIa0ONVMxvl/hmONtX/7y4vGjdUhjcTHlKlDhw3H9t98fPvyA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.43.0.tgz", "integrity": "sha512-KPPyAdlcIZ6S9C3S2cndXDkV0Bb1OSMsX0Eelr2Bay4EsF9yi9u9uzc9RniK3mcUGCLhWY9oLr6er80P5DE6XA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.43.0.tgz", "integrity": "sha512-HPGDIH0/ZzAZjvtlXj6g+KDQ9ZMHfSP553za7o2Odegb/BEfwJcR0Sw0RLNpQ9nC6Gy8s+3mSS9xjZ0n3rhcYg==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.43.0.tgz", "integrity": "sha512-gEmwbOws4U4GLAJDhhtSPWPXUzDfMRedT3hFMyRAvM9Mrnj+dJIFIeL7otsv2WF3D7GrV0GIewW0y28dOYWkmw==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.43.0.tgz", "integrity": "sha512-XXKvo2e+wFtXZF/9xoWohHg+MuRnvO29TI5Hqe9xwN5uN8NKUYy7tXUG3EZAlfchufNCTHNGjEx7uN78KsBo0g==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.43.0.tgz", "integrity": "sha512-ruf3hPWhjw6uDFsOAzmbNIvlXFXlBQ4nk57Sec8E8rUxs/AI4HD6xmiiasOOx/3QxS2f5eQMKTAwk7KHwpzr/Q==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.43.0.tgz", "integrity": "sha512-QmNIAqDiEMEvFV15rsSnjoSmO0+eJLoKRD9EAa9rrYNwO/XRCtOGM3A5A0X+wmG+XRrw9Fxdsw+LnyYiZWWcVw==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.43.0.tgz", "integrity": "sha512-jAHr/S0iiBtFyzjhOkAics/2SrXE092qyqEg96e90L3t9Op8OTzS6+IX0Fy5wCt2+KqeHAkti+eitV0wvblEoQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.43.0.tgz", "integrity": "sha512-3yATWgdeXyuHtBhrLt98w+5fKurdqvs8B53LaoKD7P7H7FKOONLsBVMNl9ghPQZQuYcceV5CDyPfyfGpMWD9mQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/rollup/node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.43.0.tgz", "integrity": "sha512-wVzXp2qDSCOpcBCT5WRWLmpJRIzv23valvcTwMHEobkjippNf+C3ys/+wf07poPkeNix0paTNemB2XrHr2TnGw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/rollup/node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.43.0.tgz", "integrity": "sha512-fYCTEyzf8d+7diCw8b+asvWDCLMjsCEA8alvtAutqJOJp/wL5hs1rWSqJ1vkjgW0L2NB4bsYJrpKkiIPRR9dvw==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/rollup/node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/roughjs": {"version": "4.5.2", "license": "MIT", "dependencies": {"path-data-parser": "^0.1.0", "points-on-curve": "^0.2.0", "points-on-path": "^0.2.1"}}, "node_modules/run-applescript": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/rw": {"version": "1.3.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/rxjs": {"version": "7.8.2", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/sade": {"version": "1.8.1", "license": "MIT", "dependencies": {"mri": "^1.1.0"}, "engines": {"node": ">=6"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex": {"version": "1.1.0", "license": "MIT", "dependencies": {"ret": "~0.1.10"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/sass-embedded": {"version": "1.89.2", "dev": true, "license": "MIT", "dependencies": {"@bufbuild/protobuf": "^2.5.0", "buffer-builder": "^0.2.0", "colorjs.io": "^0.5.0", "immutable": "^5.0.2", "rxjs": "^7.4.0", "supports-color": "^8.1.1", "sync-child-process": "^1.0.2", "varint": "^6.0.0"}, "bin": {"sass": "dist/bin/sass.js"}, "engines": {"node": ">=16.0.0"}, "optionalDependencies": {"sass-embedded-android-arm": "1.89.2", "sass-embedded-android-arm64": "1.89.2", "sass-embedded-android-riscv64": "1.89.2", "sass-embedded-android-x64": "1.89.2", "sass-embedded-darwin-arm64": "1.89.2", "sass-embedded-darwin-x64": "1.89.2", "sass-embedded-linux-arm": "1.89.2", "sass-embedded-linux-arm64": "1.89.2", "sass-embedded-linux-musl-arm": "1.89.2", "sass-embedded-linux-musl-arm64": "1.89.2", "sass-embedded-linux-musl-riscv64": "1.89.2", "sass-embedded-linux-musl-x64": "1.89.2", "sass-embedded-linux-riscv64": "1.89.2", "sass-embedded-linux-x64": "1.89.2", "sass-embedded-win32-arm64": "1.89.2", "sass-embedded-win32-x64": "1.89.2"}}, "node_modules/sass-embedded-win32-x64": {"version": "1.89.2", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/sass-embedded/node_modules/sass-embedded-android-arm": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-android-arm/-/sass-embedded-android-arm-1.89.2.tgz", "integrity": "sha512-oHAPTboBHRZlDBhyRB6dvDKh4KvFs+DZibDHXbkSI6dBZxMTT+Yb2ivocHnctVGucKTLQeT7+OM5DjWHyynL/A==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-android-arm64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-android-arm64/-/sass-embedded-android-arm64-1.89.2.tgz", "integrity": "sha512-+pq7a7AUpItNyPu61sRlP6G2A8pSPpyazASb+8AK2pVlFayCSPAEgpwpCE9A2/Xj86xJZeMizzKUHxM2CBCUxA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-android-riscv64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-android-riscv64/-/sass-embedded-android-riscv64-1.89.2.tgz", "integrity": "sha512-HfJJWp/S6XSYvlGAqNdakeEMPOdhBkj2s2lN6SHnON54rahKem+z9pUbCriUJfM65Z90lakdGuOfidY61R9TYg==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-android-x64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-android-x64/-/sass-embedded-android-x64-1.89.2.tgz", "integrity": "sha512-BGPzq53VH5z5HN8de6jfMqJjnRe1E6sfnCWFd4pK+CAiuM7iw5Fx6BQZu3ikfI1l2GY0y6pRXzsVLdp/j4EKEA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-darwin-arm64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-darwin-arm64/-/sass-embedded-darwin-arm64-1.89.2.tgz", "integrity": "sha512-UCm3RL/tzMpG7DsubARsvGUNXC5pgfQvP+RRFJo9XPIi6elopY5B6H4m9dRYDpHA+scjVthdiDwkPYr9+S/KGw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-darwin-x64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-darwin-x64/-/sass-embedded-darwin-x64-1.89.2.tgz", "integrity": "sha512-D9WxtDY5VYtMApXRuhQK9VkPHB8R79NIIR6xxVlN2MIdEid/TZWi1MHNweieETXhWGrKhRKglwnHxxyKdJYMnA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-linux-arm": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-linux-arm/-/sass-embedded-linux-arm-1.89.2.tgz", "integrity": "sha512-leP0t5U4r95dc90o8TCWfxNXwMAsQhpWxTkdtySDpngoqtTy3miMd7EYNYd1znI0FN1CBaUvbdCMbnbPwygDlA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-linux-arm64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-linux-arm64/-/sass-embedded-linux-arm64-1.89.2.tgz", "integrity": "sha512-2N4WW5LLsbtrWUJ7iTpjvhajGIbmDR18ZzYRywHdMLpfdPApuHPMDF5CYzHbS+LLx2UAx7CFKBnj5LLjY6eFgQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-linux-musl-arm": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-linux-musl-arm/-/sass-embedded-linux-musl-arm-1.89.2.tgz", "integrity": "sha512-Z6gG2FiVEEdxYHRi2sS5VIYBmp17351bWtOCUZ/thBM66+e70yiN6Eyqjz80DjL8haRUegNQgy9ZJqsLAAmr9g==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-linux-musl-arm64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-linux-musl-arm64/-/sass-embedded-linux-musl-arm64-1.89.2.tgz", "integrity": "sha512-nTyuaBX6U1A/cG7WJh0pKD1gY8hbg1m2SnzsyoFG+exQ0lBX/lwTLHq3nyhF+0atv7YYhYKbmfz+sjPP8CZ9lw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-linux-musl-riscv64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-linux-musl-riscv64/-/sass-embedded-linux-musl-riscv64-1.89.2.tgz", "integrity": "sha512-N6oul+qALO0SwGY8JW7H/Vs0oZIMrRMBM4GqX3AjM/6y8JsJRxkAwnfd0fDyK+aICMFarDqQonQNIx99gdTZqw==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-linux-musl-x64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-linux-musl-x64/-/sass-embedded-linux-musl-x64-1.89.2.tgz", "integrity": "sha512-K+FmWcdj/uyP8GiG9foxOCPfb5OAZG0uSVq80DKgVSC0U44AdGjvAvVZkrgFEcZ6cCqlNC2JfYmslB5iqdL7tg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-linux-riscv64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-linux-riscv64/-/sass-embedded-linux-riscv64-1.89.2.tgz", "integrity": "sha512-g9nTbnD/3yhOaskeqeBQETbtfDQWRgsjHok6bn7DdAuwBsyrR3JlSFyqKc46pn9Xxd9SQQZU8AzM4IR+sY0A0w==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-linux-x64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-linux-x64/-/sass-embedded-linux-x64-1.89.2.tgz", "integrity": "sha512-Ax7dKvzncyQzIl4r7012KCMBvJzOz4uwSNoyoM5IV6y5I1f5hEwI25+U4WfuTqdkv42taCMgpjZbh9ERr6JVMQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/sass-embedded-win32-arm64": {"version": "1.89.2", "resolved": "https://registry.npmjs.org/sass-embedded-win32-arm64/-/sass-embedded-win32-arm64-1.89.2.tgz", "integrity": "sha512-j96iJni50ZUsfD6tRxDQE2QSYQ2WrfHxeiyAXf41Kw0V4w5KYR/Sf6rCZQLMTUOHnD16qTMVpQi20LQSqf4WGg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded/node_modules/supports-color": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/saxes": {"version": "5.0.1", "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=10"}}, "node_modules/scheduler": {"version": "0.26.0", "license": "MIT"}, "node_modules/section-matter": {"version": "1.0.0", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "kind-of": "^6.0.0"}, "engines": {"node": ">=4"}}, "node_modules/section-matter/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/section-matter/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/set-value": {"version": "2.0.1", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/setimmediate": {"version": "1.0.5", "license": "MIT"}, "node_modules/shapefile": {"version": "0.6.6", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"array-source": "0.0", "commander": "2", "path-source": "0.1", "slice-source": "0.4", "stream-source": "0.3", "text-encoding": "^0.6.4"}, "bin": {"dbf2json": "bin/dbf2json", "shp2json": "bin/shp2json"}}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/simple-statistics": {"version": "7.8.8", "license": "ISC", "engines": {"node": "*"}}, "node_modules/simplify-geojson": {"version": "1.0.5", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"concat-stream": "~1.4.1", "minimist": "1.2.6", "simplify-geometry": "0.0.2"}, "bin": {"simplify-geojson": "cli.js"}}, "node_modules/simplify-geojson/node_modules/concat-stream": {"version": "1.4.11", "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"inherits": "~2.0.1", "readable-stream": "~1.1.9", "typedarray": "~0.0.5"}}, "node_modules/simplify-geojson/node_modules/minimist": {"version": "1.2.6", "license": "MIT"}, "node_modules/simplify-geojson/node_modules/readable-stream": {"version": "1.1.14", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/simplify-geojson/node_modules/string_decoder": {"version": "0.10.31", "license": "MIT"}, "node_modules/simplify-geometry": {"version": "0.0.2", "license": "MIT"}, "node_modules/sirv": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"@polka/url": "^1.0.0-next.24", "mrmime": "^2.0.0", "totalist": "^3.0.0"}, "engines": {"node": ">=18"}}, "node_modules/slash": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/slice-source": {"version": "0.4.1", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/snapdragon": {"version": "0.8.2", "license": "MIT", "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node": {"version": "2.1.1", "license": "MIT", "dependencies": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util": {"version": "3.0.1", "license": "MIT", "dependencies": {"kind-of": "^3.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/snapdragon/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/snapdragon/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/snapdragon/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map": {"version": "0.7.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.5.3", "license": "MIT", "dependencies": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-url": {"version": "0.4.1", "license": "MIT"}, "node_modules/speakingurl": {"version": "14.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/split-string": {"version": "3.1.0", "license": "MIT", "dependencies": {"extend-shallow": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sprintf-js": {"version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/static-extend": {"version": "0.1.2", "license": "MIT", "dependencies": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/stream-source": {"version": "0.3.5", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string.prototype.codepointat": {"version": "0.2.1", "license": "MIT"}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom-string": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/strip-final-newline": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/stylis": {"version": "4.3.6", "license": "MIT"}, "node_modules/superjson": {"version": "2.2.2", "dev": true, "license": "MIT", "dependencies": {"copy-anything": "^3.0.2"}, "engines": {"node": ">=16"}}, "node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/sync-child-process": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"sync-message-port": "^1.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/sync-message-port": {"version": "1.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=16.0.0"}}, "node_modules/tailwind-merge": {"version": "3.3.1", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/dcastil"}}, "node_modules/tar-stream": {"version": "2.2.0", "license": "MIT", "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/terser": {"version": "5.42.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.14.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/text-encoding": {"version": "0.6.4", "license": "Unlicense"}, "node_modules/tiny-inflate": {"version": "1.0.3", "license": "MIT"}, "node_modules/tiny-invariant": {"version": "1.3.3", "license": "MIT"}, "node_modules/tinyglobby": {"version": "0.2.14", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tmp": {"version": "0.2.3", "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/to-object-path": {"version": "0.3.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-object-path/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex": {"version": "3.0.2", "license": "MIT", "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range": {"version": "2.1.1", "license": "MIT", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/toggle-selection": {"version": "1.0.6", "license": "MIT"}, "node_modules/toml": {"version": "3.0.0", "license": "MIT"}, "node_modules/topojson-client": {"version": "3.1.0", "license": "ISC", "dependencies": {"commander": "2"}, "bin": {"topo2geo": "bin/topo2geo", "topomerge": "bin/topomerge", "topoquantize": "bin/topoquantize"}}, "node_modules/topojson-server": {"version": "3.0.1", "license": "ISC", "dependencies": {"commander": "2"}, "bin": {"geo2topo": "bin/geo2topo"}}, "node_modules/totalist": {"version": "3.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/traverse": {"version": "0.3.9", "license": "MIT/X11"}, "node_modules/trigram-utils": {"version": "2.0.1", "license": "MIT", "dependencies": {"collapse-white-space": "^2.0.0", "n-gram": "^2.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/ts-dedent": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6.10"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/typedarray": {"version": "0.0.6", "license": "MIT"}, "node_modules/uc.micro": {"version": "1.0.6", "license": "MIT"}, "node_modules/undici-types": {"version": "7.8.0", "license": "MIT"}, "node_modules/unicode-regex": {"version": "4.1.2", "license": "MIT", "dependencies": {"regexp-util": "^2.0.1"}, "engines": {"node": ">=16"}}, "node_modules/unicorn-magic": {"version": "0.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/unicount": {"version": "1.1.0", "license": "ISC"}, "node_modules/union-value": {"version": "1.0.1", "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/union-value/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/unist-util-stringify-position": {"version": "3.0.3", "license": "MIT", "dependencies": {"@types/unist": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}}, "node_modules/universalify": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/unset-value": {"version": "1.0.0", "license": "MIT", "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value": {"version": "0.3.1", "license": "MIT", "dependencies": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value/node_modules/isobject": {"version": "2.1.0", "license": "MIT", "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-values": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/unzipper": {"version": "0.10.14", "license": "MIT", "dependencies": {"big-integer": "^1.6.17", "binary": "~0.3.0", "bluebird": "~3.4.1", "buffer-indexof-polyfill": "~1.0.0", "duplexer2": "~0.1.4", "fstream": "^1.0.12", "graceful-fs": "^4.2.2", "listenercount": "~1.0.1", "readable-stream": "~2.3.6", "setimmediate": "~1.0.4"}}, "node_modules/unzipper/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/unzipper/node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/unzipper/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/unzipper/node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/upath": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4", "yarn": "*"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/urix": {"version": "0.1.0", "license": "MIT"}, "node_modules/use": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/use-callback-ref": {"version": "1.3.3", "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/use-sidecar": {"version": "1.1.3", "license": "MIT", "dependencies": {"detect-node-es": "^1.1.0", "tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/uuid": {"version": "11.1.0", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}}, "node_modules/uvu": {"version": "0.5.6", "license": "MIT", "dependencies": {"dequal": "^2.0.0", "diff": "^5.0.0", "kleur": "^4.0.3", "sade": "^1.7.3"}, "bin": {"uvu": "bin.js"}, "engines": {"node": ">=8"}}, "node_modules/vant": {"version": "3.6.16", "license": "MIT", "dependencies": {"@vant/icons": "^1.8.0", "@vant/popperjs": "^1.2.1", "@vant/use": "^1.4.2"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/varint": {"version": "6.0.0", "dev": true, "license": "MIT"}, "node_modules/vite": {"version": "6.3.5", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.4", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.13"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite-hot-client": {"version": "2.0.4", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"vite": "^2.6.0 || ^3.0.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0"}}, "node_modules/vite-plugin-inspect": {"version": "0.8.9", "dev": true, "license": "MIT", "dependencies": {"@antfu/utils": "^0.7.10", "@rollup/pluginutils": "^5.1.3", "debug": "^4.3.7", "error-stack-parser-es": "^0.1.5", "fs-extra": "^11.2.0", "open": "^10.1.0", "perfect-debounce": "^1.0.0", "picocolors": "^1.1.1", "sirv": "^3.0.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.1"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}}}, "node_modules/vite-plugin-inspect/node_modules/define-lazy-prop": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-inspect/node_modules/fs-extra": {"version": "11.3.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/vite-plugin-inspect/node_modules/is-wsl": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"is-inside-container": "^1.0.0"}, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-inspect/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/vite-plugin-inspect/node_modules/open": {"version": "10.1.2", "dev": true, "license": "MIT", "dependencies": {"default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "is-wsl": "^3.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-inspect/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/vite-plugin-vue-devtools": {"version": "7.7.6", "dev": true, "license": "MIT", "dependencies": {"@vue/devtools-core": "^7.7.6", "@vue/devtools-kit": "^7.7.6", "@vue/devtools-shared": "^7.7.6", "execa": "^9.5.2", "sirv": "^3.0.1", "vite-plugin-inspect": "0.8.9", "vite-plugin-vue-inspector": "^5.3.1"}, "engines": {"node": ">=v14.21.3"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0"}}, "node_modules/vite-plugin-vue-inspector": {"version": "5.3.1", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.23.0", "@babel/plugin-proposal-decorators": "^7.23.0", "@babel/plugin-syntax-import-attributes": "^7.22.5", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-typescript": "^7.22.15", "@vue/babel-plugin-jsx": "^1.1.5", "@vue/compiler-dom": "^3.3.4", "kolorist": "^1.8.0", "magic-string": "^0.30.4"}, "peerDependencies": {"vite": "^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0"}}, "node_modules/vite/node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/vue": {"version": "3.5.16", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.16", "@vue/compiler-sfc": "3.5.16", "@vue/runtime-dom": "3.5.16", "@vue/server-renderer": "3.5.16", "@vue/shared": "3.5.16"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vue-demi": {"version": "0.13.11", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/vue-echarts": {"version": "7.0.3", "license": "MIT", "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/runtime-core": "^3.0.0", "echarts": "^5.5.1", "vue": "^2.7.0 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/runtime-core": {"optional": true}}}, "node_modules/vue-router": {"version": "4.5.1", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.4"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/web-worker": {"version": "1.5.0", "license": "Apache-2.0"}, "node_modules/webpack-chain": {"version": "4.12.1", "license": "MPL-2.0", "dependencies": {"deepmerge": "^1.5.2", "javascript-stringify": "^1.6.0"}}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/xmlchars": {"version": "2.2.0", "license": "MIT"}, "node_modules/xss": {"version": "1.0.15", "license": "MIT", "dependencies": {"commander": "^2.20.3", "cssfilter": "0.0.10"}, "bin": {"xss": "bin/xss"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.1.1", "dev": true, "license": "ISC"}, "node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yoctocolors": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/zip-stream": {"version": "4.1.1", "license": "MIT", "dependencies": {"archiver-utils": "^3.0.4", "compress-commons": "^4.1.2", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 10"}}, "node_modules/zip-stream/node_modules/archiver-utils": {"version": "3.0.4", "license": "MIT", "dependencies": {"glob": "^7.2.3", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 10"}}, "node_modules/zrender": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tslib": "2.3.0"}}, "node_modules/zrender/node_modules/tslib": {"version": "2.3.0", "license": "0BSD"}}}