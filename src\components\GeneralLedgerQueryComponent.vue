<template>
  <BaseQueryComponent
    title="总台账"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/general-ledger'

// 查询字段配置
const queryFields = [
  {
    key: 'profitCenterDesc',
    label: '利润中心描述',
    type: 'text',
    placeholder: '请输入利润中心描述',
    width: '200px'
  },
  {
    key: 'profitCenter',
    label: '利润中心',
    type: 'text',
    placeholder: '请输入利润中心编号',
    width: '150px'
  },
  {
    key: 'income',
    label: '收入',
    type: 'amount-range'
  },
  {
    key: 'cost',
    label: '成本',
    type: 'amount-range'
  },
  {
    key: 'specialReserveBalance',
    label: '专项储备余额',
    type: 'amount-range'
  },
  {
    key: 'contractBalance',
    label: '合同余额',
    type: 'amount-range'
  },
  {
    key: 'contractPerformanceCostBalance',
    label: '合同履约成本余额',
    type: 'amount-range'
  },
  {
    key: 'expectedLiabilityLossContract',
    label: '预计负债亏损合同',
    type: 'amount-range'
  },
  {
    key: 'rawMaterials',
    label: '原材料',
    type: 'amount-range'
  },
  {
    key: 'costBalance',
    label: '成本余额',
    type: 'amount-range'
  },
  {
    key: 'estimatedPayableBalance',
    label: '暂估应付余额',
    type: 'amount-range'
  },
  {
    key: 'rdExpenditure',
    label: '研发支出',
    type: 'amount-range'
  },
  {
    key: 'thisProfitNonMonetaryTransactionOutstanding',
    label: '本利润非货币交易未平',
    type: 'amount-range'
  },
  {
    key: 'originalStock',
    label: '原始存量',
    type: 'amount-range'
  },
  {
    key: 'internalLoan',
    label: '内部借款',
    type: 'amount-range'
  },
  {
    key: 'factoringLoan',
    label: '保理借款',
    type: 'amount-range'
  },
  {
    key: 'internalTransactionHeadquarters',
    label: '内部往来挂总部',
    type: 'amount-range'
  },
  {
    key: 'internalTransactionManagerDepartment',
    label: '内部往来挂经理部',
    type: 'amount-range'
  },
  {
    key: 'onSiteMaintenanceFee',
    label: '现场维护费',
    type: 'amount-range'
  },
  {
    key: 'internalTransactionAdjustment',
    label: '内部往来需调整',
    type: 'amount-range'
  }
]
</script>
