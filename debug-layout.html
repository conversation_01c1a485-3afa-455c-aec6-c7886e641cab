<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局调试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e8f4fd 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .main-content {
            display: flex;
            flex-direction: row;
            gap: 20px;
            min-height: calc(100vh - 200px);
            align-items: stretch;
        }
        
        .left-panel {
            width: 420px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            flex-shrink: 0;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }
        
        .right-panel {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            min-width: 500px;
            height: calc(100vh - 200px);
            overflow: hidden;
        }
        
        .data-item {
            padding: 10px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #409EFF;
        }
        
        .table-placeholder {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
        }
        
        /* 响应式调整 */
        @media (max-width: 1200px) {
            .main-content {
                flex-direction: column;
            }
            
            .left-panel {
                width: 100%;
                max-height: 400px;
            }
            
            .right-panel {
                min-width: auto;
                height: 600px;
            }
        }
        
        /* 强制大屏幕左右布局 */
        @media (min-width: 1201px) {
            .main-content {
                flex-direction: row !important;
            }
            
            .left-panel {
                width: 420px !important;
                max-height: calc(100vh - 200px) !important;
            }
            
            .right-panel {
                min-width: 500px !important;
                height: calc(100vh - 200px) !important;
            }
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <div>窗口宽度: <span id="width">-</span>px</div>
        <div>窗口高度: <span id="height">-</span>px</div>
        <div>布局模式: <span id="layout">-</span></div>
    </div>
    
    <div class="container">
        <div class="header">
            <h2>项目报表系统 - 布局测试</h2>
            <p>测试左右分栏布局是否正常工作</p>
        </div>
        
        <div class="main-content" id="mainContent">
            <div class="left-panel">
                <h3>左侧数据面板</h3>
                <div class="data-item">项目基本信息</div>
                <div class="data-item">收入成本数据</div>
                <div class="data-item">应付款项</div>
                <div class="data-item">应收款项</div>
                <div class="data-item">资金状况</div>
                <div class="data-item">其他数据1</div>
                <div class="data-item">其他数据2</div>
                <div class="data-item">其他数据3</div>
                <div class="data-item">其他数据4</div>
                <div class="data-item">其他数据5</div>
                <div class="data-item">其他数据6</div>
                <div class="data-item">其他数据7</div>
                <div class="data-item">其他数据8</div>
                <div class="data-item">其他数据9</div>
                <div class="data-item">其他数据10</div>
            </div>
            
            <div class="right-panel">
                <h3>右侧台账表格</h3>
                <div class="table-placeholder">
                    台账表格区域
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function updateDebugInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const mainContent = document.getElementById('mainContent');
            const computedStyle = window.getComputedStyle(mainContent);
            const flexDirection = computedStyle.flexDirection;
            
            document.getElementById('width').textContent = width;
            document.getElementById('height').textContent = height;
            document.getElementById('layout').textContent = flexDirection === 'row' ? '左右布局' : '上下布局';
        }
        
        updateDebugInfo();
        window.addEventListener('resize', updateDebugInfo);
    </script>
</body>
</html>
