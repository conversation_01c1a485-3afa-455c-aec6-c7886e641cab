# 项目报表系统 API 规范文档

## 概述

本文档描述了项目报表系统中用于项目数据查询和切换的API接口规范，主要包含POST请求的数据格式定义。

## 1. 项目数据查询接口

### 接口地址
```
POST /api/project/data
```

### 请求头
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer {token}"
}
```

### 请求参数

#### 基本请求格式
```json
{
  "projectId": "string",
  "timestamp": "string",
  "requestType": "string",
  "filters": {
    "dateRange": {
      "startDate": "string",
      "endDate": "string"
    },
    "dataTypes": ["string"]
  }
}
```

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectId | string | 是 | 项目唯一标识符，如 "PRJ001" |
| timestamp | string | 是 | 请求时间戳，ISO 8601格式 |
| requestType | string | 否 | 请求类型，默认为 "full"，可选值：full, summary, details |
| filters | object | 否 | 过滤条件 |
| filters.dateRange | object | 否 | 日期范围过滤 |
| filters.dateRange.startDate | string | 否 | 开始日期，YYYY-MM-DD格式 |
| filters.dateRange.endDate | string | 否 | 结束日期，YYYY-MM-DD格式 |
| filters.dataTypes | array | 否 | 数据类型过滤，可选值见下方说明 |

#### 数据类型说明 (dataTypes)
- `basic` - 项目基本信息
- `income_cost` - 收入成本费用情况
- `financial_check` - 财务检查部分
- `fund_flow` - 资金收付情况
- `detail_tables` - 明细台账数据

### 请求示例

#### 完整数据请求
```json
{
  "projectId": "PRJ001",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestType": "full"
}
```

#### 指定日期范围的数据请求
```json
{
  "projectId": "PRJ002",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestType": "details",
  "filters": {
    "dateRange": {
      "startDate": "2024-01-01",
      "endDate": "2024-01-31"
    },
    "dataTypes": ["income_cost", "fund_flow"]
  }
}
```

### 响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "projectInfo": {
      "projectName": "智慧城市建设项目",
      "profitCenterCode": "PC001"
    },
    "financialData": {
      "mainBusinessIncome": 5680000,
      "mainBusinessCost": 3420000,
      "additionalTax": 568000,
      "cumulativeConfirmation": 4500000,
      "cumulativeIndirectCost": 450000,
      "cumulativeMachineryCost": 320000,
      "nonSubcontractSafetyFee": 85000,
      "specialReserveBalance": 120000,
      "contractPerformanceCostBalance": 890000,
      "rawMaterialBalance": 230000,
      "estimatedPayableBalance": 150000,
      "totalContractSettlement": 4200000,
      "totalContractPayment": 3800000,
      "totalContractEstimate": 400000,
      "reconciliationCheck": 0,
      "taxPayablePendingWriteOff": 45000,
      "invoiceAmountFromTax": 0,
      "cumulativeTaxInclusiveConfirmation": 5100000,
      "cumulativeOwnerReceipt": 3408000,
      "cumulativeDepositReceipt": 0,
      "cumulativeSubcontractorSettlement": 2800000,
      "cumulativeSubcontractorPayment": 2500000,
      "cumulativeTaxPayment": 0,
      "cumulativeManagementExpense": 0,
      "cumulativeOtherPayments": 0,
      "originalStock": 500000,
      "internalLoan": 1000000,
      "factoringLoan": 800000,
      "internalTransactionHeadquarters": 200000,
      "internalTransactionAdjustment": 50000,
      "realFundBalance": 1250000
    },
    "detailTables": {
      "payableSuppliers": [
        ["供应商名称", "供应商编码", "应付金额", "已付金额", "未付金额", "账期", "状态"],
        ["北京建材有限公司", "SUP001", 1500000, 1200000, 300000, "30天", "正常"]
      ],
      "payableContracts": [
        ["合同编号", "合同名称", "合同金额", "已结算金额", "未结算金额", "结算比例", "状态"],
        ["CT2024001", "主体工程施工合同", 5000000, 4500000, 500000, "90%", "执行中"]
      ]
    }
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "Invalid project ID",
  "data": null,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 2. 项目列表查询接口

### 接口地址
```
POST /api/project/search
```

### 请求参数
```json
{
  "query": "string",
  "limit": "number",
  "offset": "number"
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| query | string | 是 | 搜索关键词，支持项目名称和编码搜索 |
| limit | number | 否 | 返回结果数量限制，默认20 |
| offset | number | 否 | 分页偏移量，默认0 |

### 请求示例
```json
{
  "query": "智慧城市",
  "limit": 10,
  "offset": 0
}
```

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "projects": [
      {
        "id": "PRJ001",
        "name": "智慧城市建设项目",
        "code": "PRJ2024001"
      }
    ],
    "total": 1
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 3. 明细台账数据查询接口

### 接口地址
```
POST /api/project/detail-tables
```

### 请求参数
```json
{
  "projectId": "string",
  "tableType": "string",
  "filters": {
    "dateRange": {
      "startDate": "string",
      "endDate": "string"
    },
    "status": "string",
    "amount": {
      "min": "number",
      "max": "number"
    }
  },
  "pagination": {
    "page": "number",
    "pageSize": "number"
  }
}
```

#### 台账类型 (tableType)
- `payableSuppliers` - 应付供应商汇总台账
- `payableContracts` - 应付合同汇总台账
- `paymentLedger` - 付款台账
- `subcontractorSettlement` - 分供结算台账
- `costLedger` - 成本台账
- `fundManagement` - 资金整理
- `receiptLedger` - 收款台账
- `externalConfirmation` - 外部确权台账
- `safetyFeeLedger` - 安全费台账
- `internalReconciliation` - 内部对账

### 请求示例
```json
{
  "projectId": "PRJ001",
  "tableType": "payableSuppliers",
  "filters": {
    "dateRange": {
      "startDate": "2024-01-01",
      "endDate": "2024-01-31"
    },
    "status": "正常"
  },
  "pagination": {
    "page": 1,
    "pageSize": 50
  }
}
```

## 4. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 项目不存在 |
| 500 | 服务器内部错误 |

## 5. 注意事项

1. 所有金额字段均以分为单位存储，前端需要转换为元显示
2. 日期格式统一使用 ISO 8601 标准
3. 请求频率限制：每分钟最多100次请求
4. 数据缓存时间：项目基础数据缓存5分钟，明细数据缓存1分钟
5. 大数据量查询建议使用分页参数，单次查询不超过1000条记录

## 6. 版本信息

- API版本：v1.0
- 文档版本：1.0.0
- 最后更新：2024-01-15
