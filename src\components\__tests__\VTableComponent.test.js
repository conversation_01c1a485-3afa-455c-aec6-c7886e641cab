import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import VTableComponent from '../VTableComponent.vue'

// Mock VTable
const mockVTable = {
  ListTable: class {
    constructor(container, options) {
      this.container = container
      this.options = options
      this.records = options.records || []
    }
    setRecords(records) {
      this.records = records
    }
    render() {
      // Mock render
    }
    release() {
      // Mock release
    }
  },
  register: {
    editor: () => {}
  }
}

// Mock vtable-editors
const mockInputEditor = class {}

// Mock modules
vi.mock('@visactor/vtable', () => mockVTable)
vi.mock('@visactor/vtable-editors', () => ({
  InputEditor: mockInputEditor
}))

describe('VTableComponent', () => {
  let wrapper

  const testData = [
    ['姓名', '年龄', '城市'],
    ['张三', 28, '北京'],
    ['李四', 32, '上海']
  ]

  beforeEach(() => {
    wrapper = mount(VTableComponent, {
      props: {
        data: testData,
        width: 600,
        height: 300,
        showFilter: true
      }
    })
  })

  it('renders correctly with basic props', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.vtable-component').exists()).toBe(true)
  })

  it('shows filter panel when showFilter is true', () => {
    expect(wrapper.find('.filter-panel').exists()).toBe(true)
  })

  it('hides filter panel when showFilter is false', async () => {
    await wrapper.setProps({ showFilter: false })
    expect(wrapper.find('.filter-panel').exists()).toBe(false)
  })

  it('generates correct columns from data', () => {
    const vm = wrapper.vm
    expect(vm.columns).toHaveLength(3)
    expect(vm.columns[0].title).toBe('姓名')
    expect(vm.columns[1].title).toBe('年龄')
    expect(vm.columns[2].title).toBe('城市')
  })

  it('generates correct records from data', () => {
    const vm = wrapper.vm
    expect(vm.records).toHaveLength(2)
    expect(vm.records[0]['0']).toBe('张三')
    expect(vm.records[0]['1']).toBe(28)
    expect(vm.records[0]['2']).toBe('北京')
  })

  it('filters data correctly', async () => {
    const vm = wrapper.vm
    
    // Set filter text
    vm.filterText = '张'
    vm.doFilter()
    
    expect(vm.records).toHaveLength(1)
    expect(vm.records[0]['0']).toBe('张三')
  })

  it('resets filter correctly', async () => {
    const vm = wrapper.vm
    
    // Apply filter first
    vm.filterText = '张'
    vm.doFilter()
    expect(vm.records).toHaveLength(1)
    
    // Reset filter
    vm.resetFilter()
    expect(vm.filterText).toBe('')
    expect(vm.filterColumn).toBe('')
    expect(vm.records).toHaveLength(2)
  })

  it('filters by specific column', async () => {
    const vm = wrapper.vm
    
    // Filter by age column (index 1)
    vm.filterColumn = '1'
    vm.filterText = '28'
    vm.doFilter()
    
    expect(vm.records).toHaveLength(1)
    expect(vm.records[0]['1']).toBe(28)
  })

  it('updates when data prop changes', async () => {
    const newData = [
      ['产品', '价格'],
      ['手机', 3999],
      ['电脑', 5999]
    ]
    
    await wrapper.setProps({ data: newData })
    
    const vm = wrapper.vm
    expect(vm.columns).toHaveLength(2)
    expect(vm.columns[0].title).toBe('产品')
    expect(vm.records).toHaveLength(2)
    expect(vm.records[0]['0']).toBe('手机')
  })
})
