import duckdb
from datetime import datetime, date
import random

DB_PATH = "financial_data.duckdb"

def init_sample_data():
    """初始化示例数据"""
    conn = duckdb.connect(DB_PATH)
    
    # 清空现有数据
    tables = [
        'integrated_contract', 'special_reserve', 'master_data', 
        'payment_ledger', 'guarantee_ledger', 'internal_bank_query',
        'internal_reconciliation', 'subcontractor_settlement', 
        'external_confirmation', 'payable_by_supplier', 'cost_ledger',
        'receipt_ledger', 'fund_management'
    ]
    
    for table in tables:
        try:
            conn.execute(f"DELETE FROM {table}")
        except:
            pass  # 表可能不存在
    
    # 插入一体化合同台账示例数据
    integrated_contract_data = [
        ('IC001', '中建一局', '智慧城市项目', 'PRJ2024001', '施工总承包合同', 'HT2024001', '', '土建施工', '城投公司', 'CUS001', '施工合同', ********.0, '9%', ********.0, 5000000.0, ********.0, ********.0, 60.0, ********.0, 0.0),
        ('IC002', '中建二局', '办公楼建设', 'PRJ2024002', '装修工程合同', 'HT2024002', 'HT2023015', '装修施工', '地产公司', 'CUS002', '装修合同', 8000000.0, '6%', 7500000.0, 800000.0, 6000000.0, 7000000.0, 75.0, 1500000.0, 200000.0),
        ('IC003', '中建三局', '基础设施项目', 'PRJ2024003', '设备采购合同', 'HT2024003', '', '设备采购', '设备厂商', 'CUS003', '采购合同', 12000000.0, '13%', 11000000.0, 1200000.0, 8000000.0, 10000000.0, 67.0, 3000000.0, 500000.0)
    ]
    
    for data in integrated_contract_data:
        conn.execute("""
            INSERT INTO integrated_contract VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, data)
    
    # 插入专项储备示例数据
    special_reserve_data = [
        ('PZ2024001', '2024', 'PC001', '智慧城市项目部', '安全生产费提取', date(2024, 1, 15), date(2024, 1, 15), 500000.0, '提取'),
        ('PZ2024002', '2024', 'PC002', '办公楼项目部', '安全生产费使用', date(2024, 1, 20), date(2024, 1, 20), -200000.0, '使用'),
        ('PZ2024003', '2024', 'PC003', '基础设施项目部', '安全生产费提取', date(2024, 1, 25), date(2024, 1, 25), 300000.0, '提取')
    ]
    
    for data in special_reserve_data:
        conn.execute("""
            INSERT INTO special_reserve VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, data)
    
    # 插入主数据示例数据
    master_data_data = [
        ('PRJ2024001', 'PC001', 'ORG001', '智慧城市项目部', '华北区域'),
        ('PRJ2024002', 'PC002', 'ORG001', '办公楼项目部', '华北区域'),
        ('PRJ2024003', 'PC003', 'ORG002', '基础设施项目部', '华东区域')
    ]
    
    for data in master_data_data:
        conn.execute("""
            INSERT INTO master_data VALUES (?, ?, ?, ?, ?)
        """, data)
    
    # 插入付款台账示例数据
    payment_ledger_data = [
        ('2024', date(2024, 1, 15), date(2024, 1, 15), '分包商', 'PZ2024001', 'PC001', '智慧城市项目部', 'SUP001', '北京建筑公司', 'HT2024001', '土建分包合同', '工程款支付', 'DOC001', 5000000.0, 500000.0, 0.0, 4500000.0, 4500000.0, 0.0, ''),
        ('2024', date(2024, 1, 20), date(2024, 1, 20), '材料商', 'PZ2024002', 'PC002', '办公楼项目部', 'SUP002', '上海钢材公司', 'HT2024002', '钢材采购合同', '材料款支付', 'DOC002', 2000000.0, 0.0, 0.0, 2000000.0, 2000000.0, 0.0, ''),
        ('2024', date(2024, 1, 25), date(2024, 1, 25), '设备商', 'PZ2024003', 'PC003', '基础设施项目部', 'SUP003', '广州设备厂', 'HT2024003', '设备采购合同', '设备款支付', 'DOC003', 3000000.0, 0.0, 0.0, 3000000.0, 3000000.0, 0.0, '')
    ]
    
    for data in payment_ledger_data:
        conn.execute("""
            INSERT INTO payment_ledger VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, data)
    
    # 插入保证金台账示例数据
    guarantee_ledger_data = [
        (1.0, 'ORG001', '中建一局', 'PRJ2024001', '智慧城市项目', 'CNY', datetime(2025, 12, 31), '履约保证金', '城投公司', '政府集团', 2500000.0, 0.0, 2500000.0, '张经理'),
        (2.0, 'ORG001', '中建二局', 'PRJ2024002', '办公楼建设', 'CNY', datetime(2024, 12, 31), '质量保证金', '地产公司', '民营集团', 400000.0, 0.0, 400000.0, '李主管'),
        (3.0, 'ORG002', '中建三局', 'PRJ2024003', '基础设施项目', 'CNY', datetime(2025, 6, 30), '投标保证金', '设备厂商', '国有集团', 600000.0, 600000.0, 0.0, '王总监')
    ]
    
    for data in guarantee_ledger_data:
        conn.execute("""
            INSERT INTO guarantee_ledger VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, data)
    
    print("示例数据初始化完成！")
    conn.close()

if __name__ == "__main__":
    init_sample_data()
