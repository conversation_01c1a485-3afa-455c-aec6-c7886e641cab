<template>
  <BaseQueryComponent
    title="主数据"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/master-data'

// 查询字段配置
const queryFields = [
  {
    key: 'projectCode',
    label: '项目编码',
    type: 'text',
    placeholder: '请输入项目编码',
    width: '180px'
  },
  {
    key: 'profitCenter',
    label: '利润中心',
    type: 'text',
    placeholder: '请输入利润中心',
    width: '150px'
  },
  {
    key: 'accountingOrganization',
    label: '项目所属的核算组织',
    type: 'text',
    placeholder: '请输入核算组织',
    width: '200px'
  },
  {
    key: 'profitCenterDesc',
    label: '利润中心描述',
    type: 'text',
    placeholder: '请输入利润中心描述',
    width: '200px'
  },
  {
    key: 'profitCenterGroupDesc',
    label: '利润中心组描述',
    type: 'text',
    placeholder: '请输入利润中心组描述',
    width: '200px'
  }
]
</script>
