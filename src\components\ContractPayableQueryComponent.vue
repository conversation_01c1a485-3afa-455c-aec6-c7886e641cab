<template>
  <BaseQueryComponent
    title="应付合同汇总台账"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// 查询字段配置
const queryFields = [
  {
    key: 'contractNumber',
    label: '合同编号',
    type: 'text',
    placeholder: '请输入合同编号',
    width: '180px'
  },
  {
    key: 'contractName',
    label: '合同名称',
    type: 'text',
    placeholder: '请输入合同名称',
    width: '200px'
  },
  {
    key: 'signDate',
    label: '签订日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'contractAmount',
    label: '合同金额',
    type: 'amount-range'
  },
  {
    key: 'contractType',
    label: '合同类型',
    type: 'select',
    placeholder: '请选择类型',
    width: '150px',
    options: [
      { label: '采购合同', value: 'purchase' },
      { label: '施工合同', value: 'construction' },
      { label: '服务合同', value: 'service' },
      { label: '租赁合同', value: 'lease' }
    ]
  },
  {
    key: 'supplierName',
    label: '供应商',
    type: 'text',
    placeholder: '请输入供应商名称',
    width: '180px'
  }
]

// 模拟数据
const mockData = [
  ['合同编号', '合同名称', '供应商', '合同类型', '合同金额', '已付金额', '未付金额', '签订日期', '执行状态', '负责人'],
  ['HT202401001', '办公设备采购合同', '北京科技有限公司', '采购合同', '150000.00', '100000.00', '50000.00', '2024-01-10', '执行中', '张三'],
  ['HT202401002', '厂房装修施工合同', '上海建筑集团', '施工合同', '800000.00', '500000.00', '300000.00', '2024-01-15', '执行中', '李四'],
  ['HT202401003', '清洁服务合同', '广州保洁公司', '服务合同', '60000.00', '60000.00', '0.00', '2024-01-20', '已完成', '王五'],
  ['HT202401004', '设备租赁合同', '深圳机械租赁', '租赁合同', '120000.00', '80000.00', '40000.00', '2024-01-25', '执行中', '赵六'],
  ['HT202401005', '原材料采购合同', '天津钢铁厂', '采购合同', '500000.00', '200000.00', '300000.00', '2024-02-01', '执行中', '孙七'],
  ['HT202401006', '道路施工合同', '重庆路桥公司', '施工合同', '1200000.00', '600000.00', '600000.00', '2024-02-05', '执行中', '周八'],
  ['HT202401007', '安保服务合同', '成都安保公司', '服务合同', '80000.00', '40000.00', '40000.00', '2024-02-10', '执行中', '吴九'],
  ['HT202401008', '车辆租赁合同', '西安租车公司', '租赁合同', '90000.00', '90000.00', '0.00', '2024-02-15', '已完成', '郑十']
]
</script>
