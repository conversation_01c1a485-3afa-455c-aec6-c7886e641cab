<template>
  <BaseQueryComponent
    title="成本台账"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

defineEmits(['back'])

const queryFields = [
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    placeholder: '请输入项目名称',
    width: '200px'
  },
  {
    key: 'costCategory',
    label: '成本类别',
    type: 'select',
    placeholder: '请选择成本类别',
    width: '150px',
    options: [
      { label: '材料费', value: 'material' },
      { label: '人工费', value: 'labor' },
      { label: '机械费', value: 'machinery' },
      { label: '管理费', value: 'management' },
      { label: '其他费用', value: 'other' }
    ]
  },
  {
    key: 'occurDate',
    label: '发生日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'amount',
    label: '成本金额',
    type: 'amount-range'
  },
  {
    key: 'supplier',
    label: '供应商',
    type: 'text',
    placeholder: '请输入供应商名称',
    width: '180px'
  }
]

const mockData = [
  ['项目名称', '成本类别', '费用明细', '金额', '发生日期', '供应商', '单据号', '经办人', '审批状态', '备注'],
  ['办公楼建设', '材料费', '水泥采购', '50000.00', '2024-01-10', '北京建材公司', 'CB202401001', '张三', '已审批', ''],
  ['厂房改造', '人工费', '施工人员工资', '80000.00', '2024-01-15', '建筑劳务公司', 'CB202401002', '李四', '已审批', ''],
  ['道路硬化', '机械费', '挖掘机租赁', '15000.00', '2024-01-20', '机械租赁公司', 'CB202401003', '王五', '待审批', ''],
  ['室内装修', '材料费', '装修材料', '30000.00', '2024-01-25', '装饰材料商', 'CB202401004', '赵六', '已审批', ''],
  ['设备安装', '管理费', '项目管理费', '20000.00', '2024-02-01', '', 'CB202401005', '孙七', '已审批', '月度管理费']
]
</script>
