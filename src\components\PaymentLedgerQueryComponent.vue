<template>
  <BaseQueryComponent
    title="付款台账"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/payment-ledger'

// 查询字段配置
const queryFields = [
  {
    key: 'fiscalYear',
    label: '财年',
    type: 'select',
    placeholder: '请选择财年',
    width: '120px',
    options: [
      { label: '2024', value: '2024' },
      { label: '2023', value: '2023' },
      { label: '2022', value: '2022' },
      { label: '2021', value: '2021' }
    ]
  },
  {
    key: 'postingDate',
    label: '过帐日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'inputDate',
    label: '输入日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'supplierType',
    label: '供应商类型',
    type: 'select',
    placeholder: '请选择供应商类型',
    width: '150px',
    options: [
      { label: '分包商', value: '分包商' },
      { label: '材料商', value: '材料商' },
      { label: '设备商', value: '设备商' },
      { label: '服务商', value: '服务商' }
    ]
  },
  {
    key: 'voucherNumber',
    label: '凭证编号',
    type: 'text',
    placeholder: '请输入凭证编号',
    width: '180px'
  },
  {
    key: 'profitCenter',
    label: '利润中心',
    type: 'text',
    placeholder: '请输入利润中心',
    width: '150px'
  },
  {
    key: 'profitCenterDesc',
    label: '利润中心描述',
    type: 'text',
    placeholder: '请输入利润中心描述',
    width: '200px'
  },
  {
    key: 'supplier',
    label: '供应商',
    type: 'text',
    placeholder: '请输入供应商编号',
    width: '150px'
  },
  {
    key: 'supplierDesc',
    label: '供应商描述',
    type: 'text',
    placeholder: '请输入供应商名称',
    width: '200px'
  },
  {
    key: 'contract',
    label: '合同',
    type: 'text',
    placeholder: '请输入合同编号',
    width: '150px'
  },
  {
    key: 'contractDesc',
    label: '合同文本描述',
    type: 'text',
    placeholder: '请输入合同描述',
    width: '200px'
  },
  {
    key: 'text',
    label: '文本',
    type: 'text',
    placeholder: '请输入文本内容',
    width: '200px'
  },
  {
    key: 'platformDocumentNumber',
    label: '中台单据号',
    type: 'text',
    placeholder: '请输入中台单据号',
    width: '180px'
  },
  {
    key: 'totalPaymentAmount',
    label: '总付款金额',
    type: 'amount-range'
  },
  {
    key: 'performanceBondDeduction',
    label: '扣履约保证金',
    type: 'amount-range'
  },
  {
    key: 'supplyChainFactoring',
    label: '供应链保理',
    type: 'amount-range'
  },
  {
    key: 'costOffset',
    label: '冲成本',
    type: 'amount-range'
  },
  {
    key: 'thisProfitCenter',
    label: '本利润中心',
    type: 'amount-range'
  },
  {
    key: 'internalBankOrDeposit',
    label: '内行或存款',
    type: 'amount-range'
  },
  {
    key: 'internalBankCustomer',
    label: '内行客商',
    type: 'text',
    placeholder: '请输入内行客商',
    width: '150px'
  }
]
</script>
